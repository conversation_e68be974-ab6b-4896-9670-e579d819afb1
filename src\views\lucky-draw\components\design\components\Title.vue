<template>
  <div :style="{ 'fontSize': getValue(size), color, 'textAlign': textAlign, 'fontWeight': bold, 'padding-left': getValue(padding), 'padding-right': getValue(padding) }">
    <span v-for="item, i in chid" :key="i" class="line-height-[1.5]" :style="{ 'fontSize': getValue(item.size), 'color': item.color, 'fontWeight': item.bold, 'padding-left': getValue(item.padding), 'padding-right': getValue(item.padding) }">
      <template v-if="item.type === '变量' && variableMap">

        {{ item.text! in variableMap ? (variableMap as any)[item.text as any]?.data : '' }}
      </template>
      <template v-else>{{ item.text }}</template>
    </span>
  </div>
</template>

<script setup lang="ts">
import { useInjectionLuckDrawData } from '../useDraw'
import { getValue, type TitleAttrs } from '../utils'

defineProps<TitleAttrs>()

const { variableMap } = useInjectionLuckDrawData()
</script>

<style scoped>

</style>
