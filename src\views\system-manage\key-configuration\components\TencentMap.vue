<!--
 * @Description:
 * @Author: 景 彡
 * @Date: 2024-11-01 11:50:33
 * @LastEditors: 景 彡
-->
<template>
  <div class="h-full p-16px py-32px c-bg">
    <a-spin :spinning="spinner">
      <a-form
        :model="form"
        name="basic"
        autocomplete="off"
        layout="vertical"
        @finish="onFinish"
      >
        <a-form-item
          label="腾讯地图App Key"
          name="key"
          :rules="[{ required: true, message: '腾讯地图App Key' }]"
        >
          <a-input v-model:value="form.key" />
        </a-form-item>
        <a-form-item>
          <a-button type="primary" html-type="submit">保存</a-button>
        </a-form-item>
      </a-form>
    </a-spin>
  </div>
</template>

<script lang='ts' setup>
import * as api from '@/.generated/apis'
import { message } from 'ant-design-vue'

const form = ref({
  key: '',
})

const [,spinner] = useLoading(api.SystemBaseInfo.GetTencentMapKeyAsync, { immediate: true, callback(res) {
  form.value.key = res
} })

async function onFinish() {
  spinner.value = true
  api.SystemBaseInfo.SaveTencentMapKey_PostAsync(form.value).then(() => {
    spinner.value = false
    message.success('保存成功')
  }).catch(() => {
    spinner.value = false
    message.error('保存失败')
  })
}
</script>

<style scoped>

</style>
