<template>
  <div class="h-full">
    <c-pro-table
      ref="tableRef"
      align="center"
      v-bind="queryBind"
      row-key="id"
      immediate operation show-add-btn
      :scroll="{ x: 1440 }"
      :operation-config="{ fixed: 'right', width: '180px' }"
      :row-selection="rowSelection"
      @add-row="onEdit()"
      @del-rows="delRows"
    >
      <template #operation="{ record }">
        <a-button type="link" @click="onEdit(record, true)"> 查看 </a-button>
        <a-button type="link" @click="onEdit(record)"> 编辑 </a-button>
      </template>
    </c-pro-table>
    <a-drawer
      v-model:open="modalState"
      title="添加/编辑经销商信息"
      width="80%"
      ok-text="保存"
      placement="right"
      destroy-on-close
      :mask-closable="false"
      @ok="save()"
    >
      <div class="h-full flex gap-16px max-md:flex-col">
        <div class="relative h-full w-full">
          <div class="map-search">
            <c-input :placeholder="`${model.state}/${model.city}-请输入地点`" @change="mapSearchByKeyWord" />
            <div class="res" :class="{ fold: mapSearchModel.fold }">
              <div v-for="(item, i) in mapSearchModel.results" :key="i" class="mb8px" @click="setSuggestion(item)">
                <h4>{{ item.title }}</h4>
                <p>{{ item.address }}</p>
              </div>
            </div>
            <template v-if="mapSearchModel.results">
              <div v-if="mapSearchModel.fold === false" class="res-fold" @click="mapSearchModel.fold = true">
                <c-icon-up-outlined />
              </div>
              <div v-if="mapSearchModel.fold === true" class="res-fold" @click="mapSearchModel.fold = false">
                <c-icon-down-outlined />
              </div>

              <div class="text-12px c-text-secondary">总数： {{ mapSearchModel.results.length }}</div>
            </template>
          </div>
          <div :id="mapId" class="map h-full w-full" />
        </div>

        <c-pro-form
          ref="formRef"
          v-model:value="model"
          :read-only="readonly"
          :label-col="{ span: 4 }"
          :fields="fields"
          :descriptions="{ bordered: true, column: 1, labelStyle: { width: '120px' } }"
          colon
          class="w-full"
        >
          <template #state>
            <c-cascader
              :read-only="readonly"
              :value="[model.state, model.city].filter(Boolean)"
              :options="cityJson"
              placeholder="请输入省份/城市" :field-names="{ label: 'name', value: 'name' }"
              @change="onSelectCity"
            />
          </template>

          <template #latitude>
            <a-button title="在地图中显示">{{ model.latitude }},{{ model.longitude }}</a-button>
            <div class="c-text-tertiary">从左侧地图选点，会自动填充地址</div>
          </template>
        </c-pro-form>
      </div>
      <template #extra>
        <a-button style="margin-right: 8px" @click="onClose">取消</a-button>
        <a-button type="primary" @click="save()">保存</a-button>
      </template>
    </a-drawer>
  </div>
</template>

<script lang="ts" setup>
import type { Distributors } from '@/api/models'
import type { FormField } from 'ch2-components/types/pro-form/types'
import type { ColumnProps } from 'ch2-components/types/pro-table/types'
import * as api from '@/api/'
import { DistributorsEditModel } from '@/api/models'
import { message } from 'ant-design-vue'
import { useId } from 'vue'
import region from '../../assets/region.json'
import { useInitMap } from './initMap'

definePage({
  meta: {
    title: '经销商管理',
    layoutRoute: {
      meta: {
        layout: 'admin',
        title: '经销商管理',
        local: true,
        icon: 'InboxOutlined',
        order: 16,
      },
    },
  },
})

const cityJson = region.map(v => ({ ...v, children: v.children?.map(p => ({ ...p, children: [] })) }))

const columns = reactive<ColumnProps<Distributors>[]>([
  { dataIndex: 'name', title: '经销商名称', search: { el: 'input' } },
  { dataIndex: 'contact', title: '联系人' },
  { dataIndex: 'phone', title: '电话' },
  { dataIndex: 'city', title: '城市' },
  { dataIndex: 'email', title: '邮箱' },
  // { dataIndex: 'streetAddress', title: '详细地址', width: 200 },
  {
    dataIndex: 'created',
    title: '创建时间',
    dateFormat: true,
    sorter: true,
    width: 160,
  },
  // {
  //   dataIndex: 'modified',
  //   title: '修改时间',
  //   dateFormat: true,
  //   sorter: true,
  //   width: 160,
  // },
])

const { tableRef, queryBind, rowSelection } = useTableSearch(
  'tableRef',
  api.Dealer.Query_PostAsync,
  columns,
)

const { modalState, onEdit, fields, save, model, readonly, onClose } = useEditModal()

const {
  initMask,
  init: initMap,
  mapId,
  mapSearchByRegion,
  mapSearchModel,
  mapSearchByKeyWord,
  setSuggestion,
  getDistrict,
} = useInitMap(useId, { onSuggestionChange: (item) => {
  if (item.address)
    model.value.streetAddress = item.address!
  model.value.latitude = Number(item.location.lat.toFixed(10))
  model.value.longitude = Number(item.location.lng.toFixed(10))
  if (!model.value.name)
    model.value.name = item.title!
  initMask({
    lat: model.value.latitude!,
    lng: model.value.longitude!,
  })
}, onClick: (location) => {
  const data = { id: Date.now().toString() } as any
  data.location = location
  setSuggestion(data)
} })

function delRows(keys: string[]) {
  api.Dealer.Remove_PostAsync(keys).then(() => {
    rowSelection.selectedRowKeys = []
    rowSelection.selectedRows = []
    tableRef.value?.refresh()
    message.success('删除成功')
  })
}

function onSelectCity([state, city]: [string, string]) {
  model.value.state = state
  model.value.city = city
  mapSearchByRegion(model.value.city)
  getDistrict([state, city])
}

function useEditModal() {
  const modalState = ref(false)

  const formRef = useTemplateRef('formRef')

  const fields = reactive<FormField[]>([
    {
      label: '经销商名称',
      el: 'input',
      prop: 'name',
      attrs: { placeholder: '请输入经销商名称' },
      formItem: { required: true },
    },
    {
      label: '联系人',
      el: 'input',
      prop: 'contact',
      attrs: { placeholder: '请输入联系人' },
      required: true,
      formItem: { },
    },
    {
      label: '电话号码',
      el: 'input',
      prop: 'phone',
      attrs: { placeholder: '请输入电话号码' },
      type: 'phone',
      formItem: { },
    },
    {
      label: '营业时间',
      el: 'input',
      prop: 'operatingHours',
      attrs: { placeholder: '营业时间，如周一到周五 08:00-18:00' },
      required: true,
      formItem: { },
    },
    {
      label: '邮箱',
      el: 'input',
      prop: 'email',
      attrs: { placeholder: '请输入邮箱' },
      formItem: {},
    },
    {
      label: '地址',
      el: 'cascader',
      prop: 'state',
      attrs: { placeholder: '请输入省份/城市' },
      required: true,
      formItem: { },
    },
    {
      label: '坐标地点',
      el: 'button',
      prop: 'latitude',
      attrs: { placeholder: '请输入详细地址' },
      required: '从左侧地图选点，会自动填充地址',
      formItem: { },
    },
    {
      label: '详细地址',
      el: 'textarea',
      prop: 'streetAddress',
      attrs: { placeholder: '请输入详细地址' },
      required: true,
      formItem: { },
    },

  ])

  const model = ref(new DistributorsEditModel())

  function onClose() {
    modalState.value = false
  }

  const readonly = ref()

  function onEdit(data?: DistributorsEditModel, readOnly = false) {
    model.value = deepCopy(data) ?? { ...new DistributorsEditModel(), state: '广西壮族自治区', city: '桂林市', email: '' }
    modalState.value = true
    readonly.value = readOnly
    nextTick(async () => {
      await initMap()
      if (model.value.latitude) {
        initMask({
          lat: model.value.latitude!,
          lng: model.value.longitude!,
        })
      }
    })
  }

  const { run: save, spinner } = useLoading(async () => {
    await formRef.value?.validate()
    return (Guid.isNotNull(model.value.id) ? api.Dealer.Update_PostAsync : api.Dealer.Add_PostAsync)(
      model.value,
    ).then(() => {
      modalState.value = false
      tableRef.value?.refresh()
      message.success(model.value.id ? '更新成功' : '创建成功')
    })
  },
  )

  return {
    modalState,
    save,
    spinner,
    onEdit,
    fields,
    model,
    readonly,
    onClose,
  }
}
</script>

<style lang="less" scoped>
.map > div > div div {
  z-index: 1002 !important;
}
.map-search {
  position: absolute;
  left: 20px;
  top: 10px;
  z-index: 1001;
  width: 250px;
  padding: 10px;
  box-shadow:
    0 2px 5px -1px rgba(50, 50, 93, 0.25),
    0 1px 3px -1px rgba(0, 0, 0, 0.3);
  border-radius: 6px;
  background: @colorBgContainer;
  .res {
    margin-top: 10px;
    max-height: calc(100vh - 380px);
    overflow: auto;
    transition: all 0.3s;
    h4 {
      margin: 0;
    }
    p {
      margin-bottom: 4px;
      color: rgb(94, 94, 94);
    }
    > div {
      cursor: pointer;
    }
    > div:hover {
      background: @colorPrimaryBgHover;
    }
  }
  .fold {
    height: 0;
    overflow: hidden;
  }
  .res-fold {
    width: 100%;
    text-align: center;
    cursor: pointer;
  }
  .res-fold:hover {
    background: @colorPrimaryBgHover;
  }
}
:deep(.map-icon) {
  fill: var(--color);
  position: absolute;
  left: 0;
  top: -20px;
  cursor: pointer;
}
</style>
