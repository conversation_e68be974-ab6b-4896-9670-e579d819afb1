<!--
 * @Description:
 * @Author: 景 彡
 * @Date: 2024-11-01 11:50:33
 * @LastEditors: 景 彡
-->
<template>
  <div class="h-full p-16px py-32px c-bg">
    <a-tabs centered tab-position="left">
      <a-tab-pane key="1" tab="微信小程序">
        <h2>微信小程序</h2>

        <Mpweixin />
      </a-tab-pane>
      <a-tab-pane key="2" tab="腾讯地图">
        <h2>腾讯地图Key配置</h2>
        <TencentMap />
      </a-tab-pane>
      <a-tab-pane key="3" tab="微信支付">
        <h2>微信支付配置</h2>
        <WechatPay />
      </a-tab-pane>

      <a-tab-pane key="4" tab="红包密码">
        <h2>红包密码管理</h2>
        <PayKey />
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script lang='ts' setup>
import Mpweixin from './components/mpweixin.vue'
import PayKey from './components/PayKey.vue'
import TencentMap from './components/TencentMap.vue'
import WechatPay from './components/WechatPay.vue'

definePage({
  meta: {
    title: '密钥管理',
    local: true,
  },
})
</script>

<style scoped>

</style>
