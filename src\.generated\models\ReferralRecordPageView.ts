import { ClientUserInfoBaseView } from "./ClientUserInfoBaseView";
import { ReferralRewards } from "./ReferralRewards";
/**推广记录汇总列*/
export class ReferralRecordPageView {
  activityId: GUID = "00000000-0000-0000-0000-000000000000";
  activityName?: string | null | undefined = null;
  clientUserInfo?: ClientUserInfoBaseView | null | undefined = null;
  /**推广人数*/
  number: number = 0;
  /**成功发放奖励数*/
  successes: number = 0;
  /**没成功发放奖励数*/
  failures: number = 0;
  /**已经获得奖励总数*/
  rewards?: ReferralRewards | null | undefined = null;
}
