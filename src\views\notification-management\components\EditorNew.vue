<!--
 * @Description:编辑文章标题和正文内容
 * @Author: 景 彡
 * @Date: 2023-06-12 11:54:16
 * @LastEditors: 景 彡
 * @LastEditTime: 2024-11-29 10:34:30
-->
<template>
  <div class="box">
    <a-affix :offset-top="0">
      <div class="header-toolbar">
        <div class="editor-toolbar justify-center">
          <div ref="toolbar" />
        </div>
      </div>
    </a-affix>
    <div id="content" class="justify-center">
      <div id="editor-container">
        <div id="title-container">
          <a-input v-model:value="noticeData.title!" placeholder="输入通知标题..." />
        </div>
        <div ref="contentRef" class="editor-text-area" />
      </div>
      <a-affix :offset-top="62" :style="{ zIndex: 1, minWidth: '500px', height: 'calc(100vh - 72px)' }">
        <PushArticle ref="saveRef" v-model:form-data="noticeData" />
      </a-affix>
    </div>
  </div>
</template>

<script lang="ts" setup>
import * as api from '@/api'
import * as models from '@/api/models'
import { useEditor } from '@/hooks/useEditor'
import { message } from 'ant-design-vue'
import PushArticle from './PushArticle.vue'

const props = defineProps<{ noticeType: models.NoticeType, id: string }>()

const { toolbar, contentRef, install, editor, onAttrs } = useEditor()

const noticeData = ref<models.Notice>({ ...new models.Notice(), id: props.id, noticeType: props.noticeType })

const saveRef = ref<InstanceType<typeof PushArticle>>()

async function getArticleData(id: string) {
  try {
    noticeData.value = await api.Notices.FindById_GetAsync({ id })
    editor.value?.setData({ content: replaceImgSrc(noticeData.value.content || '') })
  }
  catch (error: any) {
    message.error(error.message)
  }
}

onAttrs.change = () => {
  noticeData.value.content = cleanEditorHtml(editor.value?.getData({ rootName: 'content' }) || '')
}

onMounted(async () => {
  await install()
  Guid.isNotNull(props.id) && getArticleData(props.id.toString())
})
</script>

<style scoped lang="less">
html,
body {
  height: 100%;
  overflow-y: hidden;
  overflow-x: hidden;
  color: #333;
}
.box {
  height: 100%;
  width: 100%;
  margin: auto;
}
.header-toolbar {
  width: 100%;
  border-bottom: 1px solid #e8e8e8;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.12);
  .editor-toolbar {
    background-color: #fcfcfc;
    display: flex;
    :deep(.w-e-drop-panel) {
      z-index: 99999;
    }
    .btn-box {
      margin-left: 10px;
      height: 40px;
      line-height: 40px;
    }
  }
}

#content {
  width: 100%;
  background-color: rgb(245, 245, 245);
  position: relative;
  display: flex;
}

#editor-container {
  max-width: 1200px;
  flex: 1;
  margin: 0 20px;
  background-color: #fff;
  border: 1px solid #e8e8e8;
  box-shadow: 0 2px 10px rgb(0 0 0 / 12%);
  border-radius: 4px;
  :deep(table) {
    min-width: 50px !important;
  }
}

#title-container {
  padding: 20px 0;
  border-bottom: 1px solid #e8e8e8;
  padding: 15px;
}

#title-container input {
  font-size: 30px;
  border: 0;
  outline: none;
  width: 100%;
  line-height: 1;
}

.editor-text-area {
  min-height: 100vh;
  height: fit-content !important;
}
:deep(.ant-input:focus, .ant-input-focused) {
  box-shadow: none;
}
:deep(.w-e-bar-item-group .w-e-bar-item-menus-container) {
  z-index: 22;
}
</style>
