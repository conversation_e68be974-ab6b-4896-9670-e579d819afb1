<!--
 * @Description:
 * @Author: 景 彡
 * @Date: 2024-10-30 10:33:30
 * @LastEditors: 景 彡
-->
<template>
  <a-tabs centered>
    <a-tab-pane key="loginLog">
      <template #tab>
        <span>
          <c-icon-solution-outlined />
          登录日志
        </span>
      </template>
      <c-pro-table
        :columns="loginLogColumns"
        :api="api.ClientUserManage.LoginLogList_GetAsync"
        row-key="id"
        immediate
        serial-number
        align="center"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'userName'">
            <a @click="showDrawer(record.userId)">{{ record.userName }}</a>
          </template>
        </template>
      </c-pro-table>
    </a-tab-pane>
    <a-tab-pane v-for="item in requestLogData" :key="item.name">
      <template #tab>
        <span>
          <c-icon-pull-request-outlined />
          {{ item.name }}
        </span>
      </template>
      <c-pro-table
        :columns="item.columns"
        :api="item.api"
        row-key="id"
        immediate
        serial-number
        align="center"
      >
        <template #bodyCell="{ column, record }">
          <ImageView v-if="column.dataIndex === 'image' && record.image" style="width: 50px; height: 50px; object-fit:cover" :src="(record.image)" />
          <span v-if="column.dataIndex === 'number'">
            <span v-if="record.opType === models.OpTypeQuery.减少">{{ `-${record.number}` }}</span>
            <span v-else-if="record.opType === models.OpTypeQuery.增加">{{ `+${record.number}` }}</span>
            <span v-else>{{ record.number }}</span>
          </span>
          <span v-if="column.dataIndex === 'status'">
            <span v-if="record.status === models.LotteryResultStatus.待发放"> <a-tag color="error">{{ models.LotteryResultStatus[record.status] }}</a-tag>
              <a-popover v-if="record.prizesName === '现金奖励'" title="输入支付密码" placement="topRight" trigger="click">
                <template #content>
                  <div class="w-380px">
                    <a-input-group compact>
                      <a-input v-model:value="psdValue" style="width: calc(100% - 100px)" />
                      <a-button type="primary" @click="onReissue(record)">确认</a-button>
                    </a-input-group>
                  </div>
                </template>
                <a class="mt-2 block">重新发放</a>
              </a-popover>

            </span>
            <a-tag v-else-if="record.status === models.LotteryResultStatus.已完成" color="success">{{ models.LotteryResultStatus[record.status] }}</a-tag>
            <a-tag v-else>{{ models.LotteryResultStatus[record.status] }}</a-tag>
          </span>
        </template>
      </c-pro-table>
    </a-tab-pane>
  </a-tabs>
</template>

<script lang="ts" setup>
import * as api from '@/.generated/apis'
import * as models from '@/api/models'
import { message } from 'ant-design-vue'
import { loginLogColumns, requestLogData } from './columns'

definePage({
  meta: {
    title: '日志管理',
  },
})

function showDrawer(userId: number) {
  console.log('%c [ userId ]-40', 'font-size:13px; background:pink; color:#bf2c9f;', userId)
}

const psdValue = ref('')

async function onReissue(record: any) {
  if (!psdValue.value)
    return message.warning('请输入支付密码')
  try {
    await api.Lotterys.TenPayByLotteryResults_GetAsync({ lotteryResulId: record.id, key: psdValue.value })
    message.success('补发成功')
    record.status = models.LotteryResultStatus.已完成
  }
  catch (error: any) {
    message.error(`补发失败：${error.message}`)
  }
}
</script>
