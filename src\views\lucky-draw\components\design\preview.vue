<template>
  <div ref="rootRef">
    <div ref="contentRef" :style="{ 'transform-origin': '0 0', 'transform': `scale(${scale})`, 'margin-left': marginLeft }" class="relative h-800px w-390px select-none">
      <div class="relative h-800px w-full overflow-hidden">
        <Position v-for="item in previewData.comps" :key="item.id" v-model:attrs="item.position" :read-only="readOnly" :route-option="item.routeOption" :root="contentRef!" :scale="scale" :active="currentId === item.id" @click="() => currentId = item.id">
          <component :is="drawComp[item.compName]" v-bind="item.attrs" />
        </Position>
      </div>
      <div>
        <slot />
      </div>
    </div>
    <a-modal v-model:open="openLuckDrawResult" wrap-class-name="luck-draw-result" :closable="false" :footer="null">
      <div class="relative box-border flex flex-col items-center justify-center p-8">
        <div class="text-8 c-#FEE89A">
          {{ prizeInfo?.name }}
        </div>
        <div class="my flex flex-col items-center justify-center rounded">
          <div class="w-30 bg-#fff p2">
            <img class="h-100% w-100% rounded-2" :src="joinImageById(prizeInfo.images!)" alt="">
          </div>
          <div class="mt2 flex flex-col items-center c-#fff">
            <div>{{ prizeInfo.name }}</div>

            <div v-if="prizeInfo.type !== LotteryPrizesType.谢谢惠顾" class="text-6 c-#FEE89A">{{ prizeInfo.type === LotteryPrizesType.现金红包 ? prizeInfo.number / 100 : prizeInfo.number }}{{ lotteryPrizesTypeUnit(prizeInfo.type) }}</div>
          </div>
        </div>
        <div class="w-full rounded-7.5 bg-#FCEDBE py text-center" @click="navigateTo(`/pages/luck-draw/lottery-records?id=${miniData.id}`)">
          查看奖品
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { LotteryPrizes, LotteryPrizesType } from '@/api/models'
import IMAGE from './components/IMAGE.vue'
import JGGDraw from './components/JGGDraw.vue'
import LPDraw from './components/LPDraw.vue'
import NOTICE from './components/NOTICE.vue'
import Placeholder from './components/Placeholder.vue'
import Position from './components/Position.vue'
import Title from './components/Title.vue'
import { useProvideLuckDrawData } from './useDraw'
import { DrawComp, type ILuckyDrawDesignConfig, lotteryPrizesTypeUnit } from './utils'

const props = defineProps<{ design?: boolean, prizesData?: LotteryPrizes[] | null | undefined, token?: string, baseUrl?: string, id?: string, readOnly?: boolean }>()

const emit = defineEmits<{
  getData: [data: ILuckyDrawDesignConfig ]
}>()

const previewData = defineModel<ILuckyDrawDesignConfig>('previewData', { default: { title: '', bgColor: '', comps: [] } })

const openLuckDrawResult = ref(false)

const prizeInfo = ref(new LotteryPrizes())

function navigateTo(url: string) {
  wx.miniProgram.navigateTo({
    url,
  })
}

const { data: miniData, getData, joinImageById } = useProvideLuckDrawData(
  props.design,
  props.prizesData,
  props.token,
  props.baseUrl,
  props.id,
  (data) => {
    prizeInfo.value = data
    openLuckDrawResult.value = true
  },
)

watch(miniData, () => {
  if (!props.design) {
    previewData.value = JSON.parse(miniData.value?.template || '{}')
    if (previewData.value && wx) {
      const data = deepCopy(previewData.value)
      data.comps = []
      document.title = '抽奖赢大礼'
      wx.miniProgram.postMessage({ data })
    }
    emit('getData', previewData.value)
  }
}, { immediate: true })

onMounted(async () => {
  await getData()
})

const currentId = defineModel<string>('currentId')

const rootRef = useTemplateRef('rootRef')

const contentRef = useTemplateRef('contentRef')

const drawComp = {
  [DrawComp.九宫格]: JGGDraw,
  [DrawComp.轮盘]: LPDraw,
  [DrawComp.图片]: IMAGE,
  [DrawComp.标题]: Title,
  [DrawComp.公告]: NOTICE,
  [DrawComp.占位符]: Placeholder,
}

const scale = ref(1)

const marginLeft = ref('')

useResizeObserver(rootRef, (entries) => {
  const entry = entries[0]!
  const { width } = entry.contentRect

  if (width > 560) {
    marginLeft.value = `${width / 2 - 560 / 2}px`
  }
  scale.value = Math.min(width, 560) / 390
})
</script>

<style lang="less">
.luck-draw-result {
  .ant-modal-content {
    background-color: #e33656;
  }
}
</style>
