import { CarouselType } from "./CarouselType";
import { CarouselStatus } from "./CarouselStatus";
/**小程序轮播*/
export class CarouselConfig {
  /**图片id*/
  image?: string | null | undefined = null;
  value1?: string | null | undefined = null;
  /**无长度限制，可以保存富文本内容*/
  value2?: string | null | undefined = null;
  /**类型*/
  type: CarouselType = 0;
  /**状态*/
  status: CarouselStatus = 0;
  /**排序*/
  sort: number = 0;
  /**次数上限*/
  count: number = 0;
  /**转发积分*/
  points: number = 0;
  /**软删除标记*/
  softDeleted: boolean = false;
  /**软删除者*/
  deletedBy?: string | null | undefined = null;
  /**软删除时间*/
  deleted?: Dayjs | null | undefined = null;
  /**创建时间*/
  created: Dayjs = dayjs();
  /**修改时间*/
  modified?: Dayjs | null | undefined = null;
  /**创建者*/
  createdBy?: string | null | undefined = null;
  /**修改者*/
  modifiedBy?: string | null | undefined = null;
  /**主键*/
  id: GUID = "00000000-0000-0000-0000-000000000000";
}
