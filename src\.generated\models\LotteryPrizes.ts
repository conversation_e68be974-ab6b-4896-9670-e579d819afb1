import { LotteryPrizesType } from "./LotteryPrizesType";
/**抽奖奖品表*/
export class LotteryPrizes {
  /**奖品*/
  name?: string | null | undefined = null;
  /**现金 or 积分 or 抽奖次数*/
  number: number = 0;
  /**图片（Path*/
  images?: string | null | undefined = null;
  /**描述*/
  description?: string | null | undefined = null;
  /**中奖概率 （大小：0~1 ）*/
  probability: number = 0;
  /**奖品类型*/
  type: LotteryPrizesType = 0;
  /**软删除标记*/
  softDeleted: boolean = false;
  /**软删除者*/
  deletedBy?: string | null | undefined = null;
  /**软删除时间*/
  deleted?: Dayjs | null | undefined = null;
  /**创建时间*/
  created: Dayjs = dayjs();
  /**修改时间*/
  modified?: Dayjs | null | undefined = null;
  /**创建者*/
  createdBy?: string | null | undefined = null;
  /**修改者*/
  modifiedBy?: string | null | undefined = null;
  /**主键*/
  id: GUID = "00000000-0000-0000-0000-000000000000";
}
