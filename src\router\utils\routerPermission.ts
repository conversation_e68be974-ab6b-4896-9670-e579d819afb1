/*
 * @Description:
 * @Author: luckymiaow
 * @Date: 2022-09-13 11:25:15
 * @LastEditors: luckymiaow
 */
import type { Router } from 'vue-router'
import { useAppStore, useUserStore } from '@/stores'
import { weChatLogin } from '@/views/login/hook/useLogin'
import { HomeRoute, LoginRoute } from './useRouteConfig'
import { whiteListTest } from './whiteList'

export default (
  router: Router,
) => {
  router.beforeEach(async (to, _, next) => {
    document.title = ''
    useAppStore().routerLoading = true
    const isWL = await weChatLogin(router)
    const UserModule = useUserStore()
    if (whiteListTest(to.path)) {
      next()
    }
    else if (!isWL && UserModule.token) {
      if (['/', LoginRoute.path].includes(to.path))
        next({ path: HomeRoute.path })
      else
        next()
    }
    else {
      next({ path: '/logoutReload', query: { redirect: to.path } })
    }
  })

  router.afterEach((to) => {
    if (to.meta.title) {
      if (to.meta.title === '首页')
        document.title = `${import.meta.env.VITE_APP_TITLE}`
      else
        document.title = `${to.meta.title} | ${import.meta.env.VITE_APP_TITLE}`
    }
    useAppStore().routerLoading = false
  })
}
