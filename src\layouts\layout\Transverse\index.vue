<template>
  <DefineSider v-slot="{ onCollapse, mode }">
    <a-layout-sider v-model:collapsed="globalStore.themeConfig.isCollapse" class="transverse-sider" collapsible :theme="theme">
      <!-- <div class="logo" :class="{ 'logo-dark': theme === 'dark' }" >{{ projectName }}</div> -->
      <div class="suspension-collapsed max-md:hidden" @click="onCollapse">
        <c-icon-left-outlined :class="{ isCollapse }" />
      </div>
      <Menu :mode="mode" />
    </a-layout-sider>
  </DefineSider>

  <a-drawer v-model:open="openSider" :body-style="{ padding: 0 }" placement="left" :closable="false" destroy-on-close width="200px">
    <ReuseSider v-if="openSider" mode="vertical" :on-collapse="() => openSider = !openSider" />
  </a-drawer>

  <a-layout class="layout-box" :class="{ 'layout-dark': globalStore.themeConfig.isDark }">
    <ReuseSider class="max-md:hidden" mode="inline" :on-collapse="() => globalStore.themeConfig.isCollapse = !isCollapse" />
    <LayoutContainer class="layout-container" :class="{ 'layout-container-collapsed': isCollapse }">
      <a-layout-header class="header">
        <a-row class="header-tools">
          <a-col :span="12">
            <div class="menu-title">
              <c-icon-menu-outlined
                class="menu-icon"
                @click="() => { openSider = true; globalStore.themeConfig.isCollapse = false }"
              />
              <LeftHeader class="menu-name" />
            </div>
          </a-col>
          <a-col :span="12">
            <RightHeader />
          </a-col>
        </a-row>
        <Tags v-if="globalStore.themeConfig.tabs && !$route.meta?.hiddenBreadcrumb" class="router-tag" />
      </a-layout-header>
    </LayoutContainer>
  </a-layout>
</template>

<script setup lang="ts">
import { useAppStore } from '@/stores'
import { createReusableTemplate, useWindowSize } from '@vueuse/core'
import { computed, ref, watch } from 'vue'
import LeftHeader from '../components/Header/LeftHeader.vue'
import RightHeader from '../components/Header/RightHeader.vue'
import LayoutContainer from '../components/LayoutContainer.vue'
import Menu from '../components/Menu/index.vue'
import Tags from '../components/Tabs/index.vue'

const globalStore = useAppStore()

const isCollapse = computed(() => globalStore.themeConfig.isCollapse)

const theme = computed(() => (globalStore.themeConfig.darkMenu ? 'dark' : 'light'))

const [DefineSider, ReuseSider] = createReusableTemplate<{ onCollapse: (...args: any) => any, mode?: 'vertical' | 'inline' }>()

const openSider = ref(false)

const { width } = useWindowSize()

watch(width, () => {
  openSider.value = false
})
</script>

<style scoped lang="less">
@mobile-breakpoint: 768px;
@mobile-mini-breakpoint: 468px;

:deep(.ant-layout-sider-light .ant-layout-sider-trigger) {
  box-shadow:
    rgba(60, 64, 67, 0.3) 0px 0px 0px 0px,
    rgba(60, 64, 67, 0.15) 0px 0px 5px 0px;
}

.layout-box {
  min-height: 100vh;

  .transverse-sider {
    .ant-menu-light {
      background: none;
    }

    .suspension-collapsed {
      position: absolute;
      inset-block-start: 108px;
      z-index: 101;
      width: 24px;
      height: 24px;
      text-align: center;
      border-radius: 40px;
      inset-inline-end: -13px;
      transition: transform 0.3s;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      color: rgba(0, 0, 0, 0.25);
      background-color: #ffffff;

      box-shadow:
        0 2px 8px -2px rgba(0, 0, 0, 0.2),
        0 1px 4px -1px rgba(25, 15, 15, 0.3),
        0 0 1px 0 rgba(0, 0, 0, 0.4);

      &:hover {
        color: rgba(0, 0, 0, 0.65);
        box-shadow:
          0 4px 16px -4px rgba(0, 0, 0, 0.2),
          0 1px 4px -1px rgba(25, 15, 15, 0.3),
          0 0 1px 0 rgba(0, 0, 0, 0.4);
      }

      > span {
        transition: all 0.5s;
      }

      .isCollapse {
        transform: rotate(-180deg);
      }
    }
  }

  @media screen and (max-width: @mobile-breakpoint) {
    .transverse-sider {
      width: 0 !important;
      min-width: 0 !important;
      overflow: hidden;
    }

    .layout-container {
      margin-left: 0 !important;
    }

    .menu-icon {
      display: block !important;
    }
  }

  @media screen and (max-width: @mobile-mini-breakpoint) {
    .menu-name {
      display: none;
    }
  }

  .header {
    background: @colorBgContainer;
    padding: 0;
    height: fit-content;
    transition: all 0.5s;

    .header-tools {
      border: #f1f1f1;
      border-bottom: 1px solid @colorBorderSecondary;
      color: #888;
      height: 64px;

      .menu-title {
        display: flex;
        align-items: center;
        height: 100%;

        .menu-icon {
          display: none;
          margin-left: calc(@size * 1px);
          font-size: calc(@fontSizeXL * 1px);
          color: @colorText;
        }
      }
    }
  }

  .site-layout .site-layout-background {
    background: @colorBgLayout;
  }
}
</style>
