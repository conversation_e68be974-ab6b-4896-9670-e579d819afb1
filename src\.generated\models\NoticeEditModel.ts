import { NoticeScope } from "./NoticeScope";
import { NoticeType } from "./NoticeType";
export class NoticeEditModel {
  /**通知范围*/
  scope: NoticeScope = 0;
  /**接收用户*/
  clientUserIds?: string[] | null | undefined = [];
  /**标题*/
  title?: string | null | undefined = null;
  /**内容（富文本）*/
  content?: string | null | undefined = null;
  /**通知类型*/
  noticeType: NoticeType = 0;
  /**主键*/
  id: GUID = "00000000-0000-0000-0000-000000000000";
}
