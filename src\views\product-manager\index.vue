<template>
  <div class="h-full">
    <c-pro-table
      ref="tableRef"
      align="center"
      v-bind="queryBind"
      row-key="id"
      immediate show-add-btn operation
      :row-selection="rowSelection"
      :scroll="{ x: 1440 }"
      :operation-config="{ fixed: 'right', width: '130px' }"
      @add-row="onEdit()"
      @del-rows="delRows"
    >
      <template #bodyCell="{ column, record }">
        <ImageView
          v-if="column.dataIndex === 'image' && record.image" style="width: 50px; height: 50px; object-fit:cover" :src="record.image"
        />
        <span v-if="column.dataIndex === 'price'">{{ record.price / 100 }}</span>
      </template>
      <template #operation="{ record }">
        <div class="flex flex-col">
          <a-button class="text-left" title="设置积分红包后才可以作为生产二维码" type="link" @click="pointsSetting(record)">
            积分红包
          </a-button>

          <a-dropdown>
            <a-button class="text-left" type="link" @click.prevent>
              更多操作
              <c-icon-down-outlined />
            </a-button>
            <template #overlay>
              <a-menu>
                <a-menu-item>
                  <a @click="onEdit(record, true)"> 查看 </a>
                </a-menu-item>
                <a-menu-divider />
                <a-menu-item>
                  <a @click="onEdit(record)"> 编辑 </a>
                </a-menu-item>
                <a-menu-item>
                  <a-popconfirm title="是否确认删除当前产品" @confirm="delRows([record.id])">
                    <a class="!c-error"> 删除产品 </a>
                  </a-popconfirm>
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </div>
      </template>
    </c-pro-table>

    <ProductInfo v-model:open="modalState" v-model:value="model" :read-only="readonly" title="添加/编辑产品信息" @change="onClose" />

    <a-drawer v-model:open="productPointEditOpen" title="设置积分" width="600px" destroy-on-close :mask-closable="false" placement="right" @close="productPointEditClose">
      <a-form
        :model="productPointEditModel"
        name="basic"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 16 }"
        autocomplete="off"
      >
        <a-form-item
          label="积分随机"
          name="randomPoints"
        >
          <c-boolean-select v-model:value="productPointEditModel.randomPoints" allow-clear @change="randomPointsChange" />
        </a-form-item>
        <template v-if="productPointEditModel.randomPoints">
          <a-form-item
            label="最小积分"
            name="minPoints"
            :rules="[{ required: true, message: '请输入最小积分!' }]"
          >
            <a-input-number v-model:value="productPointEditModel.minPoints" style="width: 100%" />
          </a-form-item>
          <a-form-item
            label="最大积分"
            name="maxPoints"
            :rules="[{ required: true, message: '请输入最大积分!' }]"
          >
            <a-input-number v-model:value="productPointEditModel.maxPoints" style="width: 100%" />
          </a-form-item>
        </template>
        <a-form-item
          v-if="!productPointEditModel.randomPoints"
          label="积分"
          name="points"
          :rules="[{ required: true, message: '请输入!' }]"
        >
          <a-input-number v-model:value="productPointEditModel.points" />
        </a-form-item>

        <a-form-item
          label="返现金额随机"
          name="randomCashback"
        >
          <c-boolean-select v-model:value="productPointEditModel.randomCashback" allow-clear @change="randomCashbackChange" />
        </a-form-item>

        <a-form-item
          v-if="!productPointEditModel.randomCashback"
          label="返现金额"
          name="cashback"
          :rules="[{ required: true, message: '请输入!' }]"
        >
          <c-input-money v-model:value="productPointEditModel.cashback!" addon-before="￥" addon-after="元" :step="10" :unit="UnitType.元" />
          <!-- <a-input-number v-model:value="productPointEditModel.cashback" /> -->
        </a-form-item>

        <template v-if="productPointEditModel.randomCashback">
          <a-form-item
            label="最小返现"
            name="minCashback"
            :rules="[{ required: true, message: '请输入最小返现金额!' }]"
          >
            <c-input-money v-model:value="productPointEditModel.minCashback!" addon-before="￥" addon-after="元" :step="10" :unit="UnitType.元" />
          </a-form-item>
          <a-form-item
            label="最大返现"
            name="maxPoints"
            :rules="[{ required: true, message: '请输入最大返现金额!' }]"
          >
            <c-input-money v-model:value="productPointEditModel.maxCashback!" addon-before="￥" addon-after="元" :step="10" :unit="UnitType.元" />
          </a-form-item>
        </template>
        <a-form-item
          label="抽奖次数"
          name="maxPoints"
          :rules="[{ required: true, message: '请输入抽奖次数!' }]"
        >
          <a-input-number v-model:value="productPointEditModel.draws" style="width: 100%" />
        </a-form-item>
      </a-form>

      <template #extra>
        <a-button style="margin-right: 8px" @click="productPointEditClose">取消</a-button>
        <a-button type="primary" @click="productPointEditSave()">保存</a-button>
      </template>
    </a-drawer>
  </div>
</template>

<script lang="ts" setup>
import type { Product } from '@/api/models'
import type { ColumnProps } from 'ch2-components/types/pro-table/types'
import * as api from '@/api/'
import { ProductEditModel, ProductPointEditModel, ProductStatus, QueryType } from '@/api/models'
import { message } from 'ant-design-vue'
import { UnitType } from 'ch2-components/lib/input-money/types'
import ProductInfo from './components/ProductInfo.vue'

definePage({
  meta: {
    title: '产品管理',
    layoutRoute: {
      meta: { layout: 'admin', title: '产品管理', local: true, icon: 'InboxOutlined', order: 9 },
    },
  },
})

const columns = reactive<ColumnProps<Product>[]>([
  { dataIndex: 'productionNumber', title: '产品编号' },
  { dataIndex: 'name', title: '产品名称', search: { el: 'input' }, align: 'left', queryType: QueryType.Include },
  // { dataIndex: 'description', title: '描述', align: 'left' },
  { dataIndex: 'image', title: '图片' },
  { dataIndex: 'price', title: '价格(元)' },
  { dataIndex: 'status', title: '产品状态', enum: ProductStatus, search: { el: 'enum-select', attrs: { placeholder: '请选择产品状态', enum: ProductStatus } }, align: 'left', queryType: QueryType.Eq },
  { dataIndex: ['type', 'name'], title: '产品类型' },
  { dataIndex: 'typeId', title: '产品类型', search: { el: 'select', attrs: {
    placeholder: '请输入产品类型',
    api: (...agrs) => tableSearch(api.Products.QueryType_PostAsync, agrs),
    page: true,
    fieldNames: { label: 'name', value: 'id' },
    showSearch: true,
  } }, isShow: false, queryType: QueryType.Eq },
  { dataIndex: 'created', title: '创建时间', dateFormat: true, sorter: true, width: 160 },
  { dataIndex: 'modified', title: '修改时间', dateFormat: true, sorter: true, width: 160 },
])

const { tableRef, queryBind, rowSelection } = useTableSearch('tableRef', api.Products.Query_PostAsync, columns)

const { modalState, onEdit, model, readonly, onClose } = useEditModal()

function delRows(keys: string[]) {
  api.Products.Remove_PostAsync(keys).then(() => {
    rowSelection.selectedRowKeys = []
    rowSelection.selectedRows = []
    tableRef.value?.refresh()
    message.success('删除成功')
  })
}

function useEditModal() {
  const modalState = ref(false)

  const model = ref(new ProductEditModel())

  const readonly = ref()

  function onClose() {
    tableRef.value?.refresh()
    modalState.value = false
  }

  function onEdit(data?: ProductEditModel, readOnly = false) {
    model.value = deepCopy(data) ?? new ProductEditModel()
    modalState.value = true
    readonly.value = readOnly
  }

  return { modalState, onEdit, model, readonly, onClose }
}

const { productPointEditModel, productPointEditOpen, productPointEditSave, productPointEditClose, pointsSetting } = useProductPointModal()

function useProductPointModal() {
  const productPointEditModel = ref(new ProductPointEditModel())

  const productPointEditOpen = ref(false)

  async function pointsSetting(record: Product) {
    await api.Products.GetPointAsync({ id: record.id }).then((res) => {
      if (res) {
        productPointEditModel.value = res
      }
      else {
        productPointEditModel.value.id = record.id
      }
      productPointEditOpen.value = true
    })
  }

  async function productPointEditSave() {
    await api.Products.SetPoint_PostAsync(productPointEditModel.value)
    productPointEditOpen.value = false
    message.success('设置成功')
  }

  function productPointEditClose() {
    productPointEditOpen.value = false
    productPointEditModel.value = new ProductPointEditModel()
  }

  return { productPointEditModel, productPointEditOpen, productPointEditClose, productPointEditSave, pointsSetting }
}

function randomCashbackChange(e: boolean) {
  if (e) {
    productPointEditModel.value.cashback = 0
  }
  else {
    productPointEditModel.value.minCashback = 0
    productPointEditModel.value.maxCashback = 0
  }
}

function randomPointsChange(e: boolean) {
  if (e) {
    productPointEditModel.value.points = 0
  }
  else {
    productPointEditModel.value.minPoints = 0
    productPointEditModel.value.maxPoints = 0
  }
}
</script>

<style scoped lang="less">
.coverBox {
  position: relative;

  .icon {
    position: absolute;
    right: 0;
    top: 0;
    background-color: #ccc;
    width: 20px;
    height: 20px;
    line-height: 20px;
    cursor: pointer;
  }
}
</style>
