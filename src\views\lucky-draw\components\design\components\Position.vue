<template>
  <div
    ref="el"
    :style="{
      width: getValue(attrs.w),
      height: getValue(attrs.h),
      position: 'absolute',
      left: getValue(attrs.x),
      top: getValue(attrs.y),
      zIndex: attrs.z,
      minWidth: '2px',
      minHeight: '2px',
    }"
    @click="onBinEvent()"
  >
    <div v-if="luckDrawConfig?.design && !readOnly" :class="{ 'bg-transparent !b-none': !active }" class="mask absolute left-0 top-0 z-999 h-full w-full cursor-pointer b-(1px orange solid)" />
    <slot />
  </div>
</template>

<script setup lang="ts">
import { useInjectionLuckDrawData } from '../useDraw'
import { getValue, type Position, type RouterUni } from '../utils'

const props = defineProps<{ root: HTMLDivElement, active?: boolean, scale: number, routeOption?: typeof RouterUni[keyof typeof RouterUni], readOnly?: boolean }>()

const { luckDrawConfig, variableMap } = useInjectionLuckDrawData()

const attrs = defineModel<Position>('attrs', { default: () => ({}) })

const el = useTemplateRef('el')

const { x, y } = useDraggable(el, {
  initialValue: { x: attrs.value.x, y: attrs.value.y },
  disabled: !luckDrawConfig?.design || props.readOnly,
  onMove: () => {
    const rootRect = props.root.getBoundingClientRect()

    attrs.value.x = (x.value - rootRect.left) / props.scale
    attrs.value.y = (y.value - rootRect.top) / props.scale
  },
})

function onBinEvent() {
  if (!luckDrawConfig?.design && props.routeOption && wx) {
    let url: any = props.routeOption.startsWith('/') ? props.routeOption : `/${props.routeOption}`
    Object.entries(variableMap?.value || {}).forEach(([key, value]) => {
      const regex = new RegExp(key, 'g')
      url = url.replace(regex, value?.data)
    })
    wx.miniProgram.navigateTo({
      url,
    })
  }
}
</script>

<style scoped>

</style>
