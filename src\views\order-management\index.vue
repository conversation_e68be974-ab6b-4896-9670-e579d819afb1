<!--
 * @Description:
 * @Author: 景 彡
 * @Date: 2024-10-30 10:31:45
 * @LastEditors: 景 彡
-->
<template>
  <c-pro-table
    ref="proTableRef" v-model:search-form="searchForm" :columns="columns" :row-selection="rowSelection"
    :search-fields="[{ prop: 'start', method: 'GET' }, { prop: 'end', method: 'GET' }, { prop: 'status', method: 'GET' }]"
    :api="api.PointsOrders.GetPointsOrderPage_PostAsync" row-key="id" immediate
    :serial-number="false"
    align="center" :scroll="{ x: 1440 }"
  >
    <template #header>
      <a-radio-group v-model:value="searchForm.status" @change="radioChange">
        <a-radio-button :value="null">全部</a-radio-button>
        <a-radio-button
          v-for="status in enumToObject(PointsOrderStatus)"
          :key="status.value"
          :value="status.value"
        >
          <span class="flex">
            {{ status.label }}
            <a-badge class="ml-4px" :count="statuCount[status.value]" />
          </span>
        </a-radio-button>
      </a-radio-group>
    </template>
    <template #bodyCell="{ column, record }">
      <span v-if="column.dataIndex === 'id'" class="text-12px c-text-secondary"> {{ record.id.replace(/-/g, '') }} </span>

      <ImageView v-if="column.dataIndex === 'image' && record.image" style="width: 50px; height: 50px; object-fit:cover" :src="(record.image)" />
      <span v-if="column.dataIndex === 'address'">{{ [record.address?.name, record.address?.phoneNumber, record.address?.addressLine1, record.address?.addressLine2].filter(Boolean).join(' ') }}</span>
      <span v-if="column.dataIndex === 'userInfo'">{{ `${record.userInfo?.name}  ${record.userInfo?.phoneNumber}` }}</span>
      <span v-if="column.dataIndex === 'status'">

        <div v-if="[PointsOrderStatus.待发货, PointsOrderStatus.仓库处理].includes(record.status) ">
          <a-popover title="输入快递单号">
            <template #content>
              <div class="flex items-center">
                <a-input v-model:value="trackingNumber" style="width: 280px" placeholder="快递单号" />
                <a-button type="primary" class="ml-8px" @click="deliverGoods(record)"> 发货 </a-button>
              </div>
            </template>
            <a-button type="link"> 发货 </a-button>
          </a-popover>

          <a-tag v-if="record.status === PointsOrderStatus.仓库处理" color="volcano">
            仓库处理
          </a-tag>
          <a-button v-else danger type="link" @click="cancelOrder(record)"> 取消订单 </a-button>
        </div>
        <template v-else>
          <a-tag
            :color="record.status === PointsOrderStatus.已取消 ? 'default' : 'purple'"
          >
            {{ PointsOrderStatus[record.status] }}
          </a-tag>
        </template>
      </span>
    </template>
    <template #toolButton>
      <a-button type="primary" @click="orderExport">未发货订单导出</a-button>
      <a-upload
        :before-upload="beforeUpload"
        :show-upload-list="false"
        accept=".xlsx"
      >
        <a-button type="primary" ghost @click="importLogistics">导入快递信息</a-button>
      </a-upload>

      <a-button :disabled="rowSelection.selectedRowKeys.length <= 0" danger @click="cancelOrder(null)">批量取消订单</a-button>
    </template>
  </c-pro-table>

  <a-modal v-model:open="open" width="600px" :mask-closable="false" :footer="false">
    <template #title>
      <span class="c-#ff0000">以下快递信息导入失败</span>
    </template>
    <div class="mb-16px text-right">
      <a-button type="primary" @click="downLoadError">
        <template #icon>
          <c-icon-download-outlined />
        </template>
        导出
      </a-button>
    </div>
    <a-table :columns="errrorColumns" :data-source="errrorList" bordered />
  </a-modal>
</template>

<script lang="tsx" setup>
import type { PointsOrderViewModel } from '@/api/models'
import type {
  ColumnProps,
} from 'ch2-components/types/pro-table/types'
import * as api from '@/.generated/apis'
import { PointsOrderStatus } from '@/api/models'
import { message, Modal } from 'ant-design-vue'
import { RangePicker } from 'ch2-components'
import * as XLSX from 'xlsx'

definePage({
  meta: {
    title: '订单管理',
    icon: 'AuditOutlined',
    layoutRoute: {
      meta: {
        layout: 'admin',
        title: '订单管理',
        local: true,
        icon: 'AuditOutlined',
        order: 12,
      },
    },
  },
})

const proTableRef = useTemplateRef('proTableRef')

const trackingNumber = ref('')// 快递单号

const searchForm = ref({
  guidString: '',
  keyword: '',
  userword: '',
  start: null,
  end: null,
  status: null,
})

const columns = reactive<ColumnProps<PointsOrderViewModel>[]>([
  {
    dataIndex: 'id',
    title: '订单编号',
    key: 'id',
    edit: false,
    width: 190,
  },
  {
    dataIndex: 'guidString',
    title: '订单编号',
    key: 'guidString',
    edit: false,
    isShow: false,
    search: { el: 'input', method: 'GET', attrs: {} },
  },
  {
    dataIndex: 'keyword',
    title: '商品信息',
    key: 'keyword',
    edit: false,
    isShow: false,
    search: { el: 'input', method: 'GET', attrs: {} },
  },
  {
    dataIndex: 'userword',
    title: '用户信息',
    key: 'userword',
    edit: false,
    isShow: false,
    search: { el: 'input', method: 'GET', attrs: {} },
  },
  {
    dataIndex: 'start',
    title: '下单时间',
    key: 'start',
    edit: false,
    isShow: false,
    search: {
      el: () => <RangePicker v-model:startTime={searchForm.value.start} v-model:endTime={searchForm.value.end} />,
      method: 'GET',
      modelFiled: false,
      attrs: {
        style: { width: '100%' },
      },
    },
  },

  {
    dataIndex: 'pointsCommodityId',
    title: '兑换商品ID',
    key: 'pointsCommodityId',
    edit: false,
    isShow: false,
  },
  {
    dataIndex: 'title',
    title: '商品信息',
    key: 'title',
    width: 200,
    ellipsis: true,
  },
  {
    dataIndex: 'image',
    title: '图片',
    key: 'image',
  },
  {
    dataIndex: 'userInfo',
    title: '用户信息',
    key: 'userInfo',
  },
  {
    dataIndex: 'pointsTotal',
    title: '积分扣除',
    key: 'pointsTotal',
    edit: false,
  },
  {
    dataIndex: 'time',
    title: '兑换时间',
    key: 'time',
    dateFormat: true,
  },
  {
    dataIndex: 'address',
    title: '收货地',
    key: 'address',
    edit: false,
    fixed: 'right',
  },
  {
    dataIndex: 'trackingNumber',
    title: '快递单号',
    key: 'trackingNumber',
    edit: false,
    fixed: 'right',
  },
  {
    dataIndex: 'status',
    title: '订单状态',
    key: 'status',
    fixed: 'right',
    // enum: PointsOrderStatus,
  },
])

const rowSelection = ref({
  selectedRowKeys: [],
  selectedRows: [],
  onChange(selectedRowKeys: never[], selectedRows: never[]) {
    rowSelection.value.selectedRowKeys = selectedRowKeys
    rowSelection.value.selectedRows = selectedRows
  },
})

async function cancelOrder(record: PointsOrderViewModel | null) {
  let ids: string[] = []
  if (record)
    ids = [record.id]
  else ids = rowSelection.value.selectedRowKeys
  const res = await api.PointsOrders.CancelOrders_PostAsync(ids)
  if (res) {
    message.success('取消订单成功')
    rowSelection.value.selectedRowKeys = []
    proTableRef.value?.refresh()
  }
  else {
    message.warn('取消订单失败')
  }
}

// 发货
async function deliverGoods(record: PointsOrderViewModel) {
  if (!trackingNumber.value)
    return message.warn('请输入快递单号')
  const res = await api.PointsOrders.SendPackage_GetAsync({ id: record.id, trackingNumber: trackingNumber.value })
  if (res) {
    message.success('发货操作成功')
    getStatuCount()
    proTableRef.value?.refresh()
    trackingNumber.value = ''
  }
  else {
    message.warn('发货操作失败')
  }
}

async function orderExport() {
  useDownload(() => api.PointsOrders.ExportPendingShipments_GetAsync({ responseType: 'blob' }), `${dateTime(new Date(), 'YYYY-MM-DD')}-未发货订单`)
}

function importLogistics() {

}

const open = ref(false)

const errrorList = ref([])

const errrorColumns = ref([
  {
    title: '快递单号',
    dataIndex: '快递单号',
  },
  {
    title: '订单编号',
    dataIndex: '订单编号',
  },
])

async function beforeUpload(file: File) {
  console.log('%c [ file ]-302', 'font-size:13px; background:pink; color:#bf2c9f;', file)
  const data = new FormData()
  data.append('file', file)
  const res = await api.PointsOrders.ImportTrackingNumber_PostAsync(data)
  if (res && res.length > 0) {
    errrorList.value = res
    open.value = true
  }
  else {
    proTableRef.value?.refresh()
    getStatuCount()
    Modal.success({
      title: '快递信息导入成功',
    })
  }
}

function downLoadError() {
  const worksheet = XLSX.utils.json_to_sheet(errrorList.value)
  const workbook = XLSX.utils.book_new()
  XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1')

  // 导出 Excel 文件
  XLSX.writeFile(workbook, '导入失败的快递信息.xlsx')
}

function radioChange() {
  nextTick(() => {
    proTableRef.value?.search()
  })
}

const statuCount = ref<Record<PointsOrderStatus, number>>({
  [PointsOrderStatus.待发货]: 0,
  [PointsOrderStatus.仓库处理]: 0,
  [PointsOrderStatus.已发货]: 0,
  [PointsOrderStatus.已取消]: 0,
})

async function getStatuCount() {
  const data = await api.PointsOrders.GetPointsOrderNumberByStatusAsync()
  statuCount.value = data.reduce((a, b) => {
    a[b.key] = b.value
    return a
  }, { } as Record<PointsOrderStatus, number>)
}

onMounted(() => {
  getStatuCount()
})
</script>
