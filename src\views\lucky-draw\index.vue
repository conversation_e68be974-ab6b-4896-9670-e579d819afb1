<!--
 * @Description:抽奖活动
 * @Author: 景 彡
 * @Date: 2024-10-30 10:01:14
 * @LastEditors: 景 彡
-->
<template>
  <div class="h-full">
    <c-pro-table
      ref="proTableRef"
      v-model:search-form="searchForm"
      :columns="columns"
      :api="api.Lotterys.LotteryActivityPage_PostAsync"
      row-key="id"
      align="center"

      immediate serial-number operation
    >
      <template #header>
        <a-button type="primary" @click="onEdit()">
          <template #icon>
            <c-icon-plus-outlined />
          </template>
          新增抽奖活动
        </a-button>
      </template>
      <template #operation="{ record }">
        <a-button :disabled="record.status !== LotteryActivityState.未启用" type="link" @click="onEdit(record)"> 编辑 </a-button>
        <a-dropdown>
          <a class="ant-dropdown-link" @click.prevent>
            更多操作
            <DownOutlined />
          </a>
          <template #overlay>
            <a-menu>
              <a-menu-item>
                <a @click="onEdit(record, true)"> 查看详情 </a>
              </a-menu-item>
              <a-menu-divider />
              <a-menu-item>
                <a @click="operation('抽奖记录', record.id)"> 抽奖记录 </a>
              </a-menu-item>
              <!-- <a-menu-item>
                <a style="color: red"> 删除活动 </a>
              </a-menu-item> -->
              <a-menu-divider />
              <a-menu-item>
                <a @click="operation('查看奖品', record.id)">查看奖品</a>
              </a-menu-item>
              <template v-if="dayjs().isBefore(dayjs(record.end)) && !(dayjs().isBetween(dayjs(record.start), dayjs(record.end), null, '[]') && record.status === 1) ">
                <a-menu-divider />
                <a-menu-item>
                  <a @click="operation('立即上架', record.id)">立即上架</a>
                </a-menu-item>
              </template>
            </a-menu>
          </template>
        </a-dropdown>
      </template>
      <template #bodyCell="{ column, record }">
        <ImageView v-if="column.dataIndex === 'images' && record.images" style="width: 50px; height: 50px; object-fit:cover" :src="(record.images)" />
        <template v-if="column.dataIndex === 'status'">
          <a-popconfirm
            title="是否要启用活动"
            ok-text="确定"
            cancel-text="取消"
            @confirm="changeStatus(record.id, 1)"
          >
            <a-tag v-if="record.status === 0" class="cursor-pointer" color="#f50">未启用</a-tag>
          </a-popconfirm>
          <a-popconfirm
            title="是否要停用活动"
            ok-text="确定"
            cancel-text="取消"
            @confirm="changeStatus(record.id, 0)"
          >
            <a-tag v-if="record.status === 1" class="cursor-pointer" color="#87d068">已启用</a-tag>
            <p v-if="record.status === 1 && !dayjs().isBetween(dayjs(record.start), dayjs(record.end), null, '[]')" class="m0 p0 text-12px c-gray-500 !mt4px">活动未开始</p>
          </a-popconfirm>
          <a-popconfirm
            title="是否要恢复活动"
            ok-text="确定"
            cancel-text="取消"
            @confirm="changeStatus(record.id, 1)"
          >
            <a-tag v-if="record.status === 2" class="cursor-pointer" color="#cd201f">已删除</a-tag>
          </a-popconfirm>
        </template>
        <template v-if="column.dataIndex === 'prizes'">
          <a @click="operation('查看奖品', record.id)">{{
            record.prizes!.length
          }}</a>
        </template>
      </template>
    </c-pro-table>
    <a-drawer
      v-model:open="modalState"
      title="添加/编辑活动信息"
      width="600px"
      :mask-closable="false"
      placement="right"
      destroy-on-close
    >
      <c-pro-form
        ref="formRef"
        v-model:value="model"
        :read-only="readonly"
        :label-col="{ span: 4 }"
        :fields="fields"
        :descriptions="{
          column: 1,
          bordered: true,
          labelStyle: { width: '120px' },
        }"
      >
        <template #template>
          <Design v-model:value="model.template" :read-only="readonly" :prizes-data="model.prizes" />
        </template>
        <template #end>
          <c-range-picker
            v-model:start-time="model!.start!"
            v-model:end-time="model!.end!"
            :read-only="readonly"
            show-time
          />
        </template>>
        <template #images>
          <div v-if="model.images" class="coverBox size-140px overflow-hidden">
            <ImageView
              :src="(model.images)"
              alt="avatar"
              :preview="true"
              :del-ico="true"
              style="height: 140px; width: 140px; object-fit: cover"
              @del-image="() => (model.images = '')"
            />
          </div>
          <a-button
            v-else
            type="dashed"
            block
            class="size-25"
            @click="avatarUpload"
          >
            <template #icon>
              <c-icon-plus-outlined />
            </template>
            上传
          </a-button>
        </template>>
      </c-pro-form>

      <template #extra>
        <a-button style="margin-right: 8px" @click="onClose">取消</a-button>
        <a-button type="primary" @click="save()">保存</a-button>
      </template>
    </a-drawer>
  </div>
  <Prizes ref="prizesRef" />
  <!-- <AddPrizes ref="addPrizesRef" /> -->
  <Record ref="recordRef" />
</template>

<script lang="ts" setup>
import type { Rule } from 'ant-design-vue/lib/form'
import type { FormField } from 'ch2-components/types/pro-form/types'
import type { ColumnProps } from 'ch2-components/types/pro-table/types'
import * as api from '@/.generated/apis'
import {
  FileType,
  LotteryActivity,
  LotteryActivityState,
  LotteryActivityType,
} from '@/api/models'
import { DownOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import dayjs from 'dayjs'
import isBetween from 'dayjs/plugin/isBetween'
import Design from './components/design/index.vue'
import Prizes from './components/prizes.vue'
import Record from './components/record.vue'

dayjs.extend(isBetween)

definePage({
  meta: {
    title: '抽奖活动管理',
    icon: 'GiftOutlined',
    layoutRoute: {
      meta: {
        layout: 'admin',
        title: '抽奖活动管理',
        local: true,
        icon: 'GiftOutlined',
        order: 11,
      },
    },
  },
})

const proTableRef = useTemplateRef('proTableRef')

const prizesRef = ref()
// const addPrizesRef = ref()
const recordRef = ref()

const searchForm = ref({
  keyword: '',
  guaranteedDraws: null,
  phoneNumber: '',
  state: '',
})

const columns = ref<ColumnProps<LotteryActivity>[]>([
  {
    dataIndex: 'type',
    title: '活动类型',
    key: 'type',
    enum: LotteryActivityType,
  },
  {
    dataIndex: 'name',
    title: '活动名称',
    key: 'name',
  },
  { dataIndex: 'images', title: '图片' },
  {
    dataIndex: 'description',
    title: '描述',
    key: 'name',
  },
  {
    dataIndex: 'initialDrawAttempts',
    title: '抽奖次数',
    key: 'initialDrawAttempts',

  },
  {
    dataIndex: 'prizes',
    title: '奖品数量',
    key: 'name',
  },
  {
    dataIndex: 'start',
    title: '活动时间',
    key: 'start',
    bodyCell: ({ record }) =>
      h('span', `${dateTime(record.start)}  ~ ${dateTime(record.end)}`),
  },
  {
    dataIndex: 'status',
    title: '状态',
    key: 'status',
  },
])

const { modalState, onEdit, fields, save, model, readonly, onClose }
  = useEditModal()

function avatarUpload() {
  useFileMangerModal(
    (files) => {
      model.value.images = files[0]?.id
    },
    { multiple: false, immediateReturn: true, menu: [FileType.图片] },
  )
}

/**
 * 更改活动状态
 */
function changeStatus(id: string, status: any) {
  api.Lotterys.SaveStatus_GetAsync({ actiyity: id, status }).then((res) => {
    if (res) {
      message.success('更改成功')
      proTableRef.value?.refresh()
    }
  })
}

function operation(action: string, id: number | any) {
  if (action === '查看奖品') {
    prizesRef.value.acceptParams({
      visible: true,
      type: action,
      currentId: id as unknown as number,
      title: action,
      callback: proTableRef.value!.refresh,
    })
  }
  // else if (action === '新增奖品') {
  //   addPrizesRef.value.acceptParams({
  //     visible: true,
  //     type: action,
  //     currentId: id as unknown as number,
  //     title: action,
  //     callback: proTableRef.value!.refresh(),
  //   })
  // }
  else if (action === '抽奖记录') {
    recordRef.value.acceptParams({
      visible: true,
      type: action,
      currentId: id as unknown as number,
      title: action,
      callback: proTableRef.value!.refresh,
    })
  }
  else if (action === '立即上架') {
    api.Lotterys.AvailableNow_GetAsync({ actiyityId: id }).then((res) => {
      if (res) {
        message.success('更改成功')
        proTableRef.value?.refresh()
      }
    })
  }
}

const formRef = useTemplateRef('formRef')

function useEditModal() {
  const modalState = ref(false)

  const model = ref(new LotteryActivity())

  const readonly = ref()

  function checkTime(_rule: Rule) {
    if (model.value.start && model.value.end)
      return Promise.resolve()
    return Promise.reject(new Error('请输入活动时间'))
  }

  const fields = reactive<FormField[]>([
    {
      label: '活动类型',
      el: 'enum-select',
      prop: 'type',
      required: true,
      attrs: { placeholder: '请输入活动类型', enum: LotteryActivityType },
      formItem: {},
    },
    {
      label: '活动名称',
      el: 'input',
      prop: 'name',
      required: true,
      attrs: { placeholder: '请输入活动名称' },
      formItem: {},
    },
    { label: '图片', el: 'input', prop: 'images', required: true, attrs: { placeholder: '请输入图片（Path' }, formItem: {} },
    {
      label: '活动时间',
      el: 'date-picker',
      prop: 'end',
      attrs: { placeholder: '请选择活动时间' },
      formItem: {
        rules: [{ validator: checkTime, trigger: 'change' }],
      },
    },

    {
      label: '保底次数',
      el: 'input-number',
      prop: 'guaranteedDraws',
      attrs: {
        placeholder: '请输入保底次数：在此次数内用户必须中保底奖品',
        style: { width: '100%' },
      },
      formItem: {},
    },
    {
      label: '抽奖次数',
      el: 'input-number',
      prop: 'initialDrawAttempts',
      attrs: {
        placeholder: '请输入抽奖次数：此活动初始赠送的抽奖次数',
        style: { width: '100%' },
      },
      formItem: {
        help: '表示该活动赠送给每个用户的抽奖次数。',
      },
      isShow: () => !Guid.isNotNull(model.value.id),
    },
    {
      label: '描述',
      el: 'textarea',
      prop: 'description',
      required: true,
      attrs: { placeholder: '请输入描述' },
      formItem: {},
    },
    {
      label: '抽奖页面设计',
      el: 'textarea',
      prop: 'template',
      required: true,
      attrs: { placeholder: '请输入描述' },
      formItem: {},
    },
  ])

  function onClose() {
    modalState.value = false
  }

  function onEdit(data?: LotteryActivity, readOnly = false) {
    model.value = deepCopy(data) ?? new LotteryActivity()
    modalState.value = true
    nextTick(formRef.value?.baseEl?.baseEl?.clearValidate)
    readonly.value = readOnly
  }

  const { run: submit, spinner } = useLoading(() =>
    api.Lotterys.Save_PostAsync(model.value).then(() => {
      modalState.value = false
      proTableRef.value?.refresh()
      message.success(model.value.id ? '更新成功' : '创建成功')
    }),
  )

  async function save() {
    await formRef.value?.validate()
    submit()
  }

  return {
    modalState,
    save,
    spinner,
    onEdit,
    fields,
    model,
    readonly,
    onClose,
  }
}
</script>

<style scoped lang="less">
.coverBox {
  position: relative;

  .icon {
    position: absolute;
    right: 0;
    top: 0;
    background-color: #ccc;
    width: 20px;
    height: 20px;
    line-height: 20px;
    cursor: pointer;
  }
}
</style>
