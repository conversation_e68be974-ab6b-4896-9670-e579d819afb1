<!--
 * @Description:
 * @Author: 景 彡
 * @Date: 2024-10-30 10:28:55
 * @LastEditors: 景 彡
-->
<template>
  <c-modal v-model:open="prizesRef.visible" width="90%" :title="prizesRef.title">
    <a-alert class="mb4px" :message="`奖品总和概率必须等于100%，目前总和概率为${totalProbability}%`" :type="totalProbability === 100 ? 'info' : 'warning'" show-icon />
    <c-pro-table :columns="columns" :data-source="prizes" row-key="id" bordered show-tool-btn :pagination="false">
      <template #header>
        <div :class="{ 'bg-warning': prizes.length !== 8 }" class="w-full b-(1px border-secondary solid) rounded-4px px8px">
          <a-button class="!m0 !p0" :disabled="prizes.length === 8" type="link" @click="onEdit(null)">
            <template #icon>
              <c-icon-plus-outlined />
            </template>
            新增奖品
          </a-button>
          <span class="ml16px c-text-secondary">
            {{ prizes.length === 8 ? '' : `奖品个数应为8个，当前奖品数为 ${prizes.length} 个，` }}
            {{ `当前保底次数为 ${activity.guaranteedDraws} 次` }}
          </span>
        </div>
      </template>
      <template #bodyCell="{ column, text, record, index }">
        <template v-if="column.dataIndex === 'name'">
          <a>{{ text }}</a>
        </template>
        <template v-if="column.dataIndex === 'probability'">
          <div v-if="currentRecord.id === record.id && isEditProbability" class="flex items-center justify-center">
            <InputPercentage v-model:value="currentRecord.probability" />
            <a-button size="small" type="link" @click="onSaveProbability(record)">保存</a-button>
            <a-button size="small" type="link" danger @click="onEditProbabilityCancel">取消</a-button>
          </div>

          <a v-else title="点击编辑" @click="onEditProbability(record)">{{ new Decimal(text).times(100).toNumber() }}%</a>
        </template>
        <template v-if="column.dataIndex === 'number'">
          <span v-if="record.type === LotteryPrizesType.抽奖次数">{{ text }}次</span>
          <span v-else-if="record.type === LotteryPrizesType.现金红包">{{ text / 100 }}元</span>
          <span v-else-if="record.type === LotteryPrizesType.积分商品">{{ text }}个</span>
          <span v-else-if="record.type === LotteryPrizesType.积分奖励">{{ text }}积分</span>
          <span v-else-if="record.type === LotteryPrizesType.谢谢惠顾">{{ text }}</span>
        </template>
        <template v-if="column.dataIndex === 'guaranteed'">
          <a-tag v-if="record.id === activity.guaranteedPrizeId" color="#87d068">是</a-tag>
        </template>
        <template v-if="column.dataIndex === 'images'">
          <ImageView
            :src="(record.images)" alt="avatar" :preview="true" :del-ico="true"
            style="height: 60px; width:60px ; object-fit:cover"
          />
        </template>
        <template v-if="column.dataIndex === 'operation'">
          <a v-if="record.id !== activity.guaranteedPrizeId" @click="setGuaranteedPrize(record.id)">设置保底</a>
          <a v-if="record.id === activity.guaranteedPrizeId" style="color:red" @click="setGuaranteedPrize()">取消保底</a>
          <a-divider type="vertical" />
          <a @click="onEdit(record)">编辑</a>
          <a-divider type="vertical" />
          <a style="color: red;" @click="removePrizes(record.id)">删除</a>
          <template v-if="prizes.length < 8">
            <a-divider type="vertical" />
            <a @click="copyPrizes(record, index)">复制</a>
          </template>
        </template>
      </template>
    </c-pro-table>
    <template #footer>
      <a-button @click="prizesRef.visible = false">关闭</a-button>
    </template>
  </c-modal>
  <AddPrizes ref="addPrizesRef" v-model:value="currentRecord" :actiyity-id="prizesRef.currentId" @success="onSuccess" />
</template>

<script lang='ts' setup>
import type { LotteryPrizes } from '@/api/models'
import type { ColumnProps } from 'ch2-components/types/pro-table/types'
import * as api from '@/.generated/apis'
import { LotteryActivity, LotteryPrizesEdit, LotteryPrizesType } from '@/api/models'
import { message } from 'ant-design-vue'
import { Decimal } from 'decimal.js'
import AddPrizes from './addPrizes.vue'

const prizesRef = ref({
  visible: false,
  title: '',
  currentId: '0',
  type: '',
  callback: () => { },
})

watch(() => prizesRef.value.visible, () => {
  if (!prizesRef.value.visible) {
    prizesRef.value.callback()
  }
})

const activity = ref<LotteryActivity>(new LotteryActivity())

const prizes = ref<LotteryPrizes[]>([])

const totalProbability = computed(() =>
  new Decimal(prizes.value.reduce((sum, prize) => sum + prize.probability, 0)).times(100).toNumber(),
)

const columns = reactive<ColumnProps<LotteryPrizes>[]>(
  [
    {
      title: '名称',
      dataIndex: 'name',
      align: 'center',
    },
    {
      title: '现金/积分/抽奖次数',
      dataIndex: 'number',
      align: 'center',
      width: '180px',
    },
    {
      title: '中奖概率',
      dataIndex: 'probability',
      align: 'center',
    },
    {
      title: '奖品类型',
      dataIndex: 'type',
      align: 'center',
      enum: LotteryPrizesType,
    },

    {
      title: '图片',
      dataIndex: 'images',
      align: 'center',
      width: '80px',
    },
    {
      title: '保底',
      dataIndex: 'guaranteed',
      align: 'center',
    },
    {
      title: '描述',
      dataIndex: 'description',
      align: 'center',
    },
    {
      title: '操作',
      dataIndex: 'operation',
      align: 'center',
    },
  ],
)

const addPrizesRef = useTemplateRef('addPrizesRef')

async function removePrizes(id: string) {
  await api.Lotterys.RemovePrizeByActiyityId_GetAsync({ actiyityId: prizesRef.value.currentId, prizesId: id }).then((res) => {
    if (res) {
      message.success('移除成功')
      getData(prizesRef.value.currentId)
    }
  })
}

async function copyPrizes(record: LotteryPrizes, index: number) {
  const obj: LotteryPrizes = { ...record, id: Guid.empty }
  api.Lotterys.SavePrizeByActiyityId_PostAsync({ actiyityId: activity.value.id }, obj).then((res) => {
    if (res) {
      prizes.value.splice(index + 1, 0, res)
      message.success('复制成功')
    }
  }).catch((error) => {
    message.error(`复制失败${error?.message}`)
  })
}

const currentRecord = ref(new LotteryPrizesEdit())

function onEdit(record: LotteryPrizes | null) {
  if (record) {
    const obj = { ...record }
    viewModelToEditModel(obj, currentRecord.value)
  }
  else {
    currentRecord.value = new LotteryPrizesEdit()
  }
  addPrizesRef.value!.open = true
}

const isEditProbability = ref(false)

function onEditProbability(record: LotteryPrizes) {
  currentRecord.value = { ...record }
  isEditProbability.value = true
}

function onEditProbabilityCancel() {
  isEditProbability.value = false
}

function onSaveProbability(record: LotteryPrizes) {
  api.Lotterys.SavePrizeByActiyityId_PostAsync({ actiyityId: activity.value.id }, currentRecord.value).then((res) => {
    if (res) {
      message.success('保存成功')
      record.probability = res.probability
      isEditProbability.value = false
    }
  }).catch((error) => {
    message.error(`保存失败${error?.message}`)
  })
}

async function setGuaranteedPrize(id?: string) {
  await api.Lotterys.SetGuaranteedPrize_GetAsync({ actiyityId: activity.value.id, prizeId: id! }).then((res) => {
    if (res) {
      message.success('设置成功')
      getData(prizesRef.value.currentId)
    }
  })
}

/**
 *  父组件传值
 * @param params 参数
 */
async function acceptParams(params: typeof prizesRef.value): Promise<void> {
  prizesRef.value = params
  prizesRef.value.visible = params.visible
  if (params.currentId) {
    getData(params.currentId)
  }
}

async function getData(id: string) {
  await api.Lotterys.FingOneById_GetAsync({ actiyityId: id }).then((res) => {
    if (res) {
      activity.value = res
      prizes.value = res.prizes as LotteryPrizes[]
    }
  })
}

function onSuccess() {
  getData(prizesRef.value.currentId)
}

defineExpose({
  acceptParams,
})
</script>

<style scoped></style>
