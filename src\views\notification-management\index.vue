<!--
 * @Description:
 * @Author: 景 彡
 * @Date: 2024-11-04 17:23:07
 * @LastEditors: 景 彡
-->
<template>
  <div class="h-full p-16px c-bg">
    <div class="mb-16px">
      <c-form layout="inline">
        <a-row :gutter="[16, 16]">
          <a-col :span="6">
            <c-form-item label="关键字">
              <a-input
                v-model:value="searchForm.keyword"
                placeholder="请输入关键字"
                allow-clear
              />
            </c-form-item>
          </a-col>
          <a-col :span="8">
            <a-button type="primary" class="search-btn" @click="onSearch">
              <c-icon-search-outlined />搜索
            </a-button>
            <a-button class="search-btn mx-16px" @click="reSetForm()">
              <c-icon-sync-outlined />重置
            </a-button>
            <a-button type="primary" @click="onEdit('00000000-0000-0000-0000-000000000000')">
              <c-icon-plus-outlined />新增通知
            </a-button>
          </a-col>
        </a-row>
      </c-form>
    </div>
    <a-tabs v-model:active-key="activeKey" centered :destroy-inactive-tab-pane="true" @change="tabsChange">
      <a-tab-pane :key="NoticeType.系统通知" tab="系统通知"><List ref="cListRef" :search-form="searchForm" @on-edit="onEdit" /></a-tab-pane>
      <a-tab-pane :key="NoticeType.积分奖励" tab="积分奖励"><List ref="cListRef" :search-form="searchForm" @on-edit="onEdit" /></a-tab-pane>
      <a-tab-pane :key="NoticeType.活动福利" tab="活动福利"><List ref="cListRef" :search-form="searchForm" @on-edit="onEdit" /></a-tab-pane>
    </a-tabs>
  </div>

  <a-drawer v-model:open="open" width="90%" :title="`新增通知-${NoticeType[searchForm.noticeType]}`" placement="right">
    <template #extra>
      <a-button style="margin-right: 8px" @click="open = false">关闭</a-button>
    </template>
    <EditorNew v-if="open" :id="currentId" :notice-type="searchForm.noticeType" @close="open = false" />
  </a-drawer>
</template>

<script lang='ts' setup>
import { NoticeType } from '@/api/models'
import { Form } from 'ant-design-vue'
import EditorNew from './components/EditorNew.vue'
import List from './components/List.vue'

definePage({
  meta: {
    title: '通知管理',
    layoutRoute: {
      meta: { layout: 'admin', title: '通知管理', local: true, icon: 'SoundOutlined', order: 7 },
    },
  },
})

const searchForm = ref({
  keyword: '',
  noticeType: NoticeType.系统通知,

})

const activeKey = ref(NoticeType.系统通知)

const { resetFields } = Form.useForm(searchForm)

const cListRef = ref() // 编辑资讯列表ref

function reSetForm() {
  resetFields()
  cListRef.value.reSetForm()
}

// const router = useRouter()

const open = ref(false)

const currentId = ref('00000000-0000-0000-0000-000000000000')
// 操作资讯信息
async function onEdit(id: string) {
  currentId.value = id
  open.value = true
}

provide('saveSucces', () => {
  cListRef.value.reSetForm()
  open.value = false
})

function tabsChange(key: NoticeType) {
  searchForm.value.noticeType = key
  cListRef.value.search()
}

function onSearch() {
  cListRef.value.search()
}
</script>

<style scoped lang="less">
:deep(.ant-row) {
  width: 100%;
}
</style>
