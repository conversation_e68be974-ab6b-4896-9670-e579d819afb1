import antfu from '@antfu/eslint-config'

const config = antfu({
  unocss: true,
  formatters: true,
  ignores: [
    'tsconfig.json',
    'package.json',
    'src/.generated/',
    'config/',
    'api.config.ts',
    'README.md',
    '.npmrc',
  ],
  rules: {
    'vue/valid-template-root': 0,
    'ts/consistent-type-definitions': 0,
    'no-console': 0,
    'no-sequences': 0,
    'vue/no-useless-template-attributes': 'off',
    'vue/singleline-html-element-content-newline': 0,
    'vue/block-order': [
      'error',
      {
        order: ['template', 'script', 'style'],
      },
    ],
    'no-unused-vars': [
      'error',
      // we are only using this rule to check for unused arguments since TS
      // catches unused variables but not args.
      { varsIgnorePattern: '.*', args: 'none' },
    ],
  },
})

export default config
