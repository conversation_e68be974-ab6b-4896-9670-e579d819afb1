import type { ColumnProps } from 'ch2-components/lib/pro-table/types'
import * as api from '@/.generated/apis'
import * as models from '@/api/models'
import { reactive } from 'vue'

const loginLogColumns = reactive<ColumnProps<models.UserLoginLog>[]>([
  { dataIndex: 'provider', title: '站点', key: 'provider' },
  {
    dataIndex: 'result',
    title: '状态',
    key: 'result',
    enum: models.LoginResultLog,
  },
  {
    dataIndex: 'userName',
    title: '用户',
    key: 'userName',
  },
  {
    dataIndex: 'loginTime',
    title: '登录时间',
    key: 'loginTime',
    dateFormat: true,
  },
  { dataIndex: 'ipAddress4', title: 'IP地址V4', key: 'ipAddress4' },
  { dataIndex: 'ipAddress6', title: 'IP地址V6', key: 'ipAddress6' },
  {
    dataIndex: 'userAgent',
    title: '浏览器标志',
    key: 'userAgent',
  },
])

const productColumn = reactive<ColumnProps<models.ProductActivationLog>[]>([
  {
    title: 'Id',
    dataIndex: 'clientUserId',
  },
  {
    title: '用户名',
    dataIndex: 'userName',
  },
  {
    title: '电话',
    dataIndex: 'userPhoneNumber',
  },
  {
    title: '激活时间',
    dataIndex: 'activationTime',
  },
  {
    title: '激活批次',
    dataIndex: 'productBatch',
  },
  {
    title: '批次时间',
    dataIndex: 'productBatchTime',
  },
])

const cashbackColumn = reactive<ColumnProps<models.CashbackRecord>[]>([
  {
    title: '姓名',
    dataIndex: 'userName',
  },
  {
    title: '手机号',
    dataIndex: 'userPhoneNumber',
  },
  {
    title: '说明',
    dataIndex: 'description',
  },
  {
    title: '推广人数',
    dataIndex: 'data',
  },
  {
    title: '获取时间',
    dataIndex: 'time',
  },
])

const pointsColumn = reactive<ColumnProps<models.PointsTransactionView>[]>([
  {
    title: '用户名',
    dataIndex: 'userName',
  },
  {
    title: '手机号',
    dataIndex: 'userPhoneNumber',
  },
  {
    title: 'type',
    dataIndex: 'opType',
    enum: models.OpTypeQuery,
  },
  {
    title: '积分',
    dataIndex: 'number',
  },
  {
    title: '来源',
    dataIndex: 'source',
    enum: models.RecordSource,
  },
  {
    title: '获取时间',
    dataIndex: 'time',
  },
])
const referralColumn = reactive<ColumnProps<models.ReferralRecordPageView>[]>([
  {
    title: '推广名称',
    dataIndex: 'activityName',
  },
  {
    title: '用户',
    dataIndex: ['clientUserInfo', 'name'],
  },
  {
    title: '手机号',
    dataIndex: ['clientUserInfo', 'phoneNumber'],
  },
  {
    title: '推广人数',
    dataIndex: 'number',
  },
  {
    title: '失败奖励',
    dataIndex: 'failures',
  },
])

const lotteryColumn = reactive<ColumnProps<models.LotteryResultPageView>[]>([
  {
    title: '姓名',
    dataIndex: 'name',
    align: 'center',
  },
  {
    title: '手机号',
    dataIndex: 'phoneNumber',
    align: 'center',
    width: '100px',
  },
  {
    title: '奖品名称',
    dataIndex: 'prizesName',
    align: 'center',
  },
  {
    title: '图片',
    dataIndex: 'images',
    align: 'center',
    width: '80px',
  },
  {
    title: '中奖时间',
    dataIndex: 'time',
    align: 'center',
    dateFormat: true,
  },

  {
    title: '状态',
    dataIndex: 'status',
    align: 'center',
  },
])

const pointsOrderColumn = reactive<ColumnProps<models.PointsOrderViewModel>[]>([
  {
    dataIndex: 'id',
    title: '订单编号',
    key: 'id',
    width: 190,
  },
  {
    dataIndex: 'guidString',
    title: '订单编号',
    key: 'guidString',
    isShow: false,
  },

  {
    dataIndex: 'start',
    title: '下单时间',
    key: 'start',
    isShow: false,
  },

  {
    dataIndex: 'pointsCommodityId',
    title: '兑换商品ID',
    key: 'pointsCommodityId',
    isShow: false,
  },
  {
    dataIndex: 'title',
    title: '商品信息',
    key: 'title',
    width: 200,
    ellipsis: true,
  },
  {
    dataIndex: 'image',
    title: '图片',
    key: 'image',
    width: 120,
  },
  {
    dataIndex: ['userInfo', 'name'],
    title: '用户信息',
    key: 'userInfo',
  },
  {
    dataIndex: 'pointsTotal',
    title: '积分扣除',
    key: 'pointsTotal',
  },
  {
    dataIndex: 'time',
    title: '兑换时间',
    key: 'time',
    dateFormat: true,
  },
])

const requestLogData = [
  {
    name: '扫码日志',
    api: api.ClientUserActivityLogs.ProductActivation_PostAsync,
    columns: productColumn,
  },
  {
    name: '返现日志',
    api: api.ClientUserActivityLogs.CashbackRecord_PostAsync,
    columns: cashbackColumn,
  },
  {
    name: '积分日志',
    api: api.ClientUserActivityLogs.PointsTransactionLogs_PostAsync,
    columns: pointsColumn,
  },
  {
    name: '抽奖日志',
    api: api.ClientUserActivityLogs.LotteryResultLogs_GetAsync,
    columns: lotteryColumn,
  },
  {
    name: '积分兑换日志',
    api: api.PointsOrders.GetPointsOrderPage_PostAsync,
    columns: pointsOrderColumn,
  },
  {
    name: '推广日志',
    api: api.Referrals.GetReferralRecordPageView_PostAsync,
    columns: referralColumn,
  },
]

export { loginLogColumns, requestLogData }
