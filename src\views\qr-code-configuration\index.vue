<!--
 * @Description: 二维码生产配置
 * @Author: 景 彡
 * @Date: 2024-10-30 14:50:42
 * @LastEditors: 景 彡
-->
<template>
  <div class="h-full">
    <c-pro-table
      ref="tableRef" align="center" v-bind="queryBind" row-key="id" :show-del-btn="false" immediate operation
    >
      <template #header>
        <a-button type="primary" @click="onEdit()">
          <c-icon-plus-circle-outlined />添加
        </a-button>

        <a-button type="primary" danger ghost @click="() => openVerificationQR = true">
          <c-icon-issues-close-outlined />核验二维码使用情况
        </a-button>
      </template>
      <template #operation="{ record }">
        <a-button type="link" @click="onEdit(record, true)">
          查看
        </a-button>
        <a-popconfirm @confirm="updateBatch(record)">
          <a-button type="link">
            弃用
          </a-button>
        </a-popconfirm>
      </template>
      <template #bodyCell="{ record, column }">
        <template v-if="column.dataIndex === 'status'">
          <a-tag :color="record.status === ProductBatchStatus.批次已过期 ? 'red' : 'blue'">
            {{ ProductBatchStatus[record.status] }}
          </a-tag>
        </template>
        <template v-if="column.dataIndex === 'situation'">
          <a-tooltip :title="`已激活${record.activationCount}个，共${record.count}个`">
            <a-button type="link"> {{ record.activationCount }} / {{ record.count }}</a-button>
          </a-tooltip>
        </template>
      </template>
    </c-pro-table>

    <a-drawer v-model:open="modalState" title="添加二维码生产" destroy-on-close width="680px" placement="right">
      <c-pro-form ref="proFormRef" v-model:value="model" :descriptions="{ bordered: true, column: 1, labelStyle: { width: '120px' } }" :read-only="readonly" :fields="fields" colon />
      <template v-if="productPoint">
        <a-divider>产品信息</a-divider>
        <a-descriptions bordered :label-style="{ width: '100px' }" :column="3">
          <a-descriptions-item label="积分">
            <span v-if="productPoint.randomPoints">
              {{ productPoint.minPoints }} ~ {{ productPoint.maxPoints }}
            </span>
            <span v-else> {{ productPoint.cashback }} </span>
          </a-descriptions-item>
          <a-descriptions-item label="红包">
            <span v-if="productPoint.randomCashback">
              {{ productPoint.minCashback / 100 }} ~ {{ productPoint.maxCashback / 100 }}元
            </span>
            <span v-else> {{ productPoint.cashback / 100 }}元 </span>
          </a-descriptions-item>
        </a-descriptions>
      </template>
      <template v-if="readonly">
        <a-divider>发放奖励情况</a-divider>
        <a-descriptions bordered :label-style="{ width: '100px' }" :column="2">
          <a-descriptions-item label="激活数量">
            {{ productBatchInfo.activationCount }}
          </a-descriptions-item>
          <a-descriptions-item label="发放的红包">
            <span> {{ productPoint.cashback / 100 }}元 </span>
          </a-descriptions-item>
          <a-descriptions-item label="发放的积分">
            {{ productBatchInfo.points }}
          </a-descriptions-item>
          <a-descriptions-item label="抽奖次数">
            {{ productBatchInfo.sweepstakesCount }}
          </a-descriptions-item>
        </a-descriptions>
      </template>

      <template v-if="!readonly">
        <a-divider>二维码配置</a-divider>
        <a-descriptions bordered :label-style="{ width: '100px' }" :column="2">
          <a-descriptions-item label="背景色">
            <ColorPicker v-model:pure-color="qrConfig.bgColor" format="hex8" />
          </a-descriptions-item>
          <a-descriptions-item label="图标">
            <div v-if="qrConfig.icon" class="coverBox size-54px overflow-hidden">
              <c-image :src="qrConfig.icon" alt="avatar" :preview="true" :del-ico="true" style="height: 54px; width:54px ; object-fit:cover" @del-image="() => qrConfig.icon = ''" />
            </div>
            <a-button v-else type="dashed" block class="size-80px" @click="uploadQrIcon">
              <template #icon>
                <c-icon-plus-outlined />
              </template>
              上传
            </a-button>
          </a-descriptions-item>
          <a-descriptions-item label="颜色">
            <ColorPicker v-model:pure-color="qrConfig.color" format="hex8" />
          </a-descriptions-item>
          <a-descriptions-item label="预览结果">
            <a-qrcode
              :bg-color="qrConfig.bgColor" :color="qrConfig.color" :size="80" error-level="Q" :value="qrConfig.href"
              :icon-size="20" :icon="qrConfig.icon"
            />
          </a-descriptions-item>
        </a-descriptions>
      </template>
      <div class="mt16px bg-#CCEBDA p-16px">
        <div>请注意：</div>
        <div class="mt-16px pl-16px">
          <p> 1. 请将生产的二维码移交刚玉产品包装印刷单位-桂林XXX包装有限公司进行生产流程；</p>
          <p> 2. 请谨慎保存生产的二维码，仅限于刚玉系列产品包装印刷，严禁泄露。</p>
          <p> 3. 如不慎遗失生产的二维码，请马上联系刚玉公司XX经理进行注销处理。</p>
          <p> 4. 二维码生产后请核对生产数量是否正确。</p>
        </div>
        <div class="c-error">如在生产及移交过程中有任何问题，请联系刚玉公司营销中心XX经理，联系电话-13012341234 / 0773-2568256.</div>
      </div>

      <div v-show="qrCodeList.length > 0" class="fixed z--2 op-0">
        <div v-for="qrCode, index in qrCodeList" :key="index">
          <a-qrcode
            :ref="setQrCodeRef(index)" :bg-color="qrConfig.bgColor" error-level="Q" :color="qrConfig.color" :size="1080" :value="qrCode.url!"
            :icon-size="200" :icon="qrConfig.icon"
          />
        </div>
        <!-- <QrcodeVue
          v-for="item in qrCodeList" :key="item"
          class="canvasDom" :value="item" level="M" :size="500"
        /> -->
      </div>

      <template v-if="!readonly" #extra>
        <a-button style="margin-right: 8px" @click="onClose">
          取消
        </a-button>
        <a-button :disabled="!model.count" type="primary" @click="save()">创建批次</a-button>
      </template>
    </a-drawer>
  </div>

  <a-modal v-model:open="qrOpen" title="导出二维码" :closable="false" :mask-closable="false">
    <template #footer>
      <a-popconfirm
        title="当前导出窗口关闭则无法找回二维码，请确认下载成功后再将本窗口关闭！"
        ok-text="我已成功下载，确认关闭"
        placement="bottom"
        @confirm="onCloseQrDow"
      >
        <a-button type="primary" ghost class="w-full">
          关闭窗口
        </a-button>
      </a-popconfirm>
    </template>

    <a-button class="w-full" type="primary" @click="downloadQRCodes">
      <template #icon>
        <c-icon-download-outlined />
      </template>点击下载所有二维码到压缩包
    </a-button>
    <a-alert class="mt16px" message="当前导出窗口关闭则无法找回二维码，请确认下载成功后再将本窗口关闭！" type="warning" />
  </a-modal>
  <Situation />
  <VerificationQR v-model:open="openVerificationQR" />
</template>

<script lang="ts" setup>
import type { CodeViewGenModel, ProductBatch, ProductBatchPageView } from '@/api/models'
import type { QRCode } from 'ant-design-vue'
import type { FormField } from 'ch2-components/types/pro-form/types'
import type { ColumnProps } from 'ch2-components/types/pro-table/types'
import * as api from '@/api/'
import { FileType, ProductBatchNewModel, ProductBatchStatus, ProductBatchViewModel, ProductPoint, QueryType } from '@/api/models'
import image from '@/assets/images/logo.png'
import { message } from 'ant-design-vue'
import { saveAs } from 'file-saver'
import JSZip from 'jszip'
import { replace } from 'lodash-es'
import { ColorPicker } from 'vue3-colorpicker'
import Situation from './components/Situation.vue'
import 'vue3-colorpicker/style.css'

definePage({
  meta: {
    title: '二维码生产配置',
    layoutRoute: {
      meta: { layout: 'admin', title: '二维码管理', local: true, icon: 'QrcodeOutlined', order: 8 },
    },
  },
})
const columns = reactive<ColumnProps<ProductBatch>[]>([
  { dataIndex: 'count', title: '批次数量', key: 'count' },
  // { dataIndex: 'propertyName', title: '产品名称', isShow: false, search: { el: 'input' }, align: 'left', queryType: QueryType.Include },
  { dataIndex: 'created', key: 'created', title: '创建时间', align: 'center', dateFormat: true, width: '160px' },
  { dataIndex: 'createdBy', key: 'createdBy', title: '创建用户', align: 'center', width: '160px' },
  { dataIndex: ['product', 'name'], key: 'product.name', title: '关联产品', align: 'left' },
  { dataIndex: 'status', key: 'status', title: '批次状态', align: 'center', search: { el: 'enum-select', attrs: { placeholder: '请选择产品状态', enum: ProductBatchStatus } }, queryType: QueryType.Eq },
  { dataIndex: 'remark', title: '说明' },
  { dataIndex: 'situation', title: '激活情况', align: 'center' },

])

const { tableRef, queryBind } = useTableSearch('tableRef', api.Products.QueryBatch_PostAsync, columns)

const openVerificationQR = ref(false)

const { productBatchInfo, modalState, onEdit, fields, save, model, readonly, onClose, updateBatch, qrCodeList, qrOpen, onCloseQrDow, qrConfig, uploadQrIcon, productPoint } = useEditModal()

const currentProductName = ref('')

const qrCodeRefs = ref<(InstanceType<typeof QRCode>)[]>([])

function useEditModal() {
  const modalState = ref(false)

  const proFormRef = useTemplateRef('proFormRef')

  const productPoint = ref(new ProductPoint())

  const productBatchInfo = ref(new ProductBatchViewModel())

  const fields = reactive<FormField[]>([
    { label: '批次数量', el: 'input-number', prop: 'count', attrs: { placeholder: '请输入批次数量', style: { width: '100%' } }, required: true, formItem: {} },
    {
      label: '关联产品',
      el: 'select',
      prop: 'productId',
      attrs: {
        placeholder: '关联产品',
        api: () => tableSearch(api.Products.Query_PostAsync, [{ limit: -1, offset: 0 }], null, null, ['id', 'name']),
        fieldNames: { label: 'name', value: 'id' },
        showSearch: true,
        immediate: true,
        async onChange(_value, option: any) {
          currentProductName.value = option.label
          productPoint.value = await api.Products.GetPointAsync({ id: option.value })
        },
      },
      required: true,
      formItem: { rules: [{
        required: true,
        validator: FormValidator('guidReg', '产品不能为空'),
        trigger: 'blur',
      }] },
    },
    { label: '说明', el: 'textarea', prop: 'remark', attrs: { placeholder: '请输入说明', style: { width: '100%' } }, required: true, formItem: {} },
  ])

  const model = ref(new ProductBatchNewModel())

  const readonly = ref()

  const qrCodeList = ref<CodeViewGenModel[]>([])

  const qrOpen = ref(false)

  const qrConfig = ref({
    color: '#000000',
    bgColor: '#ffffff',
    icon: image,
    href: location.href,
  })

  function onEdit(data?: ProductBatchPageView, readOnly = false) {
    model.value = data ?? new ProductBatchNewModel()
    modalState.value = true
    readonly.value = readOnly
    if (data?.id) {
      api.Products.FindProductBatchViewById_GetAsync({ productBatchId: data?.id }).then((res) => {
        productBatchInfo.value = res
      })
    }
  }

  const { run: save, spinner } = useLoading(async () => {
    await proFormRef.value?.validate()
    return api.Products.AddBatch_PostAsync(model.value).then((res) => {
      // 增加编号到url中方便后续操作
      qrCodeRefs.value = []
      qrCodeList.value = res.map(v => ({ name: v.name, url: `${v.url}&code=${v.name}` }))
      readonly.value = true
      qrOpen.value = true
      tableRef.value?.refresh()
      message.success('创建成功')
      // nextTick(download)
    })
  })

  function onClose() {
    modalState.value = false
  }

  async function updateBatch(record: ProductBatch) {
    await api.Products.UpdateBatch_PostAsync({ status: ProductBatchStatus.批次已过期, id: record.id })
    record.status = ProductBatchStatus.批次已过期
    message.success('更新成功')
  }

  async function onView(record: ProductBatch) {
    const res = await api.Products.GetBatchAsync({ id: record.id })
    console.log('%c [ res ]-151', 'font-size:13px; background:pink; color:#bf2c9f;', res)
  }

  function onCloseQrDow() {
    qrOpen.value = false
    qrCodeList.value = []
    onClose()
  }

  function uploadQrIcon() {
    useFileMangerModal(async (files) => {
      if (files.length)
        qrConfig.value.icon = await loadImage(joinFilePathById(files[0]!.id)) || ''
    }, { multiple: false, immediateReturn: true, menu: [FileType.图片] })
  }

  return { productBatchInfo, modalState, save, spinner, onEdit, fields, model, readonly, onClose, updateBatch, qrCodeList, onView, qrOpen, onCloseQrDow, qrConfig, uploadQrIcon, productPoint }
}

function setQrCodeRef(index: number) {
  return (el: InstanceType<typeof QRCode>) => {
    if (el) {
      qrCodeRefs.value[index] = el // 将元素存储到 refs 数组
    }
  }
}

function base64ToBlob(base64: string, type: string): Blob {
  const cleanBase64 = replace(base64, /^data:image\/\w+;base64,/, '')

  const byteCharacters = atob(cleanBase64) // 解码 Base64 字符串
  const byteNumbers = new Uint8Array(byteCharacters.length)

  for (let i = 0; i < byteCharacters.length; i++) {
    byteNumbers[i] = byteCharacters.charCodeAt(i)
  }

  return new Blob([byteNumbers], { type })
}

async function downloadQRCodes() {
  const zip = new JSZip()
  qrCodeRefs.value.forEach((qrCode: any, index) => {
    const base64 = qrCode.toDataURL()
    zip.file(`${qrCodeList.value[index]?.name}.png`, base64ToBlob(base64, 'image/png'))
  })

  const content = await zip.generateAsync({ type: 'blob' })
  saveAs(content, `${currentProductName.value}红包二维码${dayjs().format('YYYYMMDDHHmm')}.zip`)
}
</script>
