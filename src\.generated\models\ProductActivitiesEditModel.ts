import { ProductActivitiesStatus } from "./ProductActivitiesStatus";
export class ProductActivitiesEditModel {
  /**活动名称*/
  name?: string | null | undefined = null;
  /**活动描述*/
  description?: string | null | undefined = null;
  /**积分倍率*/
  pointsRate: number = 0;
  /**返现倍率*/
  cashbackRate: number = 0;
  /**活动状态*/
  status: ProductActivitiesStatus = 0;
  /**涉及全部产品*/
  all: boolean = false;
  /**开始时间*/
  upTime: Dayjs = dayjs();
  /**结束时间*/
  downTime: Dayjs = dayjs();
  /**参与活动的商品*/
  items?: string[] | null | undefined = [];
  /**抽奖活动*/
  lotteryActivityId?: GUID = null;
  /**主键*/
  id: GUID = "00000000-0000-0000-0000-000000000000";
}
