import { NoticeType } from "./NoticeType";
import { NoticeScope } from "./NoticeScope";
import { NoticeStatus } from "./NoticeStatus";
import { UserNotice } from "./UserNotice";
/**通知表*/
export class Notice {
  /**标题*/
  title?: string | null | undefined = null;
  /**内容（富文本）*/
  content?: string | null | undefined = null;
  /**通知类型*/
  noticeType: NoticeType = 0;
  /**通知范围*/
  scope: NoticeScope = 0;
  /**接收用户*/
  clientUserIds?: string[] | null | undefined = [];
  /**列表状态*/
  status: NoticeStatus = 0;
  /**发送时间*/
  sentTime?: Dayjs | null | undefined = null;
  userNotices?: UserNotice[] | null | undefined = [];
  /**软删除标记*/
  softDeleted: boolean = false;
  /**软删除者*/
  deletedBy?: string | null | undefined = null;
  /**软删除时间*/
  deleted?: Dayjs | null | undefined = null;
  /**创建时间*/
  created: Dayjs = dayjs();
  /**修改时间*/
  modified?: Dayjs | null | undefined = null;
  /**创建者*/
  createdBy?: string | null | undefined = null;
  /**修改者*/
  modifiedBy?: string | null | undefined = null;
  /**主键*/
  id: GUID = "00000000-0000-0000-0000-000000000000";
}
