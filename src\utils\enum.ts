/*
 * @Description:
 * @Author: 景 彡
 * @Date: 2024-11-07 14:38:30
 * @LastEditors: 景 彡
 */
/*
 * @Author: Ztq
 * @Date: 2022-12-06 17:44:07
 * @LastEditors: nono
 * @LastEditTime: 2024-11-07 16:59:24
 * @Description:
 * @FilePath: \content-analyze-manage-vue\src\utils\enum.ts
 */

export function enumToObject<T>(enumData: T): { label: string | number, value: keyof T }[] {
  const data: { label: string | number, value: keyof T }[] = []
  const regPos = /^(?:0|[1-9]\d*|-[1-9]\d*)$/
  for (const key in enumData) {
    if (!regPos.test(key))
      data.push({ value: enumData[key] as any, label: key })
  }
  return data
}

export function enumToString<T>(enumData: T): Record<keyof T, string> {
  const temp: any = {}

  enumToObject(enumData).forEach((item) => {
    temp[item.label] = item.label
  })

  return temp as Record<keyof T, string>
}
