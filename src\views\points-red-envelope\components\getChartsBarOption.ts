/*
 * @Description:
 * @Author: 景 彡
 * @Date: 2024-11-08 16:05:53
 * @LastEditors: 景 彡
 */
import type { UserTotalStatistics } from '@/api/models'

export function getChartsBarOption(
  data: UserTotalStatistics[],
  color: string[] = ['#09B388', '#009944'],
) {
  const newData = data.sort((a, b) => b.data - a.data)
  const xData = newData.map(item => `${item.name} ${item.phone}`)
  const seriesData = newData.map(item => item.data)

  const dataZoomMove = {
    start: 0,
    end: 5,
  }

  const option: any = {
    dataZoom: [
      {
        show: true, // 为true 滚动条出现
        startValue: dataZoomMove.start,
        endValue: dataZoomMove.end,
        yAxisIndex: [0, 1], // 关联多个y轴
        width: 6,
        showDetail: false,
        brushSelect: false,
        orient: 'vertical',
        backgroundColor: '#8BD99C',
        zoomLock: true,
        fillerColor: '#0AB389',
      },
      {
        // 没有下面这块的话，只能拖动滚动条，鼠标滚轮在区域内不能控制外部滚动条
        type: 'inside',
        yAxisIndex: 0,
        zoomOnMouseWheel: false, // 滚轮是否触发缩放
        moveOnMouseMove: true, // 鼠标滚轮触发滚动
        moveOnMouseWheel: true,
      },
    ],
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },

    },
    legend: {},
    grid: {
      left: '3%',
      right: '10%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'value',
      show: false,
    },
    yAxis: [
      {
        type: 'category',
        inverse: true,
        data: xData,
        splitLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLine: {
          show: false,
        },
        axisLabel: {
          inside: true,
          verticalAlign: 'bottom',
          lineHeight: 36,
          margin: 2, // 刻度标签与轴线之间的距离
          formatter: (value: string) => {
            const k = xData.indexOf(value)
            const index = k < 9 ? (k + 1) : k + 1
            return `{b|TOP${index}} {a|${value}}`
          },
          rich: {
            b: {
              // borderColor: "#fff",
              // borderWidth: 1,
              padding: [3, 1, 0, 1],
              fontSize: '14px',
              fontWeight: 'bold',
            },
            a: {
              fontSize: '14px',
              padding: [4, 0, 0, 8],
            },
          },
        },
      },
    ],
    series: [
      {
        name: '',
        type: 'bar',
        barWidth: 10,
        label: {
          show: true,
          position: 'right',
          valueAnimation: true,
        },
        itemStyle: {
          borderRadius: [0, 0, 20, 0],
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 1,
            y2: 1,
            colorStops: [
              {
                offset: 1,
                color: color[0],
              },
              {
                offset: 0,
                color: color[1],
              },
            ],
          },
        },
        data: seriesData,
      },
    ],
  }

  return option
}
