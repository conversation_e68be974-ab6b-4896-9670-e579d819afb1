import { UserRoleViewModel } from "./UserRoleViewModel";
import { ClientUserState } from "./ClientUserState";
import { ShippingAddressBase } from "./ShippingAddressBase";
export class ClientUserViewModel {
  id: GUID = "00000000-0000-0000-0000-000000000000";
  name?: string | null | undefined = null;
  phoneNumber?: string | null | undefined = null;
  roles?: UserRoleViewModel[] | null | undefined = [];
  userName?: string | null | undefined = null;
  /**手机号是否验证*/
  phoneNumberConfirmed: boolean = false;
  /**账号过期时间*/
  expiration?: Dayjs | null | undefined = null;
  /**密码修改时限*/
  modifyPasswordEnd?: Dayjs | null | undefined = null;
  /**最近登录时间*/
  lastLogin: Dayjs = dayjs();
  /**经度（最后登录）*/
  latitude?: number | null | undefined = null;
  /**纬度（最后登录）*/
  longitude?: number | null | undefined = null;
  /**详细地址（最后登录）*/
  streetAddress: string = "";
  /**当前积分余额*/
  points: number = 0;
  /**账号权限限制*/
  limitations: boolean = false;
  /**账号权限限制*/
  limitMessage?: string | null | undefined = null;
  /**账号状态*/
  state: ClientUserState = 0;
  /**收获地址所在地区*/
  add?: ShippingAddressBase | null | undefined = null;
}
