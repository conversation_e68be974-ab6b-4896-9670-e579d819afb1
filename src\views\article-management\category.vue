<!--
 * @Description:
 * @Author: 景 彡
 * @Date: 2024-11-07 09:03:54
 * @LastEditors: 景 彡
-->
<template>
  <div class="p16px bg">
    <div class="header mb16px">
      <div class="header-form">
        <c-form layout="inline">
          <c-form-item>
            <a-input-search
              v-model:value="listQuery.name"
              placeholder="请输入标题名称"

              style="width: 400px;"
              allow-clear enter-button
              @search="listRef.fetchData()"
            />
          </c-form-item>
          <c-form-item>
            <a-button type="primary" @click="onEdit(null)">
              <c-icon-plus-outlined />新增分类
            </a-button>
          </c-form-item>
        </c-form>
      </div>
    </div>

    <c-table ref="listRef" row-key="id" bordered :pagination="false" :columns="columns" immediate :api="api.InformationCategories.GeCategoryPaged_GetAsync" :get-params="listQuery">
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'operation'">
          <a-dropdown>
            <a class="ant-dropdown-link" @click.prevent>
              <b>. . .</b>
            </a>
            <template #overlay>
              <a-menu>
                <a-menu-item>
                  <a href="javascript:;" @click="onEdit(record)">修改分类</a>
                </a-menu-item>
                <a-menu-item>
                  <a href="javascript:;" @click="onTop(record)">置顶分类</a>
                </a-menu-item>
                <a-menu-item>
                  <a href="javascript:;" @click="onDelete(record)">删除</a>
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </template>
        <template v-else-if="column.dataIndex === 'menuShow'">
          {{ record.data.menuShow ? "显示" : "不显示" }}
        </template>
        <template v-else-if="column.dataIndex === 'categoryType'">
          <a-tag> {{ models.CategoryType[record.data.categoryType] }}</a-tag>
        </template>
      </template>
    </c-table>

    <EditCategory ref="categoryRef" @success="listRef.refreshData()" />
  </div>
</template>

<script setup lang="ts">
import * as api from '@/api'
import * as models from '@/api/models'
import { deepCopy } from '@/utils/util'
import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
import { message, Modal } from 'ant-design-vue'

import { createVNode } from 'vue'
import EditCategory from './components/EditCategory.vue'

definePage({
  meta: {
    title: '文章分类',
  },
})

const listQuery = ref<Required<Parameters<typeof api.InformationCategories.GeCategoryPaged_GetAsync>[0]>>({
  type: '',
  name: '',
  menuShow: undefined,
})

const listRef = ref()

const categoryRef = useTemplateRef('categoryRef')

const columns = [
  {
    title: '分类名称',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '类型',
    dataIndex: 'categoryType',
    key: 'categoryType',
  },
  {
    title: '描述',
    dataIndex: 'description',
    key: 'description',
  },

  {
    title: '文章量',
    dataIndex: 'informationCount',
    key: 'informationCount',
  },
  {
    title: '显示菜单',
    dataIndex: 'menuShow',
    key: 'menuShow',
    align: 'center',
  },
  {
    title: '操作',
    dataIndex: 'operation',
    key: 'operation',
    align: 'center',
  },
] as const

async function onTop(record: models.InformationCategoryView) {
  let data: models.InformationCategoryEditModel = new models.InformationCategoryEditModel()
  data = deepCopy(record.data as any)
  data.menuTop = true
  try {
    await api.InformationCategories.ModifyInformationCategory_PostAsync(data)
    message.success('分类修改成功！')
    listRef.value.refreshData()
  }
  catch (error: any) {
    message.error(`资讯信息更新失败：${error.message}`)
  }
}

/**
 * 操作分类信息
 * @param record
 */
function onEdit(record: models.InformationCategoryView | null) {
  categoryRef.value!.categoryData = record?.data || new models.InformationCategory()
  console.log('%c [ record ]-147', 'font-size:13px; background:pink; color:#bf2c9f;', record)
  categoryRef.value!.visible = true
}

function onDelete(record: models.InformationCategoryView) {
  Modal.confirm({
    title: '删除提示',
    icon: createVNode(ExclamationCircleOutlined),
    content: `确定删除【${record.name}】分类吗？`,
    okText: '确定',
    okType: 'danger',
    cancelText: '取消',
    async onOk() {
      try {
        await api.InformationCategories.DeleteInformationCategory_PostAsync([record.id])
        message.success('删除成功')
        listRef.value.refreshData()
      }
      catch (error: any) {
        message.error(`删除失败${error.message}`)
      }
    },
    onCancel() {
      console.log('Cancel')
    },
  })
}
</script>

<style scoped lang="less">
.list-color {
  color: #000000c4;
}
</style>
