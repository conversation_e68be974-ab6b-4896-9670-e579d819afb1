<!--
 * @Description:活动管理
 * @Author: 景 彡
 * @Date: 2024-11-01 10:44:12
 * @LastEditors: 景 彡
-->
<template>
  <div class="p-16px c-bg">
    <c-pro-table
      ref="tableRef"
      align="center"
      v-bind="queryBind"
      row-key="id"
      immediate show-add-btn operation
      :row-selection="rowSelection"
      @add-row="onEdit(null, false)"
      @del-rows="delRows"
    >
      <template #bodyCell="{ column, record }">
        <ImageView v-if="column.dataIndex === 'image' && record.image" style="width: 50px; height: 50px; object-fit:cover" :src="(record.image)" />
        <a-tag v-if="column.dataIndex === 'status'" :color="record.status === CarouselStatus.使用中 ? 'success' : 'default'">{{ CarouselStatus[record.status] }}</a-tag>
      </template>
      <template #operation="{ record }">
        <a-button type="link" @click="onEdit(record, true)">
          查看
        </a-button>
        <a-dropdown>
          <a class="ant-dropdown-link" @click.prevent>
            更多<c-icon-down-outlined />
          </a>
          <template #overlay>
            <a-menu>
              <a-menu-item>
                <a href="javascript:;" @click="onEdit(record, false)">编辑</a>
              </a-menu-item>
              <a-menu-item>
                <a style="color: red" href="javascript:;" @click="onDel(record)">删除</a>
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
      </template>
    </c-pro-table>
  </div>

  <EditorNew :id="currentId" v-model:open="open" :read-only="readonly" @success="tableRef?.refresh()" />
</template>

<script lang='ts' setup>
import type { CarouselConfig } from '@/api/models'
import type { ColumnProps } from 'ch2-components/types/pro-table/types'
import * as api from '@/.generated/apis'
import { CarouselStatus, CarouselType } from '@/api/models'
import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import modal from 'ant-design-vue/es/modal'
import EditorNew from './editor-new.vue'

definePage({
  meta: {
    title: '轮播活动管理',
    layoutRoute: {
      meta: { layout: 'admin', title: '轮播活动管理', local: true, icon: 'ScheduleOutlined', order: 15 },
    },
  },
})

const columns = reactive<ColumnProps<CarouselConfig>[]>([
  { dataIndex: 'image', title: '图片' },
  { dataIndex: 'value1', title: '描述', align: 'left' },
  { dataIndex: 'type', title: '类型', enum: CarouselType },
  { dataIndex: 'status', title: '状态' },
  { dataIndex: 'count', title: '分享次数上限' },
  { dataIndex: 'points', title: '转发积分' },
  { dataIndex: 'created', title: '创建时间', dateFormat: true, sorter: true, width: 160 },
  { dataIndex: 'modified', title: '修改时间', dateFormat: true, sorter: true, width: 160 },
])

const { tableRef, queryBind, rowSelection } = useTableSearch('tableRef', api.CarouselConfigs.Query_PostAsync, columns)

function remove(ids: string[]) {
  modal.confirm({
    title: '确定删除吗？',
    icon: h(ExclamationCircleOutlined),
    content: '删除后不可恢复，请谨慎操作！',
    okText: '确定',
    okType: 'danger',
    cancelText: '取消',
    onOk() {
      api.CarouselConfigs.Remove_PostAsync(ids).then(() => {
        tableRef.value?.refresh()
        message.success('删除成功')
      })
    },
    onCancel() {
      console.log('Cancel')
    },
  })
}

function delRows(keys: string[]) {
  remove(keys)
}

function onDel(record: CarouselConfig) {
  remove([record.id])
}

const { onEdit, open, currentId, readonly } = useEditModal()

// const router = useRouter()

function useEditModal() {
  const open = ref(false)

  const readonly = ref(false)

  const currentId = ref(Guid.empty)

  function onEdit(record: CarouselConfig | null, isReadOnly: boolean) {
    currentId.value = record?.id || Guid.empty
    console.log('%c [ currentId.value ]-122', 'font-size:13px; background:pink; color:#bf2c9f;', currentId.value)
    readonly.value = isReadOnly
    open.value = true
    // const routeData = router.resolve({
    //   path: '/event-management/editor-new',
    //   query: { id: record?.id },
    // })
    // window.open(routeData.href, '_blank')
  }

  return { onEdit, open, currentId, readonly }
}

onMounted(async () => {
})
</script>

<style scoped>

</style>
