import { RewardRecordSatus } from "./RewardRecordSatus";
import { ClientUserInfoBaseView } from "./ClientUserInfoBaseView";
export class ReferralRewardRecordPageViewModel {
  satus: RewardRecordSatus = 0;
  /**关联用户*/
  clientUserInfo?: ClientUserInfoBaseView | null | undefined = null;
  activityId: GUID = "00000000-0000-0000-0000-000000000000";
  activityName?: string | null | undefined = null;
  /**描述*/
  description?: string | null | undefined = null;
  /**现金奖励*/
  cashback?: number | null | undefined = null;
  /**现金奖励发放情况*/
  isCashback?: boolean | null | undefined = null;
  /**现金奖励发放记录*/
  cashbackId?: GUID = null;
  /**积分奖励*/
  points?: number | null | undefined = null;
  /**积分奖励发放情况*/
  isPoints?: boolean | null | undefined = null;
  /**积分奖励发放记录*/
  pointsId?: GUID = null;
  /**抽奖次数奖励*/
  lotteryActivityCount?: number | null | undefined = null;
  /**抽奖次数奖励发放情况*/
  isLotteryActivityCount?: boolean | null | undefined = null;
  /**抽奖次数奖励发放记录*/
  lotteryActivityCountId?: GUID = null;
  /**主键*/
  id: GUID = "00000000-0000-0000-0000-000000000000";
}
