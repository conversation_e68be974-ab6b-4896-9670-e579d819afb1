import type { AxiosRequestConfig } from 'axios'
import { LotteryPrizesType } from '@/api/models'
import { Modal } from 'ant-design-vue'
import axios from 'axios'

export enum DrawComp {
  九宫格 = 'JGGDraw',
  轮盘 = 'LPDraw',
  标题 = 'Title',
  图片 = 'IMAGE',
  公告 = 'NOTICE',
  占位符 = 'PLACEHOLDER',
}

export type Attrs = object

export type ImageAttrs = {
  src: string
}

export type PlaceholderAttrs = {
  backgroundColor?: string
}

export type TitleAttrs = {
  chid?: TitleAttrs[]
  text?: string | typeof Variable[keyof typeof Variable]
  type?: '文本' | '变量'
  size?: number
  color?: string
  bold?: number
  padding?: number
  textAlign?: 'center' | 'left' | 'right'
}

export type Position = {
  position?: 'absolute' | 'fixed' | 'static' | 'relative'
  x: number
  y: number
  z: number
  w?: string | number
  h?: string | number

}

export type CompNameType = '抽奖' | '标题' | '图片' | '公告' | '占位符'

export const RouterUni = {
  我的奖励: '/pages/luck-draw/lottery-records?id=__ID__',
  奖励中心: '/pages/personal-settings/reward-center',
  每日抽奖: '/pages/luck-draw/count-records',
  抽奖次数记录: '/pages/luck-draw/count-records?id=__ID__',
} as const

export const Variable = {
  抽奖次数: '__COUNT__',
  活动id: '__ID__',
} as const

export type VariableMap = Record<typeof Variable[keyof typeof Variable], { data: any, setData: () => void }>

export interface ILuckyDrawDesignCompBase {
  id: string
  position: Position
  compName: DrawComp
  routeOption?: typeof RouterUni[keyof typeof RouterUni]
}

export type ILuckyDrawDesignImageComp = {
  type: '图片'
  attrs?: ImageAttrs
} & ILuckyDrawDesignCompBase

export type ILuckyDrawDesignTitleComp = {
  type: '标题'
  attrs?: TitleAttrs
} & ILuckyDrawDesignCompBase

export type ILuckyDrawDesignDrawComp = {
  type: '抽奖'
  attrs?: Attrs
} & ILuckyDrawDesignCompBase

export type ILuckyDrawDesignNoticeComp = {
  type: '公告'
  attrs?: Attrs
} & ILuckyDrawDesignCompBase

export type ILuckyDrawDesignPlaceholderComp = {
  type: '占位符'
  attrs?: PlaceholderAttrs
} & ILuckyDrawDesignCompBase

export type ILuckyDrawDesignComp = ILuckyDrawDesignNoticeComp | ILuckyDrawDesignImageComp | ILuckyDrawDesignTitleComp | ILuckyDrawDesignDrawComp | ILuckyDrawDesignPlaceholderComp

export const luckyDrawDesignComps: Array<Omit<ILuckyDrawDesignComp, 'id'> & { /** 同样类型下的限制组件个数 */ limit?: number }> = [
  {
    compName: DrawComp.九宫格,
    type: '抽奖',
    position: { x: 0, y: 0, z: 1 },
    limit: 1,
  },
  {
    compName: DrawComp.轮盘,
    type: '抽奖',
    position: { x: 0, y: 0, z: 1, w: 300, h: 300 },
    limit: 1,
  },
  {
    compName: DrawComp.标题,
    type: '标题',
    position: { x: 0, y: 0, z: 1 },
    attrs: {
      color: '#F9DF83',
      chid: [
        {
          text: '这是一个标题文本',
        },

      ],
      size: 18,
      bold: 500,
    },
  },
  {
    compName: DrawComp.图片,
    type: '图片',
    position: { x: 0, y: 0, z: 1 },
  },
  {
    compName: DrawComp.公告,
    type: '公告',
    position: { x: 0, y: 0, z: 1 },
  },
  {
    compName: DrawComp.占位符,
    type: '占位符',
    position: { x: 0, y: 0, z: 1, w: 100, h: 100 },
    attrs: {
      backgroundColor: 'transparent',
    },
  },
]

export type ILuckyDrawDesignConfig = {
  comps: ILuckyDrawDesignComp[]
  bgColor?: string
  title?: string
  titleBgColor?: string
  titleColor?: string
}

export function getValue(v?: number | string) {
  if (!v)
    return v

  const res = Number(v)

  if (Number.isNaN(res))
    return v
  return `${res / 16}rem`
}

export async function fetchApi(option: AxiosRequestConfig) {
  try {
    const res = await axios.request(option)
    if (res.data.code !== 200) {
      throw new Error(res.data?.message) || '错误'
    }
    return res.data.data
  }
  catch (err: any) {
    let title = ''
    let content = ''
    let fn = () => wx.miniProgram.navigateBack()
    if (err.status === 401) {
      title = '授权错误'
      content = '获取用户认证失败，请稍后重试。'
    }
    else {
      title = '提示'
      content = err.message || '网络出现了问题，请稍后重试。'
      fn = () => {}
    }

    Modal.info({
      content,
      title,
      okText: '返回',
      onOk: fn,
    })
    throw new Error(content)
  }
}

export function lotteryPrizesTypeUnit(type: LotteryPrizesType) {
  return {
    [LotteryPrizesType.积分奖励]: '积分分',
    [LotteryPrizesType.现金红包]: '元',
    [LotteryPrizesType.积分商品]: '个',
    [LotteryPrizesType.抽奖次数]: '次',
    [LotteryPrizesType.谢谢惠顾]: '未中奖',
  }[type]
}
