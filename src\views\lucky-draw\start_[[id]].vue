<template>
  <div class="text-16px">
    <Preview :id="route.params.id" :token="token" :base-url="baseUrl" @get-data="setData">
      <div class="relative box-border w-100% px[var(--safe-padding)]">
        <div class="absolute left-50% top--5 z-3 h-15 w-86% translate-x-[-50%] rounded-lg bg-#FF4949 text-center text-4.5 c-#fff line-height-15">
          如何获得抽奖资格
        </div>
        <div class="box-border rounded-xl bg-#FBEECA pt-10">
          <div class="text p px-8 c-#DE145B line-height-8">
            <div>1、扫码“桂林刚玉”系列产品包装的“红包二维码”。</div>
            <div>2、“刚玉智慧商城”小程序每日签到。</div>
            <div>3、推广好友成功注册“刚玉智慧商城”小程序。</div>
            <div>4、推广好友成功激活“红包二维码”。</div>
          </div>
          <div class="flex gap2 p2 pb">
            <!-- @click="router.push('/pages/personal-settings/reward-center')" -->
            <div class="box-border h-20 max-h-20 flex flex-1 items-center justify-between rounded-2 bg-#FF9251" @click="onBinEvent('/pages/personal-settings/reward-center')">
              <div class="p-8px pr-0">
                <div class="c-#fff">
                  <span class="text-4">现金奖励</span>
                  <span class="i-iconamoon-arrow-right-6-circle-fill" />
                </div>
                <div class="mt-1.5 text-3 c-#fff">
                  官方提现小技巧
                </div>
              </div>
              <div class="size-19">
                <img class="h-100% w-100%" src="../../assets/images/money.png" alt="">
              </div>
            </div>
            <!-- @click="router.push('/pages/luck-draw/index')" -->
            <div class="box-border h-20 flex flex-1 items-center justify-between rounded-2 bg-#FF6462" @click="onBinEvent('/pages/luck-draw/index')">
              <div class="p2 pr-0">
                <div class="c-#fff">
                  <span class="text-4">每日抽奖</span>
                  <span class="i-iconamoon-arrow-right-6-circle-fill" />
                </div>
                <div class="mt-1.5 text-3 c-#fff">
                  签到得抽奖次数
                </div>
              </div>
              <div class="box-border size-19 p2">
                <img class="h-100% w-100%" src="../../assets/images/luckdraw.png" alt="">
              </div>
            </div>
          </div>
        </div>
      </div>
    </Preview>
  </div>
</template>

<script setup lang="ts">
import type { ILuckyDrawDesignConfig } from './components/design/utils'
import { Modal } from 'ant-design-vue'
import { useRoute } from 'vue-router'
import Preview from './components/design/preview.vue'

definePage({
  meta: { layout: false, local: true, authorize: [] },
  beforeEnter: (_to, _from, next) => {
    const script = document.createElement('script')
    script.src = 'https://res.wx.qq.com/open/js/jweixin-1.4.0.js'
    script.onload = () => {
      console.log('Library loaded')
      next()
    }
    script.onerror = () => {
      console.error('Failed to load the library')
      next()
    }
    document.title = ''
    document.head.appendChild(script)
  },
})

const route = useRoute<'/lucky-draw/start_[[id]]'>()

const token = computed(() => route.query.token as string)

const baseUrl = computed(() => route.query.baseUrl as string)

function onBinEvent(url: string) {
  wx.miniProgram.navigateTo({
    url,
  })
}

function setData(data: ILuckyDrawDesignConfig) {
  document.body.setAttribute('style', `--lucky-draw-bg:${data.bgColor}`)
}

onMounted(() => {
  if (!token.value) {
    Modal.info({
      title: '未授权的访问',
      onOk: () => {
        window.close()
      },
      onCancel() {
        window.close()
      },
    })
  }
})
</script>

<style>
body,
#app {
  background-color: var(--lucky-draw-bg);
}
</style>
