<template>
  <a-modal v-model:open="open" title="发红包" :mask-closable="false" ok-text="发送" @ok="send">
    <a-form
      ref="formRef"
      :model="editForm"
      :label-col="{ span: 8 }"
      :wrapper-col="{ span: 16 }"
      autocomplete="off"
    >
      <a-form-item
        label="红包金额"
        name="amount"
        :rules="[{ required: true, message: '请输入红包金额!' }]"
      >
        <c-input-money v-model:value="editForm.amount" :unit="UnitType.元" :min="30" :max="19000" :step="10" style="width: 100%" />
      </a-form-item>
      <a-form-item
        label="提现原因"
        name="name"
        :rules="[{ required: true, message: '请输入提现原因!' }]"
      >
        <c-input v-model:value="editForm.name" style="width: 100%" />
      </a-form-item>

      <a-form-item
        label="提现类型（客户看）"
        name="shortDescription"
        :rules="[{ required: true, message: '请输入提现提现备注!' }]"
      >
        <a-auto-complete
          v-model:value="editForm.shortDescription"
          :options="[{ value: '扫码奖励' }, { value: '抽奖奖励' }]"
          style="width: 100%"
          placeholder="输入或选择"
        />
      </a-form-item>

      <a-form-item
        label="提现备注（客户看）"
        name="userRemark"
        :rules="[{ required: true, message: '请输入提现备注！' }]"
      >
        <c-input v-model:value="editForm.userRemark" style="width: 100%" />
      </a-form-item>

      <a-form-item
        label="支付密码"
        name="key"
        :rules="[{ required: true, message: '请输入支付密码！' }]"
      >
        <c-input-password v-model:value="editForm.key" style="width: 100%" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
import * as api from '@/api'
import { message } from 'ant-design-vue'
import { UnitType } from 'ch2-components/lib/input-money/types'

const props = defineProps<{ userId: string } >()

const open = defineModel('open', { default: false })

const formRef = useTemplateRef<any>('formRef')

const editForm = ref<Parameters<typeof api.ClientUserManage.TenPayClientUser_PostAsync>[0]>({
  amount: 30,
  clientUserId: '',
})

function send() {
  editForm.value.clientUserId = props.userId
  formRef.value?.validate().then(() => api.ClientUserManage.TenPayClientUser_PostAsync(editForm.value).then(() => {
    open.value = false
    editForm.value = {
      amount: 30,
      clientUserId: '',
    }
    message.success('发送成功')
  }))
}
</script>

<style scoped>

</style>
