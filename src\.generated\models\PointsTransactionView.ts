import { OpTypeQuery } from "./OpTypeQuery";
import { RecordSource } from "./RecordSource";
export class PointsTransactionView {
  /**积分的增减量*/
  number: number = 0;
  /**增加t / 减少 f*/
  opType: OpTypeQuery = 0;
  /**简短描述*/
  shortDescription?: string | null | undefined = null;
  /**描述*/
  description?: string | null | undefined = null;
  /**来源*/
  source: RecordSource = 0;
  /**产品ID*/
  sourceId?: GUID = null;
  /**防伪ID*/
  productSecurityCodeId?: GUID = null;
  /**产品活动ID*/
  productActivitiesId?: GUID = null;
  /**漆工ID*/
  clientUserId?: GUID = null;
  /**漆工昵称*/
  userName?: string | null | undefined = null;
  /**电话名称*/
  userPhoneNumber?: string | null | undefined = null;
  /**时间*/
  time: Dayjs = dayjs();
}
