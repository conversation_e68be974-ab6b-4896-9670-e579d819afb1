<!--
 * @Description: 商品兑换活动管理
 * @Author: 景 彡
 * @Date: 2024-12-02 10:00:00
 * @LastEditors: 景 彡
-->
<template>
  <c-pro-table
    ref="tableRef"
    align="center"
    v-bind="queryBind"
    row-key="id"
    immediate show-add-btn operation
    :row-selection="rowSelection"
    :scroll="{ x: 1440 }"
    :operation-config="{ fixed: 'right', width: '180px' }"
    @add-row="onEdit()"
    @del-rows="delRows"
  >
    <template #bodyCell="{ column, record }">
      <a-tag v-if="column.dataIndex === 'all'" :color="record.all ? '#55acee' : '#3b5999'">{{ record.all ? '是' : '否' }}</a-tag>
      <span v-if="column.dataIndex === 'items'">
        <a-tag v-if="record.all" color="processing"> 全部商品 </a-tag>
        <span v-else><a-tag v-for="product in record.items" :key="product.id" color="processing"> {{ product.name }} </a-tag></span>
      </span>
    </template>
    <template #operation="{ record }">
      <a-button type="link" @click="onEdit(record)">
        编辑
      </a-button>
      <a-popconfirm title="是否确认删除当前产品" @confirm="delRows([record.id])">
        <a-button type="link" style="color: red;">
          删除
        </a-button>
      </a-popconfirm>
    </template>
  </c-pro-table>
  <ActivitiesInfo :id="model.id" v-model:open="modalState" :read-only="readonly" title="添加/编辑倍率活动信息" @change="onClose" />
</template>

<script lang='ts' setup>
import type { ColumnProps } from 'ch2-components/types/pro-table/types'
import * as api from '@/api/'
import { type ProductActivities, ProductActivitiesEditModel, ProductActivitiesStatus, QueryType } from '@/api/models'
import { message } from 'ant-design-vue'
import ActivitiesInfo from './components/ActivitiesInfo.vue'

definePage({
  meta: {
    title: '活动管理',
  },
})

const columns = reactive<ColumnProps<ProductActivities>[]>([
  { dataIndex: 'name', title: '活动名称', search: { el: 'input' }, align: 'left', queryType: QueryType.Include },
  { dataIndex: 'description', title: '活动描述', truncatedText: true },
  { dataIndex: 'pointsRate', title: '积分倍率', width: 80 },
  { dataIndex: 'cashbackRate', title: '返现倍率', width: 80 },
  { dataIndex: 'status', title: '活动状态', enum: ProductActivitiesStatus },
  { dataIndex: 'items', title: '涉及参与活动的商品' },
  { dataIndex: ['lotteryActivity', 'name'], title: '抽奖活动' },
  { dataIndex: 'upTime', title: '开始时间', dateFormat: true, fixed: 'right', sorter: true },
  { dataIndex: 'downTime', title: '结束时间', dateFormat: true, fixed: 'right', sorter: true },
])

const { tableRef, queryBind, rowSelection } = useTableSearch('tableRef', api.Products.QueryActivities_PostAsync, columns)

const { modalState, onEdit, model, readonly, onClose } = useEditModal()

function delRows(keys: string[]) {
  api.Products.RemoveActivities_PostAsync(keys).then(() => {
    rowSelection.selectedRowKeys = []
    rowSelection.selectedRows = []
    tableRef.value?.refresh()
    message.success('删除成功')
  })
}

function useEditModal() {
  const modalState = ref(false)

  const model = ref(new ProductActivitiesEditModel())

  const readonly = ref()

  function onClose() {
    tableRef.value?.refresh()
    modalState.value = false
  }

  function onEdit(data?: ProductActivities, readOnly = false) {
    model.value = viewModelToEditModel(data, new ProductActivitiesEditModel())
    readonly.value = readOnly
    modalState.value = true
  }

  return { modalState, onEdit, model, readonly, onClose }
}
</script>

<style scoped>

</style>
