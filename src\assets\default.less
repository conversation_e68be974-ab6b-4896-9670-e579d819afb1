@import 'ant-design-vue/dist/reset.css';
@import 'ckeditor5/ckeditor5.css'; // 定义变量
@scrollbar-width: 6px;
@scrollbar-track-color: var(--ch2-color-border);
@scrollbar-thumb-color: var(--ch2-color-primary-bg-hover);
@scrollbar-thumb-radius: 8px;

// 定义混合
.scrollbar-style(@width, @trackColor, @thumbColor, @thumbRadius) {
  & {
    width: @width;
  }
  &-track {
    background: @trackColor;
  }
  &-thumb {
    background: @thumbColor;
    border-radius: @thumbRadius;
  }
}

/* Webkit (Safari/Chrome) */
::-webkit-scrollbar {
  .scrollbar-style(@scrollbar-width, @scrollbar-track-color, @scrollbar-thumb-color, @scrollbar-thumb-radius);
}

/* Firefox */
::-moz-scrollbar {
  .scrollbar-style(@scrollbar-width, @scrollbar-track-color, @scrollbar-thumb-color, @scrollbar-thumb-radius);
}

/* IE/Edge */
::-ms-scrollbar {
  .scrollbar-style(@scrollbar-width, @scrollbar-track-color, @scrollbar-thumb-color, @scrollbar-thumb-radius);
}

.dp-text-ellipsis-wrapper .btn {
  color: var(--ch2-color-primary-text) !important;
}

/* 1. 全局 Reset，只影响 .editor-info-detail 内部 */
.editor-info-detail {
  all: initial;
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, 'PingFang SC', 'Microsoft YaHei',
    sans-serif;
  line-height: 1.8;
  font-size: 16px;
  color: #2c2c2c;
}

/* 2. 通用标签样式 */
.editor-info-detail * {
  all: unset;
  display: revert;
  box-sizing: border-box;
  word-wrap: break-word;
}

/* 3. 段落与标题 */
.editor-info-detail p {
  margin: 1em 0;
}
.editor-info-detail h1,
.editor-info-detail h2,
.editor-info-detail h3,
.editor-info-detail h4,
.editor-info-detail h5,
.editor-info-detail h6 {
  font-weight: bold;
  margin: 1.2em 0 0.6em;
}
.editor-info-detail h1 {
  font-size: 2em;
}
.editor-info-detail h2 {
  font-size: 1.75em;
}
.editor-info-detail h3 {
  font-size: 1.5em;
}
.editor-info-detail h4 {
  font-size: 1.25em;
}
.editor-info-detail h5 {
  font-size: 1.1em;
}
.editor-info-detail h6 {
  font-size: 1em;
}

/* 4. 列表 */
.editor-info-detail ul,
.editor-info-detail ol {
  margin: 1em 0;
  padding-left: 2em;
}
.editor-info-detail li {
  margin: 0.4em 0;
}

/* 5. 图片 */
.editor-info-detail img {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 1em auto;
}

/* 6. 超链接 */
.editor-info-detail a {
  color: #1677ff;
  text-decoration: underline;
  cursor: pointer;
}

/* 7. 表格 */
.editor-info-detail table {
  width: 100%;
  border-collapse: collapse;
  border: 1px solid hsl(0, 0%, 0%);
  margin: 1em 0;
}
.editor-info-detail th,
.editor-info-detail td {
  border: 1px solid #ddd;
  padding: 0.5em 1em;
  text-align: left;
}

/* 8. 引用 */
.editor-info-detail blockquote {
  margin: 1em 0;
  padding: 0.5em 1em;
  border-left: 4px solid #ccc;
  background-color: #f9f9f9;
  color: #666;
}

/* 9. 代码 */
.editor-info-detail pre,
.editor-info-detail code {
  font-family: Menlo, Monaco, Consolas, monospace;
  background: #f4f4f4;
  padding: 0.2em 0.4em;
  border-radius: 4px;
}
.editor-info-detail pre {
  padding: 1em;
  overflow-x: auto;
}

/* 10. 其它可选优化 */
.editor-info-detail strong {
  font-weight: bold;
}
.editor-info-detail em {
  font-style: italic;
}
.editor-info-detail hr {
  border: none;
  border-top: 1px solid #eee;
  margin: 2em 0;
}
