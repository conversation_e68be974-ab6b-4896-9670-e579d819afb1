<!--
 * @Description:
 * @Author: 景 彡
 * @Date: 2024-12-03 10:55:04
 * @LastEditors: 景 彡
-->
<template>
  <DefineAreaItem v-slot="{ data, type }">
    <div
      v-for="item, i in data" :key="i"
      :class="{ 'bg-primary-active c-white': currentGeo?.id === item.id && currentGeoType === type }"
      class="hover:bg-primary-bg-active relative mb8px cursor-pointer b-(1px #ddd solid) rounded-2 p4px"
      title="点击编辑"
      @click="selectArea(item, type)"
    >
      <div> <span>坐标：</span>{{ item.latitude }}，{{ item.longitude }}</div>
      <div class="flex">
        <span class="whitespace-nowrap">范围：</span>
        <a-input-number
          v-if="currentGeo?.id === item.id && currentGeoType === type"
          v-model:value="item.radiusKm!" :min="0.1" :max="5000" class="!w-100px" addon-after="公里"
          @change="areaChange(item)"
        />
        <template v-else>{{ item.radiusKm }} 公里</template>
      </div>
      <div
        title="删除该配置" class="i-material-symbols-delete-outline-sharp absolute right-10px top-10px cursor-pointer c-error"
        @click.stop="removSanGeoRestriction(type, i)"
      />
    </div>
  </DefineAreaItem>
  <div class="h[calc(100vh-120px)] flex gap-16px px-16px py-16px c-bg">
    <div class="h-full flex flex-col justify-between gap16px">
      <a-tabs>
        <template #rightExtra>
          <a-button type="primary" @click="onFinish"><template #icon><c-icon-save-outlined /></template>保存</a-button>
        </template>
        <a-tab-pane key="1" tab="基础配置">
          <a-form
            :model="formState"
            class="w-400px"
            :label-col="{ style: { width: '180px' } }"
          >
            <a-form-item
              label="产品批次有效期"
              name="batchValidityHours"
            >
              <a-input-number v-model:value="formState.batchValidityHours!">
                <template #addonAfter>小时</template>
              </a-input-number>
            </a-form-item>
            <a-form-item
              label="每日扫码次数限制"
              name="dailyScanLimit"
            >
              <a-input-number v-model:value="formState.dailyScanLimit!">
                <template #addonAfter>次</template>
              </a-input-number>
            </a-form-item>
            <a-form-item
              label="每日扫码地点移动范围"
              name="maxDailyScanDistanceKm"
            >
              <a-input-number v-model:value="formState.maxDailyScanDistanceKm!">
                <template #addonAfter>公里</template>
              </a-input-number>
            </a-form-item>
            <a-form-item
              label="是否强制获取用户设备ID"
              name="requireDeviceId"
            >
              <c-boolean-select v-model:value="formState.requireDeviceId" />
            </a-form-item>

            <a-form-item
              label="邀请用户设备ID重复封禁"
              name="blockInviteOnDuplicateDeviceId"
            >
              <c-boolean-select v-model:value="formState.blockInviteOnDuplicateDeviceId" />
            </a-form-item>
            <a-form-item
              label="允许扫码的国内特定区域"
              name="allowedRegions"
            >
              <AreaSelect v-model:value="formState.allowedRegions" />
            </a-form-item>

            <a-form-item
              label="禁用扫码的特定区域扫码"
              name="prohibitedRegions"
            >
              <AreaSelect v-model:value="formState.prohibitedRegions" />
            </a-form-item>
          </a-form>
        </a-tab-pane>
        <a-tab-pane key="2" tab="地理范围" force-render>
          <a-collapse v-model:active-key="collapseActiveKey" class="min-w300px">
            <a-collapse-panel key="1" header="仅允许的区域">
              <template #extra> 点击右侧地图添加区域</template>
              <ReuseAreaItem type="allowed" :data="allowedArea" />
            </a-collapse-panel>
            <a-collapse-panel key="2" header="禁止的区域">
              <template #extra> 点击右侧地图添加区域</template>
              <ReuseAreaItem type="prohibited" :data="prohibitedArea" />
            </a-collapse-panel>
          </a-collapse>
        </a-tab-pane>
      </a-tabs>
    </div>

    <div class="relative h-full max-h-full w-full">
      <div class="search-box">
        <c-cascader
          :options="cityJson"
          placeholder="请输入省份/城市" :field-names="{ label: 'name', value: 'name' }"
          class="w-full"
          @change="onSelectCity"
        />
        <div class="map-search">
          <c-input placeholder="请输入地点" @change="mapSearchByKeyWord" />
          <div class="res" :class="{ fold: mapSearchModel.fold }">
            <div v-for="(item, i) in mapSearchModel.results" :key="i" class="mb8px" @click="setSuggestion(item)">
              <h4>{{ item.title }}</h4>
              <p>{{ item.address }}</p>
            </div>
          </div>
          <template v-if="mapSearchModel.results">
            <div v-if="mapSearchModel.fold === false" class="res-fold" @click="mapSearchModel.fold = true">
              <c-icon-up-outlined />
            </div>
            <div v-if="mapSearchModel.fold === true" class="res-fold" @click="mapSearchModel.fold = false">
              <c-icon-down-outlined />
            </div>

            <div class="text-12px c-text-secondary">总数： {{ mapSearchModel.results.length }}</div>
          </template>
        </div>
      </div>
      <div :id="mapId" class="map h[calc(100%-10px)]" />
    </div>
  </div>
</template>

<script lang='ts' setup>
import type { AreaType } from './initMap'
import * as api from '@/api/'
import { GeoLocation, ScanProtection } from '@/api/models'
import { message } from 'ant-design-vue'
import { useId } from 'vue'
import region from '../../assets/region.json'
import AreaSelect from './components/AreaSelect.vue'
import { useInitMap } from './initMap'

definePage({
  meta: {
    title: '防盗刷配置',
  },
})

const [DefineAreaItem, ReuseAreaItem] = createReusableTemplate<{ data: Array<GeoLocation & { id: string }>, type: AreaType }>()

const cityJson = region.map(v => ({ ...v, children: v.children?.map(p => ({ ...p, children: [] })) }))

const collapseActiveKey = ref(['1', '2'])

const formState = ref(new ScanProtection())

const allowedArea = ref<Array<GeoLocation & { id: string }>>([])

const prohibitedArea = ref<Array<GeoLocation & { id: string }>>([])

const currentGeoType = ref<AreaType>('allowed')

const currentGeo = ref<GeoLocation & { id: string }>()

const {
  init: initMap,
  mapId,
  mapSearchByRegion,
  mapSearchModel,
  initMask,
  addMask,
  updateMask,
  mapSearchByKeyWord,
  setSuggestion,
  getDistrict,
  setActiveCircle,
  removeByid,
} = useInitMap(useId, { onSuggestionChange: (item, type) => {
  currentGeoType.value = type
  const t = (currentGeoType.value === 'allowed' ? allowedArea : prohibitedArea)
  t.value.unshift({
    ...new GeoLocation(),
    id: Date.now().toString(),
    latitude: Number(item.location.lat.toFixed(6)),
    longitude: Number(item.location.lng.toFixed(6)),
    radiusKm: 100,
    address: item.address ?? '',
  })
  currentGeo.value = t.value[0]
  addMask(currentGeo.value!, currentGeoType.value)
  setActiveCircle(currentGeo.value!, type)
}, onClick: (location) => {
  const data = { id: Date.now().toString() } as any
  data.location = location
  setSuggestion(data)
} })

function onSelectCity([state, city]: [string, string]) {
  mapSearchByRegion(city)
  getDistrict([state, city])
}

function selectArea(item: GeoLocation & { id: string }, type: AreaType) {
  currentGeoType.value = type
  currentGeo.value = item
  setActiveCircle(item, type)
}

function areaChange(item: GeoLocation & { id: string }) {
  currentGeo.value = item
  updateMask(item, currentGeoType.value)
}

function removSanGeoRestriction(key: 'allowed' | 'prohibited', i: number) {
  currentGeoType.value = key
  const t = (currentGeoType.value === 'allowed' ? allowedArea : prohibitedArea)
  removeByid(t.value[i]!.id, currentGeoType.value)
  t.value.splice(i, 1)
}

async function onFinish() {
  formState.value.allowedArea = allowedArea.value.map(v => ({ ...v, id: undefined }))
  formState.value.prohibitedArea = prohibitedArea.value.map(v => ({ ...v, id: undefined }))
  await api.SystemBaseInfo.SaveScanProtection_PostAsync(formState.value)
  message.success('保存成功')
}

onMounted(async () => {
  await initMap()
  api.SystemBaseInfo.GetScanProtectionAsync().then((res) => {
    formState.value = res || new ScanProtection()
    allowedArea.value = formState.value.allowedArea?.map(v => ({ ...v, id: Date.now().toString() })) ?? []
    prohibitedArea.value = formState.value.prohibitedArea?.map(v => ({ ...v, id: Date.now().toString() })) ?? []
    initMask(allowedArea.value, prohibitedArea.value)
  })
})
</script>

<style scoped lang="less">
.search-box {
  position: absolute;
  left: 20px;
  top: 10px;
  z-index: 1001;
  display: flex;
  flex-direction: column;
  gap: 8px;
  .map-search {
    padding: 10px;
    box-shadow:
      0 2px 5px -1px rgba(50, 50, 93, 0.25),
      0 1px 3px -1px rgba(0, 0, 0, 0.3);
    border-radius: 6px;
    background: @colorBgContainer;

    .res {
      margin-top: 10px;
      max-height: calc(100vh - 380px);
      overflow: auto;
      transition: all 0.3s;
      h4 {
        margin: 0;
      }
      p {
        margin-bottom: 4px;
        color: rgb(94, 94, 94);
      }
      > div {
        cursor: pointer;
      }
      > div:hover {
        background: @colorPrimaryBgHover;
      }
    }
    .fold {
      height: 0;
      overflow: hidden;
    }
    .res-fold {
      width: 100%;
      text-align: center;
      cursor: pointer;
    }
    .res-fold:hover {
      background: @colorPrimaryBgHover;
    }
  }
}
</style>
