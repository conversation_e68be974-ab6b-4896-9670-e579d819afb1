import type { ECBasicOption } from 'echarts/types/dist/shared'
import echars from '@/utils/commonEcharts'

/**
 *  [lineChartRef,, initECharts]
 * or  {domRef,, init}
 * @param refKey
 * @returns
 */
export function useECharts(refKey: string, option?: { immediate?: boolean }) {
  const domRef = useTemplateRef<HTMLDivElement>(refKey)

  let chart: echars.ECharts | null = null

  function update<Opt extends ECBasicOption>(option: Opt) {
    chart?.setOption(option)
  }

  function init(option?: echarts.EChartsOption) {
    chart = echars.init(domRef.value)
    if (option)
      update(option)
  }

  onMounted(() => {
    option?.immediate && init()
  })

  return makeDestructurable(
    { domRef, chart, init, update } as const,
    [domRef, chart, init, update] as const,
  )
}
