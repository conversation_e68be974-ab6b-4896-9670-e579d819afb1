<!--
 * @Description:
 * @Author: 景 彡
 * @Date: 2024-12-02 10:15:11
 * @LastEditors: 景 彡
-->
<template>
  <a-drawer v-model:open="modalState" :mask-closable="false" destroy-on-close width="800px" placement="right">
    <a-spin :spinning="spinner">
      <c-pro-form
        ref="proFormRef"
        v-model:value="model"
        :read-only="readOnly"
        :fields="fields"
        :descriptions="{ column: 1, bordered: true, labelStyle: { width: '146px' } }"
      />
    </a-spin>
    <template v-if="!readOnly" #extra>
      <a-button style="margin-right: 8px" @click="onClose">取消</a-button>
      <a-button type="primary" @click="save()">保存</a-button>
    </template>
  </a-drawer>
</template>

<script setup lang="ts">
import type { FormField } from 'ch2-components/types/pro-form/types'
import * as api from '@/api/'
import { ProductActivitiesEditModel, ProductActivitiesStatus } from '@/api/models'
import { Guid } from '@/utils/GUID'
import { message, Modal } from 'ant-design-vue'

const props = defineProps<{ readOnly?: boolean, id?: GUID }>()

const emit = defineEmits(['change'])

const modalState = defineModel('open', { default: false })

const model = ref(new ProductActivitiesEditModel())

const proFormRef = useTemplateRef('proFormRef')

watch(() => props.id, () => {
  const controller = new AbortController()
  if (Guid.isNotNull(props.id)) {
    api.Products.GetActivitiesAsync({ id: props.id! }, { signal: controller.signal }).then((res) => {
      model.value = { ...res, items: res.items?.map(v => v.id) }
    })
  }
  onWatcherCleanup(() => {
    controller.abort()
  })
}, { immediate: true })

const fields = computed<FormField[]>(() => [
  { label: '活动名称', el: 'input', prop: 'name', required: true, attrs: { placeholder: '请输入活动名称' }, formItem: {} },
  { label: '积分倍率', el: 'input-number', prop: 'pointsRate', required: true, attrs: { placeholder: '请输入积分倍率' }, formItem: {} },
  { label: '返现倍率', el: 'input-number', prop: 'cashbackRate', required: true, attrs: { placeholder: '请输入返现倍率' }, formItem: {} },
  { label: '描述', el: 'textarea', prop: 'description', required: true, attrs: { placeholder: '请输入描述' }, formItem: {} },
  { label: '活动状态', el: 'enum-select', prop: 'status', required: true, attrs: { placeholder: '请选择活动状态', enum: ProductActivitiesStatus }, formItem: {} },
  { label: '涉及全部产品', el: 'boolean-select', prop: 'all', required: true, attrs: { placeholder: '请选择', onChange() {
    if (model.value.all) {
      model.value.items = []
    }
  } }, formItem: {} },
  { label: '参与活动的商品', el: 'select', prop: 'items', isShow: model.value.all === false, attrs: {
    placeholder: '请输入商品名称',
    api: (...agrs) => tableSearch(api.Products.Query_PostAsync, agrs),
    page: true,
    fieldNames: { label: 'name', value: 'id' },
    showSearch: true,
    immediate: true,
    mode: 'multiple',
  }, formItem: { rules: [{
    required: model.value.all !== true,
    validator: FormValidator('guidReg', '商品名称不能为空'),
    trigger: 'blur',
  }] } },
  { label: '开始时间', el: 'date-picker', prop: 'upTime', required: true, attrs: { placeholder: '请选择开始时间', showTime: true }, formItem: {} },
  { label: '结束时间', el: 'date-picker', prop: 'downTime', required: true, attrs: { placeholder: '请选择结束时间', showTime: true }, formItem: {} },
  { label: '抽奖活动', el: 'select', prop: 'lotteryActivityId', attrs: {
    placeholder: '请输入抽奖活动',
    api: (...agrs) => tableSearch(api.Lotterys.LotteryActivityPage_PostAsync, agrs),
    page: true,
    fieldNames: { label: 'name', value: 'id' },
    showSearch: true,
    immediate: true,
  }, formItem: { rules: [{
    required: true,
    validator: FormValidator('guidReg', '抽奖活动不能为空'),
    trigger: 'blur',
  }] } },
])

function onClose() {
  modalState.value = false
}

const { run: save, spinner } = useLoading(async () => {
  await proFormRef.value?.validate()
  if (!model.value.all && model.value.items?.length && model.value.items?.length <= 0) {
    return Modal.warning({
      title: '提示',
      content: '参与活动的商品必填！',
    })
  }
  return (Guid.isNotNull(model.value.id) ? api.Products.UpdateActivities_PostAsync : api.Products.AddActivities_PostAsync)({ ...model.value }).then(() => {
    modalState.value = false
    emit('change')
    message.success(model.value.id ? '更新成功' : '创建成功')
  })
},

)
</script>
