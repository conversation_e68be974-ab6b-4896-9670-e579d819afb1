import { ProductActivitiesStatus } from "./ProductActivitiesStatus";
import { Product } from "./Product";
import { LotteryActivity } from "./LotteryActivity";
/**产品活动*/
export class ProductActivities {
  /**活动名称*/
  name?: string | null | undefined = null;
  /**活动描述*/
  description?: string | null | undefined = null;
  /**积分倍率*/
  pointsRate: number = 0;
  /**返现倍率*/
  cashbackRate: number = 0;
  /**活动状态*/
  status: ProductActivitiesStatus = 0;
  /**涉及全部产品*/
  all: boolean = false;
  /**开始时间*/
  upTime: Dayjs = dayjs();
  /**结束时间*/
  downTime: Dayjs = dayjs();
  /**参与活动的商品*/
  items?: Product[] | null | undefined = [];
  /**抽奖活动*/
  lotteryActivityId?: GUID = null;
  /**抽奖活动*/
  lotteryActivity?: LotteryActivity | null | undefined = null;
  /**软删除标记*/
  softDeleted: boolean = false;
  /**软删除者*/
  deletedBy?: string | null | undefined = null;
  /**软删除时间*/
  deleted?: Dayjs | null | undefined = null;
  /**创建时间*/
  created: Dayjs = dayjs();
  /**修改时间*/
  modified?: Dayjs | null | undefined = null;
  /**创建者*/
  createdBy?: string | null | undefined = null;
  /**修改者*/
  modifiedBy?: string | null | undefined = null;
  /**主键*/
  id: GUID = "00000000-0000-0000-0000-000000000000";
}
