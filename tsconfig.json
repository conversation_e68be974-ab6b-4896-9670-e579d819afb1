{
  "$schema": "https://json.schemastore.org/tsconfig",
  "extends": "@tsconfig/node-lts-strictest-esm/tsconfig.json",
  "compilerOptions": {
    "target": "ESNext",
    "jsx": "preserve",
    "jsxImportSource": "vue",
    "lib": [
      "DOM",
      "ESNext"
    ],
    "baseUrl": "./",
    "module": "ESNext",
    "moduleResolution": "Node",
    "paths": {
      "@/*": [
        "src/*"
      ]
    },
    "resolveJsonModule": true,
    "types": [
      "vite/client",
      "vite-plugin-vue-layouts/client"
    ],
    "allowJs": true,
    "strict": true,
    "strictNullChecks": true,
    "exactOptionalPropertyTypes": false,
    "noPropertyAccessFromIndexSignature": false,
    "noUnusedLocals": true,
    "declaration": true,
    "declarationDir": "types",
    "outDir": "dist",
    "esModuleInterop": true,
    "forceConsistentCasingInFileNames": true,
    "isolatedModules": true,
    "skipLibCheck": true,
    "ignoreDeprecations": "5.0"
  },
  "vueCompilerOptions": {
    "plugins": [
      "@vue-macros/volar/define-models",
      "@vue-macros/volar/define-slots"
    ]
  },
  "include": [
    "src/**/*.ts",
    "src/**/*.d.ts",
    "src/**/*.tsx",
    "src/**/*.vue",
    "tests/**/*.ts",
    "tests/**/*.tsx",
    "modules/**/*.js"
  ],
  "exclude": [
    "dist",
    "node_modules"
  ],
  "ts-node": {
    "esm": true // «———— 为 ts-node 启用 ESM
  }
}