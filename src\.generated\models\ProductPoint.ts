/**产品积分设置*/
export class ProductPoint {
  /**积分*/
  points: number = 0;
  /**返现*/
  cashback: number = 0;
  /**积分随机*/
  randomPoints: boolean = false;
  /**返现随机*/
  randomCashback: boolean = false;
  /**最小积分*/
  minPoints: number = 0;
  /**最大积分*/
  maxPoints: number = 0;
  /**最小返现*/
  minCashback: number = 0;
  /**最大返现*/
  maxCashback: number = 0;
  /**抽奖次数*/
  draws: number = 0;
  /**软删除标记*/
  softDeleted: boolean = false;
  /**软删除者*/
  deletedBy?: string | null | undefined = null;
  /**软删除时间*/
  deleted?: Dayjs | null | undefined = null;
  /**创建时间*/
  created: Dayjs = dayjs();
  /**修改时间*/
  modified?: Dayjs | null | undefined = null;
  /**创建者*/
  createdBy?: string | null | undefined = null;
  /**修改者*/
  modifiedBy?: string | null | undefined = null;
  /**主键*/
  id: GUID = "00000000-0000-0000-0000-000000000000";
}
