<template>
  <div class="login-container max-w-h-100vh max-w-100vw flex items-center overflow-hidden bg-#fff">
    <div class="relative h-100vh w-52%">
      <div class="absolute left-20px top-20px">
        <img src="../../assets/images/login-logo.png" alt="">
      </div>
      <img class="h-100% w-100%" src="../../assets/images/login-bg2.png" alt="">
    </div>

    <div class="center flex-1">
      <div class="w-380px">
        <div class="center flex-col">
          <div class="text-24px c-#009944 font-bold">欢迎登录系统</div>
          <div class="mt-16px c-[rgba(0,153,68,0.6)]">WELCOME</div>
        </div>
        <div class="mc-login-form mt-32px">
          <a-spin tip="登录中..." :spinning="loading">
            <a-tabs v-model:active-key="activeKey" centered>
              <a-tab-pane key="1" tab="密码登录">
                <a-form
                  :model="formState" name="basic" :wrapper-col="{ offset: 2, span: 20 }" autocomplete="off"
                  @finish="onFinish" @finish-failed="onFinishFailed"
                >
                  <a-form-item name="username" :rules="[{ required: true, message: '请输入用户名!' }]">
                    <c-input v-model:value="formState.username" style="height: 40px" placeholder="用户名">
                      <template #prefix>
                        <c-icon-user-outlined />
                      </template>
                    </c-input>
                  </a-form-item>

                  <a-form-item name="password" :rules="[{ required: true, message: '请输入密码!' }]">
                    <c-input-password v-model:value="formState.password" style="height: 40px" placeholder="密码">
                      <template #prefix>
                        <c-icon-lock-outlined />
                      </template>
                    </c-input-password>
                  </a-form-item>

                  <!-- <a-form-item :wrapper-col="{ offset: 2, span: 20 }">
                <a-row>
                  <a-col span="16">
                    <c-input v-model:value="formState.code" style="height: 40px" placeholder="验证码" />
                  </a-col>
                  <a-col span="8">
                    <img :src="base64" style="margin-left: 15px" @click="getCaptcha()">
                  </a-col>
                </a-row>
              </a-form-item> -->

                  <a-form-item :wrapper-col="{ offset: 2, span: 20 }">
                    <a-button
                      type="primary" html-type="submit" style="
                    width: 100%;
                    height: 40px;
                    border-radius: 30px;
                    background: #009944;
                  "
                    >
                      登录
                    </a-button>
                  </a-form-item>

                  <!-- <a-form-item :wrapper-col="{ span: 20 }">
                <a-row style="font-size: 1.1em">
                  <a-col offset="2" span="6">
                    <a-checkbox v-model:checked="remember">
                      记住密码
                    </a-checkbox>
                  </a-col>
                  <a-col offset="12" span="4">
                    <a>忘记密码</a>
                  </a-col>
                </a-row>
              </a-form-item> -->
                </a-form>
              </a-tab-pane>
              <!-- <a-tab-pane key="2" tab="微信登录">
                <div class="flex flex-col items-center justify-center">
                  <div> <c-icon-wechat-outlined style="color: #03E06E; margin-right: 8px" />使用微信扫一扫( <span class="c-#ff0000">功能开发中</span>)</div>
                  <div class="mt-16px">
                    <a-qrcode value="https://www.antdv.com/" />
                  </div>
                </div>
              </a-tab-pane> -->
            </a-tabs>
          </a-spin>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted } from 'vue'
import { useLogin } from './hook/useLogin'

const { formState, onFinish, onFinishFailed, loading } = useLogin()

const activeKey = ref('1')

onMounted(() => { })
</script>

<style scoped lang="less">
.ant-spin-container {
  background-color: none;
  opacity: 0;
}

.mc-login-form {
  :deep(.ant-tabs-nav) {
    font-size: 16px;
  }

  :deep(.ant-input-affix-wrapper) {
    border-radius: 30px;
  }
}

:deep(.ant-tabs-nav::before) {
  border: none;
}
</style>
