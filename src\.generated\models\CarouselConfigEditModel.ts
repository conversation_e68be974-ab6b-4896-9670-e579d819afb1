import { CarouselType } from "./CarouselType";
import { CarouselStatus } from "./CarouselStatus";
export class CarouselConfigEditModel {
  id: GUID = "00000000-0000-0000-0000-000000000000";
  image?: string | null | undefined = null;
  value1?: string | null | undefined = null;
  /**无长度限制，可以保存富文本内容*/
  value2?: string | null | undefined = null;
  /**类型*/
  type: CarouselType = 0;
  /**状态*/
  status: CarouselStatus = 0;
  /**排序*/
  sort: number = 0;
  /**次数上限*/
  count: number = 0;
  /**转发积分*/
  points: number = 0;
}
