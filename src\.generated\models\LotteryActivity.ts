import { LotteryPrizes } from "./LotteryPrizes";
import { LotteryActivityState } from "./LotteryActivityState";
import { LotteryActivityType } from "./LotteryActivityType";
/**抽奖活动*/
export class LotteryActivity {
  /**奖品*/
  prizes?: LotteryPrizes[] | null | undefined = [];
  /**保底奖品Id*/
  guaranteedPrizeId?: GUID = null;
  /**保底奖品*/
  guaranteedPrize?: LotteryPrizes | null | undefined = null;
  /**活动名称*/
  name?: string | null | undefined = null;
  /**图片*/
  images?: string | null | undefined = null;
  /**开始时间（每日抽奖不需要设置）*/
  start?: Dayjs | null | undefined = null;
  /**结束时间（每日抽奖不需要设置）*/
  end?: Dayjs | null | undefined = null;
  /**活动状态*/
  status: LotteryActivityState = 0;
  /**抽奖类型*/
  type: LotteryActivityType = 0;
  /**描述*/
  description?: string | null | undefined = null;
  /**模板*/
  template?: string | null | undefined = null;
  /**保底次数：在此次数内用户必须中保底奖品*/
  guaranteedDraws: number = 0;
  /**该活动初始赠送的抽奖次数
（后台：需要用户点击查看抽奖活动触发）*/
  initialDrawAttempts: number = 0;
  /**软删除标记*/
  softDeleted: boolean = false;
  /**软删除者*/
  deletedBy?: string | null | undefined = null;
  /**软删除时间*/
  deleted?: Dayjs | null | undefined = null;
  /**创建时间*/
  created: Dayjs = dayjs();
  /**修改时间*/
  modified?: Dayjs | null | undefined = null;
  /**创建者*/
  createdBy?: string | null | undefined = null;
  /**修改者*/
  modifiedBy?: string | null | undefined = null;
  /**主键*/
  id: GUID = "00000000-0000-0000-0000-000000000000";
}
