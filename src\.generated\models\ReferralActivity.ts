import { ReferralRewards } from "./ReferralRewards";
import { ReferralActivityStatus } from "./ReferralActivityStatus";
/**推广活动表*/
export class ReferralActivity {
  /**标题*/
  title?: string | null | undefined = null;
  /**图片*/
  image?: string | null | undefined = null;
  /**描述*/
  description?: string | null | undefined = null;
  /**详情（富文本）*/
  details?: string | null | undefined = null;
  /**激活返佣标准*/
  number: number = 0;
  /**一级返佣奖励*/
  firstLevel?: ReferralRewards | null | undefined = null;
  /**二级返佣奖励*/
  secondLevel?: ReferralRewards | null | undefined = null;
  /**活动状态*/
  status: ReferralActivityStatus = 0;
  /**开始时间（为空长期有效）*/
  start?: Dayjs | null | undefined = null;
  /**结束时间（为空长期有效）*/
  end?: Dayjs | null | undefined = null;
  /**软删除标记*/
  softDeleted: boolean = false;
  /**软删除者*/
  deletedBy?: string | null | undefined = null;
  /**软删除时间*/
  deleted?: Dayjs | null | undefined = null;
  /**创建时间*/
  created: Dayjs = dayjs();
  /**修改时间*/
  modified?: Dayjs | null | undefined = null;
  /**创建者*/
  createdBy?: string | null | undefined = null;
  /**修改者*/
  modifiedBy?: string | null | undefined = null;
  /**主键*/
  id: GUID = "00000000-0000-0000-0000-000000000000";
}
