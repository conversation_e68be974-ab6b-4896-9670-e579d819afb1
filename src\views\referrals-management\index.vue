<!--
 * @Description:
 * @Author: 景 彡
 * @Date: 2024-11-12 14:59:23
 * @LastEditors: 景 彡
-->
<template>
  <div class="h-full">
    <c-pro-table
      ref="tableRef"
      :columns="columns"
      align="center"
      :api="api.Referrals.GetReferralRecordPageView_PostAsync"
      row-key="id"
      immediate
    />
  </div>
</template>

<script setup lang="ts">
import type { ReferralRecordPageView } from '@/api/models'
import type { ColumnProps } from 'ch2-components/types/pro-table/types'
import * as api from '@/api/'
import { Decimal } from 'decimal.js'

definePage({
  meta: {
    title: '推广记录',
    layoutRoute: {
      meta: { layout: 'admin', title: '推广管理', local: true, icon: 'share-alt-outlined', order: 7 },
    },
  },
})

const tableRef = ref()

const columns = reactive<ColumnProps<ReferralRecordPageView>[]>([
  { dataIndex: 'activityName', title: '推广名称' },
  { dataIndex: 'activityKeyWord', title: '推广名称', isShow: false, search: { el: 'input', method: 'GET' }, align: 'left' },
  { dataIndex: 'userKeyWord', title: '用户名称', isShow: false, search: { el: 'input', method: 'GET' }, align: 'left' },
  { dataIndex: ['clientUserInfo', 'name'], title: '漆工名称', align: 'left' },
  { dataIndex: 'number', title: '推广人数' },
  { dataIndex: 'successes', title: '成功发放奖励数' },
  { dataIndex: 'failures', title: '未成功发放奖励数' },
  { dataIndex: ['rewards', 'cashback'], title: '获得金额（元）', bodyCell: ({ record }) => h('div', `${new Decimal(record.rewards?.cashback || 0).dividedBy(100).toNumber()} 元 `) },
  { dataIndex: ['rewards', 'points'], title: '获得积分' },
  { dataIndex: ['rewards', 'lotteryActivityCount'], title: '获得抽奖次数' },

])
</script>

<style scoped>

</style>
