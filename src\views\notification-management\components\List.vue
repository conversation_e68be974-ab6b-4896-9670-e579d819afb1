<!--
 * @Description:
 * @Author: 景 彡
 * @Date: 2024-11-04 09:24:20
 * @LastEditors: 景 彡
-->
<template>
  <c-list ref="listRef" :api="api.Notices.FindNotices_GetAsync" :get-params="searchForm" immediate pagination>
    <template #renderItem="{ item }">
      <a-list-item key="item.id">
        <a-list-item-meta>
          <template #title>
            <h3>
              <a href="#" class="list-color" @click="onEdit(item)">{{ item.title }}</a>
            </h3>
          </template>
          <template #description>
            <!-- <p class="list-color">
                {{ item.description }}
              </p> -->
            <p>
              <a-tag color="cyan">
                {{ NoticeStatus[item.status] }}
              </a-tag>
              <a-tag color="processing">
                {{ NoticeType[item.noticeType] }}
              </a-tag>
            </p>
            <p>
              <span>创建时间：{{
                dayjs(item.created).format("YYYY-MM-DD HH:mm:ss")
              }}</span>
              <a-divider type="vertical" />
              <span>发送时间：{{
                dayjs(item.sentTime).format("YYYY-MM-DD HH:mm:ss")
              }}</span>
              <!-- <span>{{ item.views }} 阅读</span> -->
              <!-- <a-divider type="vertical" />
              <a @click="preview(item.id)">预览</a> -->
            </p>
          </template>
        </a-list-item-meta>

        <ImageView v-if=" item.cover" :src="item.cover" width="200px" height="140px" />

        <template #actions>
          <a-dropdown>
            <a class="ant-dropdown-link" @click.prevent>
              更多<c-icon-down-outlined />
            </a>
            <template #overlay>
              <a-menu>
                <template v-if="item.status !== NoticeStatus.已发送">
                  <a-menu-item>
                    <a href="javascript:;" @click="onSend(item)">发送通知</a>
                  </a-menu-item>
                  <a-menu-item>
                    <a href="javascript:;" @click="onEdit(item)">编辑</a>
                  </a-menu-item>
                </template>

                <a-menu-item>
                  <a style="color: red;" @click="onDel(item)">删除</a>
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </template>
      </a-list-item>
    </template>
  </c-list>
</template>

<script lang='ts' setup>
import type { Notice } from '@/api/models'
// import type { NoticeType } from '@/api/models'
import * as api from '@/.generated/apis'
import { NoticeStatus, NoticeType } from '@/api/models'
import { message } from 'ant-design-vue'
import dayjs from 'dayjs'

interface SearchForm {
  keyword: string
  noticeType: NoticeType
}

defineProps<{ searchForm: SearchForm }>()

const emit = defineEmits<{
  (e: 'onEdit', id: string): void
}>()

const listRef = ref() // 编辑资讯列表ref

async function onSend(record: Notice) {
  await api.Notices.SendNotice_GetAsync({ noticeId: record.id })
  message.success('发送成功')
  listRef.value.refreshData()
}

// 操作资讯信息
async function onEdit(record: Notice) {
  emit('onEdit', record.id)
}

async function onDel(record: Notice) {
  await api.Notices.Removes_PostAsync([record.id])
  message.success('删除成功')
  listRef.value.refreshData()
}

function search() {
  listRef.value.fetchData()
}

function reSetForm() {
  listRef.value.fetchData()
}

defineExpose({
  search,
  reSetForm,
})
</script>

<style scoped>

</style>
