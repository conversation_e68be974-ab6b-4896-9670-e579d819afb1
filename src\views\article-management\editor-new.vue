<!--
 * @Description:编辑文章标题和正文内容
 * @Author: 景 彡
 * @Date: 2023-06-12 11:54:16
 * @LastEditors: 景 彡
 * @LastEditTime: 2025-07-30 15:14:02
-->
<template>
  <div class="box">
    <a-affix :offset-top="0">
      <div class="header-toolbar">
        <div class="editor-toolbar justify-center">
          <div ref="toolbar" />
          <div class="btn-box">
            <a-button @click="togglePreview">
              {{ showPreview ? '隐藏预览' : '显示预览' }}
            </a-button>
          </div>
        </div>
      </div>
    </a-affix>
    <div id="content" class="justify-center">
      <div id="editor-container" class="flex-1">
        <div id="title-container">
          <a-input v-model:value="informationData.title!" placeholder="输入文章标题..." />
        </div>
        <div ref="contentRef" class="editor-text-area" />
      </div>

      <!-- 预览面板 -->
      <div v-if="showPreview" class="preview-container flex-1">
        <div class="preview-header">
          <h3>实时预览</h3>
        </div>
        <div class="preview-content">
          <article class="article-preview">
            <!-- <h1 class="article-title">{{ informationData.title || '文章标题' }}</h1> -->
            <div class="editor-text-area editor-info-detail ch2-editor ck ck-content" v-html="previewContent" />
          </article>
        </div>
      </div>

      <div v-if="!showPreview" class="w-500px">
        <a-affix :offset-top="62" :style="{ zIndex: 1, minWidth: '500px', height: 'calc(100vh - 72px)' }">
          <PushArticle ref="saveRef" v-model:form-data="informationData" @clar="clar()" />
        </a-affix>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import * as api from '@/api'
import * as models from '@/api/models'
import { useEditor } from '@/hooks/useEditor'
import { message } from 'ant-design-vue'
import { useRouter } from 'vue-router'
import PushArticle from './components/PushArticle.vue'

definePage({
  meta: {
    title: '编辑/新增文章',
    hidden: true,
    layout: false,
  },
})

const router = useRouter ()

useBlockClosure()

const { toolbar, contentRef, install, editor, onAttrs } = useEditor()

const informationData = ref<models.Information>(new models.Information())

const saveRef = ref<InstanceType<typeof PushArticle>>()

async function getArticleData(informationId: string) {
  try {
    informationData.value = await api.Informations.GetByInformationIdAsync({ informationId })
    editor.value?.setData({ content: replaceImgSrc(informationData.value.content || '') })
  }
  catch (error: any) {
    message.error(error.message)
  }
}

onAttrs.change = () => {
  informationData.value.content = cleanEditorHtml(editor.value?.getData({ rootName: 'content' }) || '')
}

onMounted(async () => {
  await install()
  router.currentRoute.value.query.informationId && getArticleData(router.currentRoute.value.query.informationId.toString())
})

function useBlockClosure() {
  function onbeforeunload(e: { returnValue: string }) {
    const dialogText = 'close'
    e.returnValue = dialogText
    return dialogText
  }

  onUnmounted(() => {
    window.removeEventListener('beforeunload', onbeforeunload)
  })

  onMounted(async () => {
    window.addEventListener('beforeunload', onbeforeunload)
  })
}

// 添加预览相关的响应式数据
const showPreview = ref(false)
const previewContent = ref('')

// 修改 onAttrs.change 以支持实时预览
onAttrs.change = () => {
  const content = cleanEditorHtml(editor.value?.getData({ rootName: 'content' }) || '')
  console.log(content)
  informationData.value.content = content
  // 更新预览内容
  previewContent.value = replaceImgSrc(content)
  console.log(previewContent.value)
}

// 切换预览显示
function togglePreview() {
  showPreview.value = !showPreview.value
  if (showPreview.value) {
    // 显示预览时更新内容
    previewContent.value = replaceImgSrc(informationData.value.content || '')
  }
}

function clar() {
  editor.value?.setData({ content: replaceImgSrc(informationData.value.content || '') })
  previewContent.value = replaceImgSrc(informationData.value.content || '')
}

// 修改 clar 函数以同步预览
// function clar() {
//   editor.value?.setData({ content: replaceImgSrc(informationData.value.content || '') })
//   previewContent.value = replaceImgSrc(informationData.value.content || '')
// }
</script>

<style scoped lang="less">
html,
body {
  height: 100%;
  overflow-y: hidden;
  overflow-x: hidden;
  color: #333;
}
.box {
  height: 100%;
  width: 100%;
  margin: auto;
}
.header-toolbar {
  width: 100%;
  border-bottom: 1px solid #e8e8e8;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.12);
  .editor-toolbar {
    background-color: #fcfcfc;
    display: flex;
    :deep(.w-e-drop-panel) {
      z-index: 99999;
    }
    .btn-box {
      margin-left: 10px;
      height: 40px;
      line-height: 40px;
    }
  }
}

#title-container {
  padding: 20px 0;
  border-bottom: 1px solid #e8e8e8;
  padding: 15px;
}

#title-container input {
  font-size: 30px;
  border: 0;
  outline: none;
  width: 100%;
  line-height: 1;
}

.editor-text-area {
  min-height: 100vh;
  height: fit-content !important;
}
:deep(.ant-input:focus, .ant-input-focused) {
  box-shadow: none;
}
:deep(.w-e-bar-item-group .w-e-bar-item-menus-container) {
  z-index: 22;
}

#content {
  width: 100%;
  background-color: rgb(245, 245, 245);
  position: relative;
  display: flex;
}

#editor-container {
  max-width: 1200px;
  flex: 1;
  margin: 0 20px;
  background-color: #fff;
  border: 1px solid #e8e8e8;
  box-shadow: 0 2px 10px rgb(0 0 0 / 12%);
  border-radius: 4px;

  &.with-preview {
    flex: 0 0 50%;
    margin-right: 0;
  }

  :deep(table) {
    min-width: 50px !important;
  }
}

.preview-container {
  // flex: 0 0 50%;
  background-color: #fff;
  border: 1px solid #e8e8e8;
  box-shadow: 0 2px 10px rgb(0 0 0 / 12%);
  border-radius: 4px;
  margin-right: 20px;
  overflow: hidden;

  .preview-header {
    padding: 15px;
    border-bottom: 1px solid #e8e8e8;
    background-color: #fafafa;

    h3 {
      margin: 0;
      font-size: 16px;
      color: #333;
    }
  }

  .preview-content {
    // height: calc(100vh - 120px);
    overflow-y: auto;
    padding: 20px;
  }

  .article-preview {
    .article-title {
      font-size: 28px;
      font-weight: bold;
      margin-bottom: 20px;
      color: #333;
      line-height: 1.3;
    }

    .article-body {
      line-height: 2;
      // color: #555;

      :deep(img) {
        max-width: 100%;
        height: auto;
        border-radius: 4px;
        margin: 10px 0;
      }

      :deep(p) {
        margin-bottom: 16px;
      }

      :deep(h1, h2, h3, h4, h5, h6) {
        margin: 20px 0 10px 0;
        // color: #333;
      }
    }
  }
}

.btn-box {
  margin-left: 10px;
  height: 40px;
  line-height: 40px;
}
</style>
