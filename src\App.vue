<!--
 * @Description:
 * @Author: luckymiaow
 * @Date: 2022-04-15 16:15:09
 * @LastEditors: 景 彡
-->

<template>
  <c-config-provider :theme="themeProvider" :author-img="authorImg" :select="{ runApi, filterOption }" :locale="zhCN" :modal="{ maskClosable: false }">
    <router-view />
  </c-config-provider>
</template>

<script lang="ts" setup>
import { theme } from 'ant-design-vue'
import zhCN from 'ant-design-vue/es/locale/zh_CN'
import dayjs from 'dayjs'
import { computed } from 'vue'
import { useAppStore, useUserStore } from './stores'
import { useToken } from './theme'
import { useUnocssPluginConfig } from './theme/useUnocssPluginConfig'
import 'dayjs/locale/zh-cn'

const { token } = useToken()

useUnocssPluginConfig(token)

const { token: userToken } = useUserStore()

const app = useAppStore()

dayjs.locale('zh_CN')

const themeProvider = computed(() => {
  return {
    token: { ...app.themeConfig.themeColor, zIndexPopupBase: 1002 },
    algorithm: app.themeConfig.isDark ? theme.darkAlgorithm : theme.defaultAlgorithm,

  }
})

const authorImg = computed(() => {
  return {
    authorization: `Bearer ${userToken}`,
  }
})

// const options = ref(new Map<string, any>())

async function runApi(api: any, _refresh = false, arges: any, _fnId: string) {
  // console.log('%c [ fnId ]-50', 'font-size:13px; background:pink; color:#bf2c9f;', fnId)
  // let data
  // if (refresh || !options.value.has(fnId)) {
  //   data = await api(...arges)
  //   options.value.set(fnId, data)
  // }
  // else {
  //   data = options.value.get(fnId)
  //   console.log('%c [ 缓存 ]-58', 'font-size:13px; background:pink; color:#bf2c9f;', data)
  // }
  // console.log('%c [ options ]-48', 'font-size:13px; background:pink; color:#bf2c9f;', options)
  // return data

  return api(...arges)
}

function filterOption(input: string, option: any) {
  return option.label.toLowerCase().includes(input.toLowerCase())
}
</script>

<style>
#app {
  height: 100%;
  background-color: #eff1f4;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 全局设置modal溢出滚动 */
.ant-modal-body {
  overflow: auto;
}

:root {
  --ck-z-default: 99;
}
/* .ch2-editor figure {
  width: 100% !important;
} */
</style>
