import { ProductStatus } from "./ProductStatus";
export class ProductEditModel {
  id: GUID = "00000000-0000-0000-0000-000000000000";
  /**产品名称*/
  name?: string | null | undefined = null;
  /**描述（富文本）*/
  description?: string | null | undefined = null;
  /**特点*/
  feature?: string | null | undefined = null;
  /**规格（18kg/桶）*/
  specs?: string | null | undefined = null;
  /**详情（富文本）*/
  details?: string | null | undefined = null;
  /**图片*/
  image?: string | null | undefined = null;
  /**价格*/
  price: number = 0;
  /**产品状态*/
  status: ProductStatus = 0;
  /**产品类型*/
  typeId: GUID = "00000000-0000-0000-0000-000000000000";
  /**产品生产编号*/
  productionNumber?: string | null | undefined = null;
}
