import { IdentityUser } from "./IdentityUser";
import { UserRole } from "./UserRole";
import { Role } from "./Role";
/**用户*/
export class User extends IdentityUser<string> {
  /**用户角色关联表*/
  userRoles?: UserRole[] | null | undefined = [];
  /**用户角色*/
  roles?: Role[] | null | undefined = [];
  /**软删除标记*/
  softDeleted: boolean = false;
  /**软删除者*/
  deletedBy?: string | null | undefined = null;
  /**软删除时间*/
  deleted?: Dayjs | null | undefined = null;
  /**创建时间*/
  created: Dayjs = dayjs();
  /**修改时间*/
  modified?: Dayjs | null | undefined = null;
  /**创建者*/
  createdBy?: string | null | undefined = null;
  /**修改者*/
  modifiedBy?: string | null | undefined = null;
}
