import { PointsCommodityStatus } from "./PointsCommodityStatus";
import { InitialMode } from "./InitialMode";
/**积分商品表*/
export class PointsCommodity {
  /**商品标题*/
  title?: string | null | undefined = null;
  /**图片（Path*/
  image?: string | null | undefined = null;
  /**描述*/
  description?: string | null | undefined = null;
  /**详情（富文本）*/
  details?: string | null | undefined = null;
  /**规格（18kg/桶）*/
  specs?: string | null | undefined = null;
  /**积分价格*/
  points: number = 0;
  /**库存*/
  stock: number = 0;
  /**库存警戒值*/
  stockAlertThreshold?: number | null | undefined = null;
  /**是否触发库存警戒（后台管理）*/
  isAlert: boolean = false;
  /**是否推荐*/
  isRecommend: boolean = false;
  /**排序*/
  order: number = 0;
  /**商品状态*/
  status: PointsCommodityStatus = 0;
  /**上架时间*/
  shelfTime?: Dayjs | null | undefined = null;
  /**下架时间（可选）*/
  removeTime?: Dayjs | null | undefined = null;
  /**处理模式*/
  immediateMode: InitialMode = 0;
  /**软删除标记*/
  softDeleted: boolean = false;
  /**软删除者*/
  deletedBy?: string | null | undefined = null;
  /**软删除时间*/
  deleted?: Dayjs | null | undefined = null;
  /**创建时间*/
  created: Dayjs = dayjs();
  /**修改时间*/
  modified?: Dayjs | null | undefined = null;
  /**创建者*/
  createdBy?: string | null | undefined = null;
  /**修改者*/
  modifiedBy?: string | null | undefined = null;
  /**主键*/
  id: GUID = "00000000-0000-0000-0000-000000000000";
}
