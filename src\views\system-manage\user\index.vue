<template>
  <c-pro-table
    ref="tableRef" :api="api.UserManage.GetUserListAsync" :columns="columns" :row-selection="rowSelections"
    :serial-number="true" row-key="id" immediate bordered
  >
    <template #header>
      <a-button type="primary" @click="operation('新增', null)">
        <PlusSquareOutlined />
        新增用户
      </a-button>
    </template>

    <template #bodyCell="{ record, column }">
      <template v-if="column.dataIndex === 'roles'" style="float:left">
        <template v-if="record.roles.length > 0">
          <template v-for="(item, index) in record.roles" :key="index">
            <a-tag style="font-size: 1.1em;" closable color="#87d068" @close="handleClose(record.id, item)">
              {{ item.name
              }}
            </a-tag>
          </template>
        </template>
        <template v-else />
        <a-button type="dashed" danger style="margin-top: 5px" @click="editRole(record)">
          <PlusOutlined /> 添加角色
        </a-button>
      </template>

      <template v-if="column.dataIndex === 'lockoutEnabled'" style="float:left">
        <a-switch :checked="record.lockoutEnabled" checked-children="启用" un-checked-children="锁定" :loading="changeSateSpinner" @change="onChangeSate(record)" />
      </template>

      <template v-if="column.dataIndex === 'operation'">
        <span>
          <a @click="operation('查看', record.id)">查看</a>
          <a-divider type="vertical" />

          <a-dropdown>
            <a class="ant-dropdown-link" @click.prevent>
              更多
              <DownOutlined />
            </a>
            <template #overlay>
              <a-menu>
                <a-menu-item>
                  <a @click="operation('编辑', record.id)">编辑用户</a>
                </a-menu-item>
                <a-menu-item>
                  <a @click="editPassword(record.id)">修改密码</a>
                </a-menu-item>
                <!-- <a-menu-item @click="simulationLogin(record.id)">
                  <a>模拟用户</a>
                </a-menu-item> -->
                <a-menu-item>
                  <a-popconfirm title="确认删除吗?" @confirm="del({ userId: record.id })">
                    <a style="color: red">删除用户</a>
                  </a-popconfirm>
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </span>
      </template>
    </template>
  </c-pro-table>
  <a-modal v-model:open="roleModelVisible" width="40%" title="添加角色" @ok="roleHandleOk">
    <a-select
      v-model:value="selectRole" mode="tags" style="width: 100%" placeholder="请选择角色"
      :options="options"
    />
  </a-modal>

  <a-modal v-model:open="passwordModelVisible" width="30%" title="修改密码" @ok="passwordHandleOk">
    <a-input v-model:value="password" style="width: 98%" placeholder="请输入登录密码" />
    <a-progress style="width: 95%" :percent="currentStep" :steps="step" :stroke-color="stepColor" :format="strength" />
  </a-modal>
  <Details ref="detailsRef" />
</template>

<script setup lang="ts">
import type { SelectProps } from 'ant-design-vue'
import type { ColumnProps } from 'ch2-components/types/pro-table/types'
import * as api from '@/api'
import * as models from '@/api/models'
import {
  DownOutlined,
  PlusOutlined,
  PlusSquareOutlined,
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import Details from './components/details.vue'

definePage({
  meta: {
    title: '用户管理',
    local: true,
  },
})

const roleModelVisible = ref<boolean>(false)
const passwordModelVisible = ref<boolean>(false)

const tableRef = ref()
const detailsRef = ref()
const options = ref<SelectProps['options']>([])
const selectRole = ref<SelectProps['options']>([])
const currentUserId = ref()
const password = ref<string>('')

// 步骤条组件总步数
const step = ref(5)

// 当前进度
const currentStep = ref<number>(0)

// 根据进度步骤条分别显示不同颜色
const stepColor = computed(() => (currentStep.value <= 40 ? 'red' : currentStep.value <= 80 ? '#f75300' : '#29f700'))
// 步骤条文本数组
const strengthArr = ref(['非常弱', '弱', '中等', '强', '非常强'])
// 步骤条当前文本
function strength() {
  return strengthArr.value[currentStep.value / 20 - 1]
}

/**
 * @function 监听事件
 * @description 监听新密码变化控制进度条
 */
watch(
  () => password.value,
  (newValue) => {
    const check = chackPasswordStrength(newValue?.toString())
    if (newValue && newValue.length > 0)
      currentStep.value = (check + 1) * 20
    else
      currentStep.value = 0
  },
)

/**
 * @function 检查密码复杂度
 * @param password 新密码
 * @description 判断密码强度，分5个等级
 */
function chackPasswordStrength(password: string | any) {
  const patterns = [/\d/, /[a-z]/, /[A-Z]/, /[^a-z\d]/i]
  const count = patterns.reduce((acc, pattern) => acc + Number(pattern.test(password)), 0)
  const len = password.length
  return len < 8 ? 0 : len >= 16 && count >= 3 ? 4 : count < 3 ? (len < 12 ? 1 : 3) : len < 12 ? 2 : 3
}

const rowSelections = ref({
  selectedRowKeys: [] as number[],
  onChange: (keys: number[]) => {
    rowSelections.value.selectedRowKeys = keys
  },
})

const columns = ref<ColumnProps<models.UserViewModel>[]>([
  {
    dataIndex: 'name',
    title: '名称',
    key: 'name',
    align: 'center',
    width: '200px',
  },
  {
    dataIndex: 'userName',
    title: '登录账号',
    key: 'userName',
    align: 'center',
    width: '200px',
    search: {
      el: 'input',
      method: 'GET',
      attrs: { placeholder: '请输入登录账号' },
    },
  },
  {
    dataIndex: 'phoneNumber',
    title: '手机号码',
    key: 'phoneNumber',
    align: 'center',
    width: '200px',
  },
  {
    dataIndex: 'lockoutEnabled',
    title: '状态',
    key: 'lockoutEnabled',
    align: 'center',
    width: '200px',
  },
  {
    dataIndex: 'roles',
    title: '角色',
    key: 'roles',
    align: 'center',
    width: '300px',
  },
  {
    dataIndex: 'operation',
    title: '操作',
    key: 'operation',
    align: 'center',
    width: '200px',
  },
])

function operation(action: string, id: number | any) {
  detailsRef.value.acceptParams({
    visible: true,
    type: action,
    currentId: id as unknown as number,
    title:
      action === '编辑'
        ? '编辑用户信息'
        : action === '新增'
          ? '新增用户'
          : '查看用户详情',
    readonly: action === '查看',
    callback: action !== '查看' ? tableRef.value!.refresh : () => { },
  })
}

const { run: del } = useLoading(api.UserManage.DeleteUser_PostAsync, { callback: () => {
  tableRef.value.refresh()
} })

const { run: onChangeSate, spinner: changeSateSpinner } = useLoading((record: models.UserViewModel) => {
  return (record.lockoutEnabled ? api.UserManage.UnLockUser_GetAsync : api.UserManage.LockUser_GetAsync)({ userId: record.id }).then((res) => {
    if (res) {
      record.lockoutEnabled = !record.lockoutEnabled
      message.success(`${record.lockoutEnabled ? '解锁' : '锁定'}成功`)
    }
    else {
      message.error(`${record.lockoutEnabled ? '解锁' : '锁定'}失败`)
    }
  })
})

async function handleClose(id: string, role: models.Role) {
  await api.RolesManage.RemoveUserRole_PostAsync({ userId: id, roleId: role.id as string }).then((res) => {
    if (res) {
      tableRef.value.refresh()
    }
  })
}

function roleHandleOk() {
  selectRole.value?.forEach(async (p) => {
    await api.RolesManage.SetUserRole_PostAsync({
      userId: currentUserId.value,
      roleId: p as unknown as string,
      expirationTime: undefined,
    }).then((res) => {
      if (res) {
        tableRef.value.refresh()
        roleModelVisible.value = false
      }
    })
  })
}

function editPassword(id: string) {
  passwordModelVisible.value = true
  currentUserId.value = id
}

async function passwordHandleOk() {
  const data = new models.UserPasswordChangeEditModel()
  data.id = currentUserId.value
  data.password = password.value
  await api.UserManage.EditUserPassword_PostAsync(data).then((res) => {
    if (res) {
      message.success('修改成功')
      tableRef.value.refresh()
      passwordModelVisible.value = false
    }
  })
}

function editRole(user: models.UserViewModel) {
  roleModelVisible.value = true
  options.value = []
  currentUserId.value = user.id
  api.RolesManage.RoleAll_PostAsync({ roleName: '' }).then((res) => {
    if (res) {
      res.forEach((p) => {
        options.value?.push({
          value: p.id,
          label: p.name,
        })
      })
    }
  })
  selectRole.value = []
  user?.roles?.forEach((p) => {
    selectRole.value?.push({
      value: p.id,
      label: p.name,
    })
  })
}
</script>

<style scoped>
:deep(.ant-progress-steps-item) {
  min-width: 18%;
}
</style>
