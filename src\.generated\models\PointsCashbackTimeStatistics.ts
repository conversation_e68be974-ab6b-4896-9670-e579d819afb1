import { UserTotalStatistics } from "./UserTotalStatistics";
import { UserActivityLog } from "./UserActivityLog";
/**积分红包时间段统计*/
export class PointsCashbackTimeStatistics {
  /**返现排行榜*/
  cashbackRecordTotal?: UserTotalStatistics[] | null | undefined = [];
  /**积分排行榜*/
  pointTransactionTotal?: UserTotalStatistics[] | null | undefined = [];
  /**邀请排行榜*/
  referralRecordsTotal?: UserTotalStatistics[] | null | undefined = [];
  /**返现记录*/
  cashbackRecordLog?: UserActivityLog[] | null | undefined = [];
  /**积分记录*/
  pointTransactionLog?: UserActivityLog[] | null | undefined = [];
  /**邀请记录*/
  referralRecordLog?: UserActivityLog[] | null | undefined = [];
  /**总返现*/
  cashbackRecord: number = 0;
  /**总积分*/
  pointTransaction: number = 0;
  /**总邀请*/
  referralRecord: number = 0;
}
