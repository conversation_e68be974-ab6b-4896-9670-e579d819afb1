<template>
  <c-image
    :src="joinFilePathById(src, 'resize,p_50')"
    :preview="previewObj"
  >
    <template v-for="(_, key) in slots" :key="key" #[key]="scope">
      <slot :name="key" v-bind="scope ?? {}" />
    </template>
  </c-image>
</template>

<script setup lang="ts">
const props = defineProps<{ /* id */src: string, preview?: any }>()

const slots = useSlots()

const previewObj = computed(() => {
  if (props.preview && props.preview !== true)
    return props.preview
  return {
    src: joinFilePathById(props.src),
  }
})
</script>

<style scoped>

</style>
