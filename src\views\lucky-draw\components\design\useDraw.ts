import { LotteryActivity, LotteryPrizes } from '@/api/models'
import { Modal } from 'ant-design-vue'
import { fetchApi, type VariableMap } from './utils'

export function useLuckDraw(option?: {
  success: () => void
}) {
  const curIndex = ref(-1) // 当前位置

  const curStep = ref(0) // 已跑步数

  const speed = ref(0) // 转动速度

  const isRunning = ref(false) // 是否在抽奖

  const lotterySort = [0, 1, 2, 5, 8, 7, 6, 3] // 顺时针转动顺序

  const baseCircles = ref(3) // 跑动圈数

  const winId = ref(6) // 中奖id

  const initSpeed = ref(300) // 抽奖初始转动速度

  const fastSpeed = ref(100) // 抽奖最快转动速度

  const slowSpeed = ref(600) // 抽奖最慢转动速度

  /**
   * 抽奖停止位置，修改他可以使转盘停止在指定的位置
   */
  const winningAPrize = ref(-1) // -1未开始

  /**
   * 总跑动步数
   * 总步数=基本圈数*8+中奖格子在顺时针跑动数组中的位置
   */
  const totalSteps = computed(() => {
    return baseCircles.value * 8 + lotterySort.indexOf(winId.value)
  })

  const timer: Ref<ReturnType<typeof setTimeout> | null> = ref(null)

  /**
   * 开始抽奖
   */
  function begin() {
  // popup.value.open()
    if (!isRunning.value) {
      isRunning.value = true
      curStep.value = 0
      speed.value = initSpeed.value
      startRun()
    }
  }

  /**
   * 停止抽奖
   */
  function stop() {
    isRunning.value = false
    curIndex.value = -1
    winningAPrize.value = -1
  }

  function startRun() {
    if (!isRunning.value)
      return

    timer.value && clearTimeout(timer.value)

    // console.log(`已走步数=${state.curStep}, 执行总步数=${totalSteps.value}`);

    // 已走步数超过要执行总步数, 则停止
    if (curStep.value > totalSteps.value && winningAPrize.value === curIndex.value) {
      isRunning.value = false
      option?.success()
      return
    }

    // 高亮抽奖格子序号
    curIndex.value = lotterySort[curStep.value % 8]!
    // 速度调整
    speed.value = calcSpeed(speed.value)

    timer.value = setTimeout(() => {
      curStep.value++
      startRun()
    }, speed.value)
  }

  // 需要加速的前段步数
  const frontSteps = Math.floor(baseCircles.value * 8 * (1 / 3))
  // 需要减速的后段步数
  const midSteps = Math.floor(totalSteps.value * (5 / 6))

  function calcSpeed(speed: number) {
    // 最快最慢速度
  // 前段加速，中段匀速，后段减速
    if (curStep.value < frontSteps && speed > fastSpeed.value) {
      speed = speed - Math.floor((initSpeed.value - fastSpeed.value) / frontSteps)
    }
    else if (curStep.value > midSteps && speed < slowSpeed.value) {
    // 减速不一定要减速到最慢速度，优先保证动画效果看着协调
      speed = speed + Math.floor((slowSpeed.value - fastSpeed.value) / frontSteps / 5)
    }
    return speed
  }

  return { begin, curIndex, winningAPrize, stop }
}

/**
 *
 * @param design
 * @param prizesData 在设计模式下时，传入remoteData可以用来显示图片
 * @param token
 * @param baseURL
 * @param id
 * @param onLuckDrawCallback 抽奖后的回调
 * @returns
 */
export function useProvideLuckDrawData(
  design?: boolean,
  prizesData?: LotteryPrizes[] | null | undefined,
  token?: string,
  baseURL?: string,
  id?: string,
  onLuckDrawCallback?: (data: LotteryPrizes) => void,
) {
  const data = ref(new LotteryActivity())

  const variableMap = ref<VariableMap>({
    __COUNT__: {
      data: 0,
      setData: getCount,
    },
    __ID__: {
      data: id,
      setData: () => {
        variableMap.value.__ID__.data = data.value.id!
      },
    },
  })

  provide('LuckDrawData', data)

  provide('LuckDrawVariable', variableMap)

  provide('LuckDrawConfig', { design, token, baseURL, onLuckDrawCallback: (data: LotteryPrizes) => {
    onLuckDrawCallback && onLuckDrawCallback(data)
    getCount()
  } })

  async function getCount() {
    if (design)
      return
    const temp = await fetchApi({
      url: '/api/Lotterys/GetLotteryCount',
      baseURL,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
      params: {
        activityId: data.value.id,
      },
    })
    variableMap.value.__COUNT__.data = temp
  }

  async function getData() {
    if (design) {
      data.value = {
        ...new LotteryActivity(),
        prizes: prizesData && prizesData.length >= 8
          ? prizesData
          : [
              ...(prizesData || []),
              ...Array.from<LotteryPrizes>({ length: 8 - (prizesData?.length || 0) }).fill(new LotteryPrizes()),
            ],
      }
    }
    else {
      const temp = await fetchApi({
        url: '/api/Lotterys/GetActivity',
        baseURL,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        params: {
          id,
        },
      })
      if (!temp) {
        Modal.info({
          content: '当前活动已结束，敬请期待下次开启！',
          title: '当前活动已结束',
          okText: '返回',
          onOk: () => {
            wx.miniProgram.navigateBack()
          },
        })
        throw new Error('当前活动已结束，敬请期待下次开启！')
      }
      data.value = temp
      variableMap.value.__ID__.setData()
      getCount()
    }
    return data.value
  }

  function joinImageById(id: string) {
    if (id?.startsWith('/'))
      return id
    const path = `${baseURL || ''}/files/${id}`
    if (design) {
      const { token: adminToken } = useUserStore()
      return `${path}?access_token=${adminToken}`
    }

    return `${path}?access_token=${token}`
  }

  provide('joinImageById', joinImageById)

  return { data, getData, getCount, variableMap, joinImageById }
}

export function useInjectionLuckDrawData() {
  const data = inject<Ref<LotteryActivity>>('LuckDrawData')

  const prizes = ref<LotteryPrizes>()

  const joinImageById = inject<(id: string) => string>('joinImageById')!

  const luckDrawConfig = inject<{ design: boolean, token: string, baseURL: string, onLuckDrawCallback: (data: LotteryPrizes) => void }>('LuckDrawConfig')

  const variableMap = inject<Ref<VariableMap>>('LuckDrawVariable')

  /**
   * 请求抽取
   * @returns
   */
  async function drawPrizeApi(): Promise<LotteryPrizes> {
    const res = await fetchApi({
      url: '/api/Lotterys/DrawPrize',
      baseURL: luckDrawConfig?.baseURL,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${luckDrawConfig?.token}`,
      },
      params: {
        lotteryActivityId: data?.value.id,
      },
    })
    prizes.value = res
    return res
  }

  return { data, luckDrawConfig, variableMap, joinImageById, drawPrizeApi, prizes }
}
