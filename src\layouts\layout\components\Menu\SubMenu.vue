<!--
 * @Description:
 * @Author: luckymiaow
 * @Date: 2023-06-09 16:37:00
 * @LastEditors: 景 彡
-->
<template>
  <a-menu-item
    v-if="!menuInfo.children?.length || menuInfo.children.length === 1" :key="menuInfo.path" :index="menuInfo.path"
    @click="handleClickMenu(menuInfo.path, menuInfo)"
  >
    <template #icon>
      <Icon v-if="menuInfo?.meta?.icon" :icon="menuInfo.meta.icon" />
      <!-- <PicRightOutlined v-else /> -->
    </template>

    <router-link :to="menuInfo.path">
      <span class="flex">
        {{ menuInfo.children?.length === 1 ? (menuInfo.children[0]?.meta?.title
          || menuInfo.meta?.title) : menuInfo.meta?.title }}
        <a-badge v-if="menuInfo.meta?.title === '订单管理'" class="ml-4px" :count="menuInfo.meta.badge" />
      </span>
    </router-link>
  </a-menu-item>
  <template v-else-if="menuInfo.children && menuInfo.children.length > 1">
    <a-sub-menu :key="menuInfo.path" :index="menuInfo.path">
      <template #icon>
        <Icon v-if="menuInfo?.meta?.icon" :icon="menuInfo.meta.icon" />
        <!-- <PicRightOutlined v-else /> -->
      </template>
      <template #title>
        {{ menuInfo.meta?.title }}
      </template>
      <template v-for="item in menuInfo.children" :key=" item.path">
        <template v-if="item.children === null || item.children?.length === 0 || item.children?.length === 1">
          <a-menu-item :key="item.path" @click="handleClickMenu(item.path, item)">
            <template #icon>
              <Icon v-if="item?.meta?.icon" :icon="item.meta.icon" />
            </template>
            <router-link :to="item.path">
              <span class="flex">
                {{ item.meta?.title }}
                <a-badge v-if="item.meta?.title === '表单管理'" class="ml-4px" :count="item.meta?.unFormCount" />
              </span>
            </router-link>
          </a-menu-item>
        </template>
        <template v-else>
          <sub-menu
            v-if="menuInfo.children?.length !== 1" :key="item.path" :menu-info="item" :index="item.path"
            :parent-path="menuInfo.path"
          />
          <template v-else>
            <a-menu-item :key="item.path" @click="handleClickMenu(item.path, item)">
              <template #icon>
                <Icon v-if="item?.meta?.icon" :icon="item.meta.icon" />
              </template>
              <router-link :to="item.path">
                {{ item.meta?.title }}
              </router-link>
            </a-menu-item>
          </template>
        </template>
      </template>
    </a-sub-menu>
  </template>
</template>

<script setup lang="ts">
import type { RouterItem } from '@/types/interfaces'
import { useTabsStore } from '@/stores'
import * as $Icon from '@ant-design/icons-vue'
import { camelCase, upperFirst } from 'lodash-es'
import { createVNode } from 'vue'

defineProps<{ menuInfo: RouterItem, parentPath?: string }>()

const tabStore = useTabsStore()

function Icon(props: { icon: string }) {
  const { icon } = props
  const k = upperFirst(camelCase(icon))
  return createVNode(($Icon as any)[k])
}

function handleClickMenu(path: string, route: RouterItem) {
  const tabsParams = {
    icon: route.meta?.icon,
    title: route.meta?.title,
    path,
    name: route.meta?.title,
    close: !route.meta?.isAffix,
  } as any
  tabStore.addTabs(tabsParams)
}
</script>

<style scoped></style>
