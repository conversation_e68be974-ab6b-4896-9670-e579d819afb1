import axios, { AxiosRequestConfig, AxiosResponse } from "axios";
import {
  AddressComponentRes,
  ApiResult,
  BaseInfo,
  BaseUserRequestLog,
  BasicInformationTypeDescription,
  CarouselConfig,
  CarouselConfigEditModel,
  CarouselConfigNewModel,
  CarouselStatus,
  CarouselType,
  CashbackRecord,
  CashbackRecordPageView,
  CategoryType,
  ClientRole,
  ClientUser,
  ClientUserActivitiesCountViewModel,
  ClientUserInfoBaseView,
  ClientUserLiteViewModel,
  ClientUserLoginLog,
  ClientUserRequestLog,
  ClientUserRole,
  ClientUserState,
  ClientUserViewModel,
  CodeViewGenModel,
  CurrentUserPasswordChangeEditModel,
  DeletedFileInfo,
  Distributors,
  DistributorsEditModel,
  DistributorsNewModel,
  EfCoreResourcePermission,
  EmbedFileInfo,
  FeedbackStatus,
  FeedbackType,
  FeedbackView,
  FileAttribution,
  FileType,
  FormCompOptions,
  FormData,
  FormDataState,
  FormDesign,
  FormDesignViewModel,
  FormItem,
  FormState,
  FormStructure,
  GeoLocation,
  GetListModel,
  GuidIdNameViewModel,
  HomeStatistics,
  IActionResult,
  ILimitedResource,
  IPagedEnumerable,
  IPermissionStoreCapacities,
  IResourceMetadata,
  IResourcePermission,
  IVersioned,
  IdentityRole,
  IdentityUser,
  IdentityUserLoginLog,
  IdentityUserRole,
  InfoImage,
  Information,
  InformationCategory,
  InformationCategoryEditModel,
  InformationCategoryRef,
  InformationCategoryView,
  InformationEditModel,
  InformationPageView,
  InformationSortBy,
  InformationStatus,
  InformationView,
  InitialMode,
  InvalidModelApiResult,
  LimitedPermissionNode,
  LimitedResourceNode,
  LimitiUserModel,
  LoginResultLog,
  LotteryActivity,
  LotteryActivityEdit,
  LotteryActivitySortBy,
  LotteryActivityState,
  LotteryActivityType,
  LotteryCountChange,
  LotteryPrizes,
  LotteryPrizesEdit,
  LotteryPrizesType,
  LotteryResultPageView,
  LotteryResultSorBy,
  LotteryResultStatus,
  Notice,
  NoticeEditModel,
  NoticeScope,
  NoticeStatus,
  NoticeType,
  OpTypeQuery,
  PackedApiResult,
  PagedEnumerable,
  PermissionType,
  PointsCashbackTimeStatistics,
  PointsCommodity,
  PointsCommodityEditModel,
  PointsCommodityPageSortBy,
  PointsCommodityPageView,
  PointsCommodityStatus,
  PointsOrder,
  PointsOrderStatus,
  PointsOrderStatusKeyValue,
  PointsOrderViewModel,
  PointsTransaction,
  PointsTransactionQuery,
  PointsTransactionView,
  Product,
  ProductActivationLog,
  ProductActivationLogViewModel,
  ProductActivationStatistics,
  ProductActivities,
  ProductActivitiesEditModel,
  ProductActivitiesNewModel,
  ProductActivitiesStatus,
  ProductBatch,
  ProductBatchEditModel,
  ProductBatchNewModel,
  ProductBatchPageView,
  ProductBatchStatus,
  ProductBatchViewModel,
  ProductEditModel,
  ProductNewModel,
  ProductPoint,
  ProductPointEditModel,
  ProductStatus,
  ProductType,
  ProductTypeEditModel,
  ProductTypeNewModel,
  QueryFormat,
  QueryModel,
  QueryType,
  RecordSource,
  ReferralActivity,
  ReferralActivityEditModel,
  ReferralActivityStatus,
  ReferralRecordPageView,
  ReferralRewardRecordPageViewModel,
  ReferralRewards,
  RequestType,
  ResourceGrant,
  ResourceMetadata,
  ResourcePermission,
  ResourceType,
  ResponseType,
  RewardRecordSatus,
  Role,
  ScanProtection,
  SearchCriteria,
  ShippingAddressBase,
  SimulateUserViewModel,
  SortModel,
  SortType,
  SystemInfo,
  TenPayApiKeyEntity,
  TrackingNumberImportInfo,
  TypeNumber,
  UploadFileInfo,
  UploadFileInfoResult,
  User,
  UserActivityLog,
  UserCreateModel,
  UserEditModel,
  UserExpirationEditModel,
  UserLoginLog,
  UserNotice,
  UserPasswordChangeEditModel,
  UserRequestLog,
  UserRole,
  UserRoleViewModel,
  UserTotalStatistics,
  UserViewModel,
  WeChatApiKeyEntity,
  WeChatCallbackToken,
  WeChatSyncModel,
} from "../models";

export class apiOptions {
  static async request<TData, TResult>(
    options: AxiosRequestConfig<TData>
  ): Promise<TResult> {
    return axios.request<TData, AxiosResponse<TResult>>(options) as TResult;
  }
}

export async function requestPackedApi<TData, TResult>(
  options: AxiosRequestConfig<TData>
) {
  const data = await apiOptions.request<TData, PackedApiResult<TResult>>(
    options
  );
  if (!data.success) throw new Error(data.data as string);
  return data.data as TResult;
}

export class BaseInfoManage {
  /**
              * GetSystemBaseTypeAsync /api/BaseInfoManage/GetSystemBaseType
              * 系统基础类型
默认不允许
              */
  static async GetSystemBaseTypeAsync(
    options?: AxiosRequestConfig
  ): Promise<BasicInformationTypeDescription[]> {
    return requestPackedApi({
      method: "GET",
      url: `/api/BaseInfoManage/GetSystemBaseType`,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * GetTypeDescriptionAsync /api/BaseInfoManage/GetTypeDescription
   * 获取参数允许的类型
   */
  static async GetTypeDescriptionAsync(
    options?: AxiosRequestConfig
  ): Promise<BasicInformationTypeDescription[]> {
    return requestPackedApi({
      method: "GET",
      url: `/api/BaseInfoManage/GetTypeDescription`,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * VerifyData_GetAsync /api/BaseInfoManage/VerifyData
   * 验证数据是否正确
   */
  static async VerifyData_GetAsync(
    params: {
      /**数据类型*/ type?: string;
      /**数据*/
      data?: string;
    },
    options?: AxiosRequestConfig
  ): Promise<boolean> {
    return requestPackedApi({
      method: "GET",
      url: `/api/BaseInfoManage/VerifyData`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * VerifyType_GetAsync /api/BaseInfoManage/VerifyType
   * 验证类型是否正确
   */
  static async VerifyType_GetAsync(
    params: {
      /**数据类型*/ type?: string;
    },
    options?: AxiosRequestConfig
  ): Promise<boolean> {
    return requestPackedApi({
      method: "GET",
      url: `/api/BaseInfoManage/VerifyType`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * GetPagedAsync /api/BaseInfoManage/GetPaged
   * 获取所有基础信息(隐藏除外)
   */
  static async GetPagedAsync(
    params: { limit?: number; offset?: number },
    options?: AxiosRequestConfig
  ): Promise<IPagedEnumerable<BaseInfo>> {
    return requestPackedApi({
      method: "GET",
      url: `/api/BaseInfoManage/GetPaged`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * GetInfoAsync /api/BaseInfoManage/GetInfo
   * 基础信息内容
   */
  static async GetInfoAsync(
    params: { key?: string },
    options?: AxiosRequestConfig
  ): Promise<BaseInfo> {
    return requestPackedApi({
      method: "GET",
      url: `/api/BaseInfoManage/GetInfo`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * AddTypeDescription_PostAsync /api/BaseInfoManage/AddTypeDescription
   * 添加类型
   */
  static async AddTypeDescription_PostAsync(
    data: BasicInformationTypeDescription,
    options?: AxiosRequestConfig
  ): Promise<ApiResult> {
    return apiOptions.request({
      method: "POST",
      url: `/api/BaseInfoManage/AddTypeDescription`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * Save_PostAsync /api/BaseInfoManage/Save
   * 基础信息内容
   */
  static async Save_PostAsync(
    data: BaseInfo,
    options?: AxiosRequestConfig
  ): Promise<ApiResult> {
    return apiOptions.request({
      method: "POST",
      url: `/api/BaseInfoManage/Save`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
}
export class CarouselConfigs {
  /**
   * GetAsync /api/CarouselConfigs/Get
   * 获取轮播配置
   */
  static async GetAsync(
    params: { id: string },
    options?: AxiosRequestConfig
  ): Promise<CarouselConfig> {
    return requestPackedApi({
      method: "GET",
      url: `/api/CarouselConfigs/Get`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * Add_PostAsync /api/CarouselConfigs/Add
   * 添加轮播配置
   */
  static async Add_PostAsync(
    data: CarouselConfigNewModel,
    options?: AxiosRequestConfig
  ): Promise<ApiResult> {
    return apiOptions.request({
      method: "POST",
      url: `/api/CarouselConfigs/Add`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * Update_PostAsync /api/CarouselConfigs/Update
   * 修改轮播配置
   */
  static async Update_PostAsync(
    data: CarouselConfigEditModel,
    options?: AxiosRequestConfig
  ): Promise<ApiResult> {
    return apiOptions.request({
      method: "POST",
      url: `/api/CarouselConfigs/Update`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * Query_PostAsync /api/CarouselConfigs/Query
   * 查询轮播配置
   */
  static async Query_PostAsync(
    data: SearchCriteria,
    options?: AxiosRequestConfig
  ): Promise<PagedEnumerable<CarouselConfig>> {
    return requestPackedApi({
      method: "POST",
      url: `/api/CarouselConfigs/Query`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * Remove_PostAsync /api/CarouselConfigs/Remove
   * 移除轮播配置
   */
  static async Remove_PostAsync(
    data: string[],
    options?: AxiosRequestConfig
  ): Promise<ApiResult> {
    return apiOptions.request({
      method: "POST",
      url: `/api/CarouselConfigs/Remove`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
}
export class ClientRolesManage {
  /**
   * RoleList_PostAsync /api/ClientRolesManage/RoleList
   * 角色列表
   */
  static async RoleList_PostAsync(
    params: { roleName?: string; offset?: number; limit?: number },
    options?: AxiosRequestConfig
  ): Promise<IPagedEnumerable<ClientRole>> {
    return requestPackedApi({
      method: "POST",
      url: `/api/ClientRolesManage/RoleList`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
              * RoleAll_PostAsync /api/ClientRolesManage/RoleAll
              * 角色列表
完整
              */
  static async RoleAll_PostAsync(
    params: { roleName?: string },
    options?: AxiosRequestConfig
  ): Promise<ClientRole[]> {
    return requestPackedApi({
      method: "POST",
      url: `/api/ClientRolesManage/RoleAll`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * SetUserRole_PostAsync /api/ClientRolesManage/SetUserRole
   * 设置用户角色
   */
  static async SetUserRole_PostAsync(
    params: { userId: string; roleId: string; expirationTime?: Date },
    options?: AxiosRequestConfig
  ): Promise<ApiResult> {
    return apiOptions.request({
      method: "POST",
      url: `/api/ClientRolesManage/SetUserRole`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * RemoveUserRole_PostAsync /api/ClientRolesManage/RemoveUserRole
   * 删除用户角色
   */
  static async RemoveUserRole_PostAsync(
    params: { userId: string; roleId: string },
    options?: AxiosRequestConfig
  ): Promise<ApiResult> {
    return apiOptions.request({
      method: "POST",
      url: `/api/ClientRolesManage/RemoveUserRole`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * CreateRole_PostAsync /api/ClientRolesManage/CreateRole
   * 创建角色
   */
  static async CreateRole_PostAsync(
    params: { roleName?: string },
    options?: AxiosRequestConfig
  ): Promise<ClientRole> {
    return requestPackedApi({
      method: "POST",
      url: `/api/ClientRolesManage/CreateRole`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * Delete_PostAsync /api/ClientRolesManage/Delete
   * 删除角色
   */
  static async Delete_PostAsync(
    params: { roleId: string },
    options?: AxiosRequestConfig
  ): Promise<boolean> {
    return requestPackedApi({
      method: "POST",
      url: `/api/ClientRolesManage/Delete`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * ModifyRole_PostAsync /api/ClientRolesManage/ModifyRole
   * 修改客户端角色
   */
  static async ModifyRole_PostAsync(
    params: { roleId: string; roleName?: string },
    options?: AxiosRequestConfig
  ): Promise<ApiResult> {
    return apiOptions.request({
      method: "POST",
      url: `/api/ClientRolesManage/ModifyRole`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
}
export class ClientUserActivityLogs {
  /**
   * LotteryResultLogs_GetAsync /api/ClientUserActivityLogs/LotteryResultLogs
   * 抽奖活动日志
   */
  static async LotteryResultLogs_GetAsync(
    params: {
      actiyityId?: string;
      sortBy?: LotteryResultSorBy;
      isDescending?: boolean;
      offset?: number;
      limit?: number;
    },
    options?: AxiosRequestConfig
  ): Promise<IPagedEnumerable<LotteryResultPageView>> {
    return requestPackedApi({
      method: "GET",
      url: `/api/ClientUserActivityLogs/LotteryResultLogs`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * PointsTransactionLogs_PostAsync /api/ClientUserActivityLogs/PointsTransactionLogs
   * 积分日志
   */
  static async PointsTransactionLogs_PostAsync(
    params: {
      userId?: string;
      userKeyword?: string;
      keyword?: string;
      source?: RecordSource;
      start?: Date;
      end?: Date;
      type?: OpTypeQuery;
      offset?: number;
      limit?: number;
    },
    options?: AxiosRequestConfig
  ): Promise<IPagedEnumerable<PointsTransactionView>> {
    return requestPackedApi({
      method: "POST",
      url: `/api/ClientUserActivityLogs/PointsTransactionLogs`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * CashbackRecord_PostAsync /api/ClientUserActivityLogs/CashbackRecord
   * 红包日志
   */
  static async CashbackRecord_PostAsync(
    params: {
      userId?: string;
      userKeyword?: string;
      keyword?: string;
      source?: RecordSource;
      start?: Date;
      end?: Date;
      offset?: number;
      limit?: number;
    },
    options?: AxiosRequestConfig
  ): Promise<IPagedEnumerable<CashbackRecordPageView>> {
    return requestPackedApi({
      method: "POST",
      url: `/api/ClientUserActivityLogs/CashbackRecord`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * ProductActivation_PostAsync /api/ClientUserActivityLogs/ProductActivation
   * 产品激活日志
   */
  static async ProductActivation_PostAsync(
    params: {
      activity?: boolean;
      userId?: string;
      userKeyword?: string;
      productName?: string;
      source?: RecordSource;
      start?: Date;
      end?: Date;
      offset?: number;
      limit?: number;
    },
    options?: AxiosRequestConfig
  ): Promise<IPagedEnumerable<ProductActivationLog>> {
    return requestPackedApi({
      method: "POST",
      url: `/api/ClientUserActivityLogs/ProductActivation`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
}
export class ClientUserManage {
  /**
   * GetUserListAsync /api/ClientUserManage/GetUserList
   * 返回漆工列表
   */
  static async GetUserListAsync(
    params: {
      /**角色Id*/ roleId?: string;
      /**漆工名*/
      userName?: string;
      /**电话*/
      phoneNumber?: string;
      clientUserState?: ClientUserState;
      offset?: number;
      limit?: number;
    },
    options?: AxiosRequestConfig
  ): Promise<IPagedEnumerable<ClientUserViewModel>> {
    return requestPackedApi({
      method: "GET",
      url: `/api/ClientUserManage/GetUserList`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * GetUserViewListAsync /api/ClientUserManage/GetUserViewList
   * 返回漆工列表
   */
  static async GetUserViewListAsync(
    params: {
      /**角色Id*/ roleId?: string;
      /**漆工名*/
      userName?: string;
      /**邮箱*/
      email?: string;
      /**电话*/
      phoneNumber?: string;
      offset?: number;
      limit?: number;
    },
    options?: AxiosRequestConfig
  ): Promise<IPagedEnumerable<GuidIdNameViewModel>> {
    return requestPackedApi({
      method: "GET",
      url: `/api/ClientUserManage/GetUserViewList`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * GetUserViewListRefAsync /api/ClientUserManage/GetUserViewListRef
   *
   */
  static async GetUserViewListRefAsync(
    params: {
      roleId?: string;
      userName?: string;
      email?: string;
      phoneNumber?: string;
    },
    options?: AxiosRequestConfig
  ): Promise<GuidIdNameViewModel[]> {
    return requestPackedApi({
      method: "GET",
      url: `/api/ClientUserManage/GetUserViewListRef`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * GetUserViewModelAsync /api/ClientUserManage/GetUserViewModel
   * 漆工的详细信息
   */
  static async GetUserViewModelAsync(
    params: {
      /**漆工ID*/ userId: string;
    },
    options?: AxiosRequestConfig
  ): Promise<ClientUserViewModel> {
    return requestPackedApi({
      method: "GET",
      url: `/api/ClientUserManage/GetUserViewModel`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * UserLoginLogList_GetAsync /api/ClientUserManage/UserLoginLogList
   * 获取漆工的登录日志
   */
  static async UserLoginLogList_GetAsync(
    params: { userId: string; offset?: number; limit?: number },
    options?: AxiosRequestConfig
  ): Promise<IPagedEnumerable<ClientUserLoginLog>> {
    return requestPackedApi({
      method: "GET",
      url: `/api/ClientUserManage/UserLoginLogList`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * LoginLogList_GetAsync /api/ClientUserManage/LoginLogList
   * 获取所有漆工的登录日志
   */
  static async LoginLogList_GetAsync(
    params: { offset?: number; limit?: number },
    options?: AxiosRequestConfig
  ): Promise<IPagedEnumerable<ClientUserLoginLog>> {
    return requestPackedApi({
      method: "GET",
      url: `/api/ClientUserManage/LoginLogList`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * EditPoints_GetAsync /api/ClientUserManage/EditPoints
   * 新增/减少漆工积分
   */
  static async EditPoints_GetAsync(
    params: {
      userId: string;
      number: number;
      /**增加t / 减少 f*/
      opType: boolean;
      /**修改说明*/
      description?: string;
    },
    options?: AxiosRequestConfig
  ): Promise<ApiResult> {
    return apiOptions.request({
      method: "GET",
      url: `/api/ClientUserManage/EditPoints`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * EditUserModel_PostAsync /api/ClientUserManage/EditUserModel
   * 修改漆工信息
   */
  static async EditUserModel_PostAsync(
    data: UserEditModel,
    options?: AxiosRequestConfig
  ): Promise<ClientUserViewModel> {
    return requestPackedApi({
      method: "POST",
      url: `/api/ClientUserManage/EditUserModel`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * LimitUser_PostAsync /api/ClientUserManage/LimitUser
   * 禁用漆工
   */
  static async LimitUser_PostAsync(
    data: LimitiUserModel,
    options?: AxiosRequestConfig
  ): Promise<ApiResult> {
    return apiOptions.request({
      method: "POST",
      url: `/api/ClientUserManage/LimitUser`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * UnLimitUser_PostAsync /api/ClientUserManage/UnLimitUser
   * 取消禁用漆工
   */
  static async UnLimitUser_PostAsync(
    params: {
      /**漆工Id*/ userId: string;
    },
    options?: AxiosRequestConfig
  ): Promise<ApiResult> {
    return apiOptions.request({
      method: "POST",
      url: `/api/ClientUserManage/UnLimitUser`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * EditUserPassword_PostAsync /api/ClientUserManage/EditUserPassword
   * 修改漆工的密码
   */
  static async EditUserPassword_PostAsync(
    data: UserPasswordChangeEditModel,
    options?: AxiosRequestConfig
  ): Promise<ApiResult> {
    return apiOptions.request({
      method: "POST",
      url: `/api/ClientUserManage/EditUserPassword`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * EditUserExpiration_PostAsync /api/ClientUserManage/EditUserExpiration
   * 修改漆工过期时间
   */
  static async EditUserExpiration_PostAsync(
    data: UserExpirationEditModel,
    options?: AxiosRequestConfig
  ): Promise<ApiResult> {
    return apiOptions.request({
      method: "POST",
      url: `/api/ClientUserManage/EditUserExpiration`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * CreateUser_PostAsync /api/ClientUserManage/CreateUser
   * 创建漆工
   */
  static async CreateUser_PostAsync(
    data: UserCreateModel,
    options?: AxiosRequestConfig
  ): Promise<ClientUserViewModel> {
    return requestPackedApi({
      method: "POST",
      url: `/api/ClientUserManage/CreateUser`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * EditUserModifyPasswordEnd_PostAsync /api/ClientUserManage/EditUserModifyPasswordEnd
   * 修改漆工密码过期时间
   */
  static async EditUserModifyPasswordEnd_PostAsync(
    data: UserExpirationEditModel,
    options?: AxiosRequestConfig
  ): Promise<ApiResult> {
    return apiOptions.request({
      method: "POST",
      url: `/api/ClientUserManage/EditUserModifyPasswordEnd`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * TenPayClientUser_PostAsync /api/ClientUserManage/TenPayClientUser
   * 发放现金红包
   */
  static async TenPayClientUser_PostAsync(
    params: {
      /**支付密码*/ key?: string;
      /**转账名称*/
      name?: string;
      /**转账金额单位分*/
      amount: number;
      /**漆工漆工ID*/
      clientUserId: string;
      /**给漆工的简短备注，如：扫码奖励*/
      shortDescription?: string;
      /**给漆工的详细备注*/
      userRemark?: string;
      /**漆工真实姓名，大于200元时需要*/
      userName?: string;
      /**漆工身份证号码，大于200元时需要*/
      idCard?: string;
    },
    options?: AxiosRequestConfig
  ): Promise<ApiResult> {
    return apiOptions.request({
      method: "POST",
      url: `/api/ClientUserManage/TenPayClientUser`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
}
export class Crypto {
  /**
   * GetShortTokenAsync /api/Crypto/GetShortToken
   *
   */
  static async GetShortTokenAsync(
    options?: AxiosRequestConfig
  ): Promise<string> {
    return apiOptions.request({
      method: "GET",
      url: `/api/Crypto/GetShortToken`,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * GetRsaPublicKeyAsync /api/Crypto/GetRsaPublicKey
   *
   */
  static async GetRsaPublicKeyAsync(
    options?: AxiosRequestConfig
  ): Promise<string> {
    return apiOptions.request({
      method: "GET",
      url: `/api/Crypto/GetRsaPublicKey`,
      responseType: "json",
      ...(options || {}),
    });
  }
}
export class CurrentUser {
  /**
   * Me_GetAsync /api/CurrentUser/Me
   * 用户信息
   */
  static async Me_GetAsync(
    options?: AxiosRequestConfig
  ): Promise<UserViewModel> {
    return requestPackedApi({
      method: "GET",
      url: `/api/CurrentUser/Me`,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * CurrentUserLoginLogList_GetAsync /api/CurrentUser/CurrentUserLoginLogList
   * 获取当前用户的登录日志
   */
  static async CurrentUserLoginLogList_GetAsync(
    params: { offset?: number; limit?: number },
    options?: AxiosRequestConfig
  ): Promise<IPagedEnumerable<UserLoginLog>> {
    return requestPackedApi({
      method: "GET",
      url: `/api/CurrentUser/CurrentUserLoginLogList`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * EditUserModel_PostAsync /api/CurrentUser/EditUserModel
   * 修改用户信息
   */
  static async EditUserModel_PostAsync(
    data: UserEditModel,
    options?: AxiosRequestConfig
  ): Promise<UserViewModel> {
    return requestPackedApi({
      method: "POST",
      url: `/api/CurrentUser/EditUserModel`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * EditUserPassword_PostAsync /api/CurrentUser/EditUserPassword
   * 修改用户密码
   */
  static async EditUserPassword_PostAsync(
    data: CurrentUserPasswordChangeEditModel,
    options?: AxiosRequestConfig
  ): Promise<ApiResult> {
    return apiOptions.request({
      method: "POST",
      url: `/api/CurrentUser/EditUserPassword`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
}
export class DataAnalysis {
  /**
   * ProductActivation_GetAsync /api/DataAnalysis/ProductActivation
   * 产品激活查询
   */
  static async ProductActivation_GetAsync(
    params: {
      start: Date;
      end: Date;
      /**是否激活*/
      activity: boolean;
    },
    options?: AxiosRequestConfig
  ): Promise<ProductActivationLog[]> {
    return requestPackedApi({
      method: "GET",
      url: `/api/DataAnalysis/ProductActivation`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * PointsTransaction_GetAsync /api/DataAnalysis/PointsTransaction
   * 积分变动
   */
  static async PointsTransaction_GetAsync(
    params: { start: Date; end: Date; type?: OpTypeQuery },
    options?: AxiosRequestConfig
  ): Promise<PointsTransactionView[]> {
    return requestPackedApi({
      method: "GET",
      url: `/api/DataAnalysis/PointsTransaction`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * CashbackRecord_GetAsync /api/DataAnalysis/CashbackRecord
   * 返现变动
   */
  static async CashbackRecord_GetAsync(
    params: { start: Date; end: Date },
    options?: AxiosRequestConfig
  ): Promise<CashbackRecord[]> {
    return requestPackedApi({
      method: "GET",
      url: `/api/DataAnalysis/CashbackRecord`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * NewClientUser_GetAsync /api/DataAnalysis/NewClientUser
   * 新用户
   */
  static async NewClientUser_GetAsync(
    params: { start: Date; end: Date },
    options?: AxiosRequestConfig
  ): Promise<ClientUserLiteViewModel[]> {
    return requestPackedApi({
      method: "GET",
      url: `/api/DataAnalysis/NewClientUser`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * LastLoginClientUser_GetAsync /api/DataAnalysis/LastLoginClientUser
   * 上次登录的用户
   */
  static async LastLoginClientUser_GetAsync(
    params: { start: Date; end: Date },
    options?: AxiosRequestConfig
  ): Promise<ClientUserLiteViewModel[]> {
    return requestPackedApi({
      method: "GET",
      url: `/api/DataAnalysis/LastLoginClientUser`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * ProductActivationClientUser_GetAsync /api/DataAnalysis/ProductActivationClientUser
   * 用户产品激活次数
   */
  static async ProductActivationClientUser_GetAsync(
    params: { start: Date; end: Date },
    options?: AxiosRequestConfig
  ): Promise<ClientUserActivitiesCountViewModel[]> {
    return requestPackedApi({
      method: "GET",
      url: `/api/DataAnalysis/ProductActivationClientUser`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * PointsAndCashbackRecordTotal_GetAsync /api/DataAnalysis/PointsAndCashbackRecordTotal
   * 积分红包-按时间查询
   */
  static async PointsAndCashbackRecordTotal_GetAsync(
    params: { start: Date; end: Date },
    options?: AxiosRequestConfig
  ): Promise<PointsCashbackTimeStatistics> {
    return requestPackedApi({
      method: "GET",
      url: `/api/DataAnalysis/PointsAndCashbackRecordTotal`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * HomeStatistics_GetAsync /api/DataAnalysis/HomeStatistics
   * 首页各项统计
   */
  static async HomeStatistics_GetAsync(
    options?: AxiosRequestConfig
  ): Promise<HomeStatistics> {
    return requestPackedApi({
      method: "GET",
      url: `/api/DataAnalysis/HomeStatistics`,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * ProductActivationStatistics_GetAsync /api/DataAnalysis/ProductActivationStatistics
   * 激活详情统计
   */
  static async ProductActivationStatistics_GetAsync(
    params: { start: Date; end: Date },
    options?: AxiosRequestConfig
  ): Promise<ProductActivationStatistics[]> {
    return requestPackedApi({
      method: "GET",
      url: `/api/DataAnalysis/ProductActivationStatistics`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
}
export class Dealer {
  /**
   * GetAsync /api/Dealer/Get
   * 获取
   */
  static async GetAsync(
    params: { id: string },
    options?: AxiosRequestConfig
  ): Promise<Distributors> {
    return requestPackedApi({
      method: "GET",
      url: `/api/Dealer/Get`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * Add_PostAsync /api/Dealer/Add
   * 添加
   */
  static async Add_PostAsync(
    data: DistributorsNewModel,
    options?: AxiosRequestConfig
  ): Promise<ApiResult> {
    return apiOptions.request({
      method: "POST",
      url: `/api/Dealer/Add`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * Update_PostAsync /api/Dealer/Update
   * 更新
   */
  static async Update_PostAsync(
    data: DistributorsEditModel,
    options?: AxiosRequestConfig
  ): Promise<ApiResult> {
    return apiOptions.request({
      method: "POST",
      url: `/api/Dealer/Update`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * Query_PostAsync /api/Dealer/Query
   * 查询
   */
  static async Query_PostAsync(
    data: SearchCriteria,
    options?: AxiosRequestConfig
  ): Promise<PagedEnumerable<Distributors>> {
    return requestPackedApi({
      method: "POST",
      url: `/api/Dealer/Query`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * Remove_PostAsync /api/Dealer/Remove
   * 移除
   */
  static async Remove_PostAsync(
    data: string[],
    options?: AxiosRequestConfig
  ): Promise<ApiResult> {
    return apiOptions.request({
      method: "POST",
      url: `/api/Dealer/Remove`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
}
export class Feedbacksontroller {
  /**
   * FindOneById_GetAsync /api/Feedbacksontroller/FindOneById
   * 反馈信息详情
   */
  static async FindOneById_GetAsync(
    params: { id: string },
    options?: AxiosRequestConfig
  ): Promise<FeedbackView> {
    return requestPackedApi({
      method: "GET",
      url: `/api/Feedbacksontroller/FindOneById`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * SetStatus_GetAsync /api/Feedbacksontroller/SetStatus
   * 设置已回复
   */
  static async SetStatus_GetAsync(
    params: { id: string; status?: FeedbackStatus },
    options?: AxiosRequestConfig
  ): Promise<boolean> {
    return requestPackedApi({
      method: "GET",
      url: `/api/Feedbacksontroller/SetStatus`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * GetUnStatusAsync /api/Feedbacksontroller/GetUnStatus
   * 未回复反馈数量
   */
  static async GetUnStatusAsync(options?: AxiosRequestConfig): Promise<number> {
    return requestPackedApi({
      method: "GET",
      url: `/api/Feedbacksontroller/GetUnStatus`,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * GetPaged_PostAsync /api/Feedbacksontroller/GetPaged
   * 反馈信息查询
   */
  static async GetPaged_PostAsync(
    params: {
      type?: FeedbackType;
      keyword?: string;
      status?: FeedbackStatus;
      limit?: number;
      offset?: number;
    },
    options?: AxiosRequestConfig
  ): Promise<IPagedEnumerable<FeedbackView>> {
    return requestPackedApi({
      method: "POST",
      url: `/api/Feedbacksontroller/GetPaged`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
}
export class FileManage {
  /**
   * GetFileUrlAsync /api/FileManage/GetFileUrl
   * 文件直链获取
   */
  static async GetFileUrlAsync(
    params: { id: string; imagesResize?: string },
    options?: AxiosRequestConfig
  ): Promise<string> {
    return requestPackedApi({
      method: "GET",
      url: `/api/FileManage/GetFileUrl`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * GetFilesAsync /api/FileManage/GetFiles
   * 获取文件列表
   */
  static async GetFilesAsync(
    params: {
      type?: FileType;
      attribution?: FileAttribution;
      fileName?: string;
      offset?: number;
      limit?: number;
    },
    options?: AxiosRequestConfig
  ): Promise<IPagedEnumerable<UploadFileInfo>> {
    return requestPackedApi({
      method: "GET",
      url: `/api/FileManage/GetFiles`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * GetRemoveFilesAsync /api/FileManage/GetRemoveFiles
   * 获取回收站文件列表
   */
  static async GetRemoveFilesAsync(
    params: {
      type?: FileType;
      attribution?: FileAttribution;
      offset?: number;
      limit?: number;
    },
    options?: AxiosRequestConfig
  ): Promise<IPagedEnumerable<DeletedFileInfo>> {
    return requestPackedApi({
      method: "GET",
      url: `/api/FileManage/GetRemoveFiles`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * UploadPortalFile_PostAsync /api/FileManage/UploadPortalFile
   * 文件上传-门户站点文章用
   */
  static async UploadPortalFile_PostAsync(
    data: FormData,
    options?: AxiosRequestConfig
  ): Promise<UploadFileInfoResult[]> {
    return requestPackedApi({
      method: "POST",
      url: `/api/FileManage/UploadPortalFile`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * UploadCertifiedFile_PostAsync /api/FileManage/UploadCertifiedFile
   * 文件上传
   */
  static async UploadCertifiedFile_PostAsync(
    data: FormData,
    options?: AxiosRequestConfig
  ): Promise<UploadFileInfoResult[]> {
    return requestPackedApi({
      method: "POST",
      url: `/api/FileManage/UploadCertifiedFile`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * RemoveFile_PostAsync /api/FileManage/RemoveFile
   * 删除文件
   */
  static async RemoveFile_PostAsync(
    data: string[],
    options?: AxiosRequestConfig
  ): Promise<boolean> {
    return requestPackedApi({
      method: "POST",
      url: `/api/FileManage/RemoveFile`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * RemoveCompletelyFile_PostAsync /api/FileManage/RemoveCompletelyFile
   * 彻底删除文件
   */
  static async RemoveCompletelyFile_PostAsync(
    data: string[],
    options?: AxiosRequestConfig
  ): Promise<boolean> {
    return requestPackedApi({
      method: "POST",
      url: `/api/FileManage/RemoveCompletelyFile`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
}
export class FormDesigns {
  /**
   * FindDesign_GetAsync /api/FormDesigns/FindDesign
   * 查询表单
   */
  static async FindDesign_GetAsync(
    params: {
      name?: string;
      formState?: FormState;
      offset?: number;
      limit?: number;
    },
    options?: AxiosRequestConfig
  ): Promise<IPagedEnumerable<FormDesignViewModel>> {
    return requestPackedApi({
      method: "GET",
      url: `/api/FormDesigns/FindDesign`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * FindDesignById_GetAsync /api/FormDesigns/FindDesignById
   * 查询表单根据id
   */
  static async FindDesignById_GetAsync(
    params: { id: string },
    options?: AxiosRequestConfig
  ): Promise<FormDesign> {
    return requestPackedApi({
      method: "GET",
      url: `/api/FormDesigns/FindDesignById`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * FindFormDataByDesign_GetAsync /api/FormDesigns/FindFormDataByDesign
   * 查询表单数据
   */
  static async FindFormDataByDesign_GetAsync(
    params: {
      designId: string;
      state?: FormDataState;
      offset?: number;
      limit?: number;
    },
    options?: AxiosRequestConfig
  ): Promise<IPagedEnumerable<FormData>> {
    return requestPackedApi({
      method: "GET",
      url: `/api/FormDesigns/FindFormDataByDesign`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * GetUnFormDataStateAsync /api/FormDesigns/GetUnFormDataState
   * 未处理表单数量
   */
  static async GetUnFormDataStateAsync(
    options?: AxiosRequestConfig
  ): Promise<number> {
    return requestPackedApi({
      method: "GET",
      url: `/api/FormDesigns/GetUnFormDataState`,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * GetUnFormDataStateByIdAsync /api/FormDesigns/GetUnFormDataStateById
   * 未处理表单数量ById
   */
  static async GetUnFormDataStateByIdAsync(
    params: { formDesignId: string },
    options?: AxiosRequestConfig
  ): Promise<number> {
    return requestPackedApi({
      method: "GET",
      url: `/api/FormDesigns/GetUnFormDataStateById`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * ExportFormData_GetAsync /api/FormDesigns/ExportFormData
   * 导出表单数据
   */
  static async ExportFormData_GetAsync(
    params: { designId: string },
    options?: AxiosRequestConfig
  ): Promise<Blob> {
    return request({
      method: "GET",
      url: `/api/FormDesigns/ExportFormData`,
      params,
      responseType: "blob",
      ...(options || {}),
    });
  }
  /**
   * SaveDesign_PostAsync /api/FormDesigns/SaveDesign
   * 保存表单
   */
  static async SaveDesign_PostAsync(
    data: FormDesign,
    options?: AxiosRequestConfig
  ): Promise<FormDesign> {
    return requestPackedApi({
      method: "POST",
      url: `/api/FormDesigns/SaveDesign`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * EditFormDataState_PostAsync /api/FormDesigns/EditFormDataState
   * 修改状态
   */
  static async EditFormDataState_PostAsync(
    params: { id: string; state?: FormDataState },
    options?: AxiosRequestConfig
  ): Promise<FormData> {
    return requestPackedApi({
      method: "POST",
      url: `/api/FormDesigns/EditFormDataState`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
}
export class InformationCategories {
  /**
   * GetOneByIdAsync /api/InformationCategories/GetOneById
   * 根据ID查找
   */
  static async GetOneByIdAsync(
    params: { id: string },
    options?: AxiosRequestConfig
  ): Promise<InformationCategory> {
    return requestPackedApi({
      method: "GET",
      url: `/api/InformationCategories/GetOneById`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * GetRefByIdAsync /api/InformationCategories/GetRefById
   * 根据ID查找Ref
   */
  static async GetRefByIdAsync(
    params: { id: string },
    options?: AxiosRequestConfig
  ): Promise<InformationCategoryRef> {
    return requestPackedApi({
      method: "GET",
      url: `/api/InformationCategories/GetRefById`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * GetRefByTypeAsync /api/InformationCategories/GetRefByType
   * 获取分类列表Ref
   */
  static async GetRefByTypeAsync(
    params: { isAdmin?: boolean },
    options?: AxiosRequestConfig
  ): Promise<InformationCategoryRef[]> {
    return requestPackedApi({
      method: "GET",
      url: `/api/InformationCategories/GetRefByType`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * GeCategoryPaged_GetAsync /api/InformationCategories/GeCategoryPaged
   * 获取分类列表
   */
  static async GeCategoryPaged_GetAsync(
    params: { type?: string; name?: string; menuShow?: boolean },
    options?: AxiosRequestConfig
  ): Promise<InformationCategoryView[]> {
    return requestPackedApi({
      method: "GET",
      url: `/api/InformationCategories/GeCategoryPaged`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * CreateInformationCategory_PostAsync /api/InformationCategories/CreateInformationCategory
   * 创建分类
   */
  static async CreateInformationCategory_PostAsync(
    data: InformationCategoryEditModel,
    options?: AxiosRequestConfig
  ): Promise<InformationCategory> {
    return requestPackedApi({
      method: "POST",
      url: `/api/InformationCategories/CreateInformationCategory`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * ModifyInformationCategory_PostAsync /api/InformationCategories/ModifyInformationCategory
   * 编辑分类
   */
  static async ModifyInformationCategory_PostAsync(
    data: InformationCategoryEditModel,
    options?: AxiosRequestConfig
  ): Promise<InformationCategory> {
    return requestPackedApi({
      method: "POST",
      url: `/api/InformationCategories/ModifyInformationCategory`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * DeleteInformationCategory_PostAsync /api/InformationCategories/DeleteInformationCategory
   * 删除分类
   */
  static async DeleteInformationCategory_PostAsync(
    data: string[],
    options?: AxiosRequestConfig
  ): Promise<boolean> {
    return requestPackedApi({
      method: "POST",
      url: `/api/InformationCategories/DeleteInformationCategory`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
}
export class Informations {
  /**
   * GetByInformationIdAsync /api/Informations/GetByInformationId
   * 获取文章最新版本
   */
  static async GetByInformationIdAsync(
    params: {
      /**informationId*/ informationId: string;
    },
    options?: AxiosRequestConfig
  ): Promise<InformationView> {
    return requestPackedApi({
      method: "GET",
      url: `/api/Informations/GetByInformationId`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * GetInformationHistoryAsync /api/Informations/GetInformationHistory
   * 获取文章历史版本
   */
  static async GetInformationHistoryAsync(
    params: { informationId: string },
    options?: AxiosRequestConfig
  ): Promise<Information[]> {
    return requestPackedApi({
      method: "GET",
      url: `/api/Informations/GetInformationHistory`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * CreateInformation_PostAsync /api/Informations/CreateInformation
   * 创建文章
   */
  static async CreateInformation_PostAsync(
    data: InformationEditModel,
    options?: AxiosRequestConfig
  ): Promise<Information> {
    return requestPackedApi({
      method: "POST",
      url: `/api/Informations/CreateInformation`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * WeChatIsSyncByMsgIds_PostAsync /api/Informations/WeChatIsSyncByMsgIds
   * 根据msgIds查询公众号文章是否同步
   */
  static async WeChatIsSyncByMsgIds_PostAsync(
    data: string[],
    options?: AxiosRequestConfig
  ): Promise<Information[]> {
    return requestPackedApi({
      method: "POST",
      url: `/api/Informations/WeChatIsSyncByMsgIds`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * WeChatSync_PostAsync /api/Informations/WeChatSync
   * 同步微信文章
   */
  static async WeChatSync_PostAsync(
    params: { status?: InformationStatus; categoryId: string },
    data: WeChatSyncModel[],
    options?: AxiosRequestConfig
  ): Promise<Information[]> {
    return requestPackedApi({
      method: "POST",
      url: `/api/Informations/WeChatSync`,
      data,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * ModifyInformation_PostAsync /api/Informations/ModifyInformation
   * 编辑文章
   */
  static async ModifyInformation_PostAsync(
    data: InformationEditModel,
    options?: AxiosRequestConfig
  ): Promise<Information> {
    return requestPackedApi({
      method: "POST",
      url: `/api/Informations/ModifyInformation`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * DeleteByInformationId_PostAsync /api/Informations/DeleteByInformationId
   * 删除文章
   */
  static async DeleteByInformationId_PostAsync(
    params: { informationId: string },
    options?: AxiosRequestConfig
  ): Promise<ApiResult> {
    return apiOptions.request({
      method: "POST",
      url: `/api/Informations/DeleteByInformationId`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * RemoveCompletelyByInformationId_PostAsync /api/Informations/RemoveCompletelyByInformationId
   * 彻底删除回收站文章
   */
  static async RemoveCompletelyByInformationId_PostAsync(
    params: { informationId: string },
    options?: AxiosRequestConfig
  ): Promise<ApiResult> {
    return apiOptions.request({
      method: "POST",
      url: `/api/Informations/RemoveCompletelyByInformationId`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * SetStatusByInformationId_PostAsync /api/Informations/SetStatusByInformationId
   * 设置文章状态
   */
  static async SetStatusByInformationId_PostAsync(
    params: {
      informationId: string;
      status?: InformationStatus;
      updateTime?: Date;
    },
    options?: AxiosRequestConfig
  ): Promise<ApiResult> {
    return apiOptions.request({
      method: "POST",
      url: `/api/Informations/SetStatusByInformationId`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * SetInformationTop_PostAsync /api/Informations/SetInformationTop
   * 更改置顶状态
   */
  static async SetInformationTop_PostAsync(
    params: { informationId: string; top: boolean },
    options?: AxiosRequestConfig
  ): Promise<ApiResult> {
    return apiOptions.request({
      method: "POST",
      url: `/api/Informations/SetInformationTop`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * GetInformation_PostAsync /api/Informations/GetInformation
   * 查找文章
   */
  static async GetInformation_PostAsync(
    params: {
      title?: string;
      content?: string;
      keyword?: string;
      categoryName?: string;
      isTop?: boolean;
      author?: string;
      startTime?: Date;
      endTime?: Date;
      status?: InformationStatus;
      sortBy?: InformationSortBy;
      isDescending?: boolean;
      isCover?: boolean;
      tag?: string;
      offset?: number;
      limit?: number;
    },
    data: string[],
    options?: AxiosRequestConfig
  ): Promise<IPagedEnumerable<InformationPageView>> {
    return requestPackedApi({
      method: "POST",
      url: `/api/Informations/GetInformation`,
      data,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
}
export class Limits {
  /**
   * GetStoreCapacitiesAsync /api/Limits/GetStoreCapacities
   *
   */
  static async GetStoreCapacitiesAsync(
    options?: AxiosRequestConfig
  ): Promise<IPermissionStoreCapacities> {
    return requestPackedApi({
      method: "GET",
      url: `/api/Limits/GetStoreCapacities`,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * List_GetAsync /api/Limits/List
   *
   */
  static async List_GetAsync(
    options?: AxiosRequestConfig
  ): Promise<ILimitedResource[]> {
    return requestPackedApi({
      method: "GET",
      url: `/api/Limits/List`,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * Tree_GetAsync /api/Limits/Tree
   *
   */
  static async Tree_GetAsync(
    options?: AxiosRequestConfig
  ): Promise<LimitedResourceNode[]> {
    return requestPackedApi({
      method: "GET",
      url: `/api/Limits/Tree`,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * GetByRolenameAsync /api/Limits/GetByRolename
   *
   */
  static async GetByRolenameAsync(
    params: { roleName?: string },
    options?: AxiosRequestConfig
  ): Promise<IResourcePermission> {
    return requestPackedApi({
      method: "GET",
      url: `/api/Limits/GetByRolename`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * GetByUserIdAsync /api/Limits/GetByUserId
   *
   */
  static async GetByUserIdAsync(
    params: { userId?: string },
    options?: AxiosRequestConfig
  ): Promise<IResourcePermission> {
    return requestPackedApi({
      method: "GET",
      url: `/api/Limits/GetByUserId`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * Permissions_GetAsync /api/Limits/Permissions
   *
   */
  static async Permissions_GetAsync(
    options?: AxiosRequestConfig
  ): Promise<IVersioned<LimitedPermissionNode[]>> {
    return requestPackedApi({
      method: "GET",
      url: `/api/Limits/Permissions`,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * GetMetadataAsync /api/Limits/GetMetadata
   *
   */
  static async GetMetadataAsync(
    params: { id?: string },
    options?: AxiosRequestConfig
  ): Promise<IResourceMetadata> {
    return requestPackedApi({
      method: "GET",
      url: `/api/Limits/GetMetadata`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * SetByRolename_PostAsync /api/Limits/SetByRolename
   *
   */
  static async SetByRolename_PostAsync(
    params: { roleName?: string },
    data: ResourcePermission,
    options?: AxiosRequestConfig
  ): Promise<ApiResult> {
    return apiOptions.request({
      method: "POST",
      url: `/api/Limits/SetByRolename`,
      data,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * SetByUserId_PostAsync /api/Limits/SetByUserId
   *
   */
  static async SetByUserId_PostAsync(
    params: { userId?: string },
    data: ResourcePermission,
    options?: AxiosRequestConfig
  ): Promise<ApiResult> {
    return apiOptions.request({
      method: "POST",
      url: `/api/Limits/SetByUserId`,
      data,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * SetMetadata_PostAsync /api/Limits/SetMetadata
   *
   */
  static async SetMetadata_PostAsync(
    params: { id?: string },
    data: ResourceMetadata,
    options?: AxiosRequestConfig
  ): Promise<ApiResult> {
    return apiOptions.request({
      method: "POST",
      url: `/api/Limits/SetMetadata`,
      data,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
}
export class Lotterys {
  /**
   * FingOneById_GetAsync /api/Lotterys/FingOneById
   * 抽奖活动详情
   */
  static async FingOneById_GetAsync(
    params: { actiyityId: string },
    options?: AxiosRequestConfig
  ): Promise<LotteryActivity> {
    return requestPackedApi({
      method: "GET",
      url: `/api/Lotterys/FingOneById`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * LotteryResultPage_GetAsync /api/Lotterys/LotteryResultPage
   * 抽奖结果列表页
   */
  static async LotteryResultPage_GetAsync(
    params: {
      actiyityId: string;
      sortBy?: LotteryResultSorBy;
      isDescending?: boolean;
      offset?: number;
      limit?: number;
    },
    options?: AxiosRequestConfig
  ): Promise<IPagedEnumerable<LotteryResultPageView>> {
    return requestPackedApi({
      method: "GET",
      url: `/api/Lotterys/LotteryResultPage`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * SaveStatus_GetAsync /api/Lotterys/SaveStatus
   * 修改活动状态
   */
  static async SaveStatus_GetAsync(
    params: { actiyity: string; status?: LotteryActivityState },
    options?: AxiosRequestConfig
  ): Promise<LotteryActivity> {
    return requestPackedApi({
      method: "GET",
      url: `/api/Lotterys/SaveStatus`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * AvailableNow_GetAsync /api/Lotterys/AvailableNow
   * 立即上架抽奖活动
   */
  static async AvailableNow_GetAsync(
    params: { actiyityId: string },
    options?: AxiosRequestConfig
  ): Promise<LotteryActivity> {
    return requestPackedApi({
      method: "GET",
      url: `/api/Lotterys/AvailableNow`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * RemovePrizeByActiyityId_GetAsync /api/Lotterys/RemovePrizeByActiyityId
   * 移除指定奖品
   */
  static async RemovePrizeByActiyityId_GetAsync(
    params: { actiyityId: string; prizesId: string },
    options?: AxiosRequestConfig
  ): Promise<LotteryActivity> {
    return requestPackedApi({
      method: "GET",
      url: `/api/Lotterys/RemovePrizeByActiyityId`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * SetGuaranteedPrize_GetAsync /api/Lotterys/SetGuaranteedPrize
   * 设置保底奖品
   */
  static async SetGuaranteedPrize_GetAsync(
    params: {
      actiyityId: string;
      /**传null时，移除保底奖品*/
      prizeId?: string;
    },
    options?: AxiosRequestConfig
  ): Promise<LotteryActivity> {
    return requestPackedApi({
      method: "GET",
      url: `/api/Lotterys/SetGuaranteedPrize`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * TenPayByLotteryResults_GetAsync /api/Lotterys/TenPayByLotteryResults
   * 补发现金奖励
   */
  static async TenPayByLotteryResults_GetAsync(
    params: {
      lotteryResulId: string;
      /**密码*/
      key?: string;
    },
    options?: AxiosRequestConfig
  ): Promise<boolean> {
    return requestPackedApi({
      method: "GET",
      url: `/api/Lotterys/TenPayByLotteryResults`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * LotteryActivityPage_PostAsync /api/Lotterys/LotteryActivityPage
   * 抽奖活动列表页
   */
  static async LotteryActivityPage_PostAsync(
    params: {
      actiyityId?: string;
      keyword?: string;
      guaranteedDraws?: number;
      state?: LotteryActivityState;
      sortBy?: LotteryActivitySortBy;
      isDescending?: boolean;
      offset?: number;
      limit?: number;
    },
    options?: AxiosRequestConfig
  ): Promise<IPagedEnumerable<LotteryActivity>> {
    return requestPackedApi({
      method: "POST",
      url: `/api/Lotterys/LotteryActivityPage`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * Save_PostAsync /api/Lotterys/Save
   * 保存/新增抽奖活动
   */
  static async Save_PostAsync(
    data: LotteryActivityEdit,
    options?: AxiosRequestConfig
  ): Promise<LotteryActivity> {
    return requestPackedApi({
      method: "POST",
      url: `/api/Lotterys/Save`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * SavePrizeByActiyityId_PostAsync /api/Lotterys/SavePrizeByActiyityId
   * 新增/保存抽奖活动奖品
   */
  static async SavePrizeByActiyityId_PostAsync(
    params: { actiyityId: string },
    data: LotteryPrizesEdit,
    options?: AxiosRequestConfig
  ): Promise<LotteryPrizes> {
    return requestPackedApi({
      method: "POST",
      url: `/api/Lotterys/SavePrizeByActiyityId`,
      data,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
}
export class Notices {
  /**
   * SendNotice_GetAsync /api/Notices/SendNotice
   * 发送通知
   */
  static async SendNotice_GetAsync(
    params: { noticeId: string },
    options?: AxiosRequestConfig
  ): Promise<boolean> {
    return requestPackedApi({
      method: "GET",
      url: `/api/Notices/SendNotice`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * FindNotices_GetAsync /api/Notices/FindNotices
   * 查询通知
   */
  static async FindNotices_GetAsync(
    params: {
      keyword?: string;
      noticeType?: NoticeType;
      offset?: number;
      limit?: number;
    },
    options?: AxiosRequestConfig
  ): Promise<IPagedEnumerable<Notice>> {
    return requestPackedApi({
      method: "GET",
      url: `/api/Notices/FindNotices`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * FindById_GetAsync /api/Notices/FindById
   * 查看通知
   */
  static async FindById_GetAsync(
    params: { id: string },
    options?: AxiosRequestConfig
  ): Promise<Notice> {
    return requestPackedApi({
      method: "GET",
      url: `/api/Notices/FindById`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * SaveNotice_PostAsync /api/Notices/SaveNotice
   * 保存、新增通知
   */
  static async SaveNotice_PostAsync(
    params: { status?: NoticeStatus },
    data: NoticeEditModel,
    options?: AxiosRequestConfig
  ): Promise<Notice> {
    return requestPackedApi({
      method: "POST",
      url: `/api/Notices/SaveNotice`,
      data,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * Removes_PostAsync /api/Notices/Removes
   * 删除通知
   */
  static async Removes_PostAsync(
    data: string[],
    options?: AxiosRequestConfig
  ): Promise<ApiResult> {
    return apiOptions.request({
      method: "POST",
      url: `/api/Notices/Removes`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
}
export class PointsMalls {
  /**
   * FindById_GetAsync /api/PointsMalls/FindById
   * 查找商品
   */
  static async FindById_GetAsync(
    params: { id: string },
    options?: AxiosRequestConfig
  ): Promise<PointsCommodity> {
    return requestPackedApi({
      method: "GET",
      url: `/api/PointsMalls/FindById`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * SetRecommend_GetAsync /api/PointsMalls/SetRecommend
   * 设置推荐
   */
  static async SetRecommend_GetAsync(
    params: { id: string },
    options?: AxiosRequestConfig
  ): Promise<boolean> {
    return requestPackedApi({
      method: "GET",
      url: `/api/PointsMalls/SetRecommend`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * SetOrder_GetAsync /api/PointsMalls/SetOrder
   * 设置排序
   */
  static async SetOrder_GetAsync(
    params: { id: string; order: number },
    options?: AxiosRequestConfig
  ): Promise<boolean> {
    return requestPackedApi({
      method: "GET",
      url: `/api/PointsMalls/SetOrder`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * GetCommodityPage_PostAsync /api/PointsMalls/GetCommodityPage
   * 积分商品查询
   */
  static async GetCommodityPage_PostAsync(
    params: {
      /**商品搜索*/ keyWord?: string;
      /**商品状态*/
      pointsTransactionQuery?: PointsTransactionQuery;
      /**排序字段*/
      sortField?: PointsCommodityPageSortBy;
      /**正序or倒序*/
      isDescending?: boolean;
      offset?: number;
      limit?: number;
    },
    options?: AxiosRequestConfig
  ): Promise<IPagedEnumerable<PointsCommodityPageView>> {
    return requestPackedApi({
      method: "POST",
      url: `/api/PointsMalls/GetCommodityPage`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * SaveCommodity_PostAsync /api/PointsMalls/SaveCommodity
   * 保存商品
   */
  static async SaveCommodity_PostAsync(
    data: PointsCommodityEditModel,
    options?: AxiosRequestConfig
  ): Promise<PointsCommodity> {
    return requestPackedApi({
      method: "POST",
      url: `/api/PointsMalls/SaveCommodity`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * BulkPublish_PostAsync /api/PointsMalls/BulkPublish
   * 批量上架
   */
  static async BulkPublish_PostAsync(
    data: string[],
    options?: AxiosRequestConfig
  ): Promise<boolean> {
    return requestPackedApi({
      method: "POST",
      url: `/api/PointsMalls/BulkPublish`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * BulkUnpublish_PostAsync /api/PointsMalls/BulkUnpublish
   * 批量下架
   */
  static async BulkUnpublish_PostAsync(
    data: string[],
    options?: AxiosRequestConfig
  ): Promise<boolean> {
    return requestPackedApi({
      method: "POST",
      url: `/api/PointsMalls/BulkUnpublish`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * Remove_PostAsync /api/PointsMalls/Remove
   * 删除积分商品
   */
  static async Remove_PostAsync(
    data: string[],
    options?: AxiosRequestConfig
  ): Promise<boolean> {
    return requestPackedApi({
      method: "POST",
      url: `/api/PointsMalls/Remove`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
}
export class PointsOrders {
  /**
   * FindById_GetAsync /api/PointsOrders/FindById
   * 查找订单
   */
  static async FindById_GetAsync(
    params: { id: string },
    options?: AxiosRequestConfig
  ): Promise<PointsOrder> {
    return requestPackedApi({
      method: "GET",
      url: `/api/PointsOrders/FindById`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * SendPackage_GetAsync /api/PointsOrders/SendPackage
   * 发货
   */
  static async SendPackage_GetAsync(
    params: { id: string; trackingNumber?: string },
    options?: AxiosRequestConfig
  ): Promise<boolean> {
    return requestPackedApi({
      method: "GET",
      url: `/api/PointsOrders/SendPackage`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * ExportPendingShipments_GetAsync /api/PointsOrders/ExportPendingShipments
   * 未发货订单导出
   */
  static async ExportPendingShipments_GetAsync(
    options?: AxiosRequestConfig
  ): Promise<Blob> {
    return request({
      method: "GET",
      url: `/api/PointsOrders/ExportPendingShipments`,
      responseType: "blob",
      ...(options || {}),
    });
  }
  /**
   * GetPointsOrderNumberAsync /api/PointsOrders/GetPointsOrderNumber
   * 未发货订单数量
   */
  static async GetPointsOrderNumberAsync(
    options?: AxiosRequestConfig
  ): Promise<number> {
    return requestPackedApi({
      method: "GET",
      url: `/api/PointsOrders/GetPointsOrderNumber`,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * GetPointsOrderNumberByStatusAsync /api/PointsOrders/GetPointsOrderNumberByStatus
   * 未发货订单数量 分类
   */
  static async GetPointsOrderNumberByStatusAsync(
    options?: AxiosRequestConfig
  ): Promise<PointsOrderStatusKeyValue[]> {
    return requestPackedApi({
      method: "GET",
      url: `/api/PointsOrders/GetPointsOrderNumberByStatus`,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * GetNewOrderNumberAsync /api/PointsOrders/GetNewOrderNumber
   * 新订单数量
   */
  static async GetNewOrderNumberAsync(
    options?: AxiosRequestConfig
  ): Promise<number> {
    return requestPackedApi({
      method: "GET",
      url: `/api/PointsOrders/GetNewOrderNumber`,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * ReadOrderNumber_GetAsync /api/PointsOrders/ReadOrderNumber
   * 已读
   */
  static async ReadOrderNumber_GetAsync(
    options?: AxiosRequestConfig
  ): Promise<ApiResult> {
    return apiOptions.request({
      method: "GET",
      url: `/api/PointsOrders/ReadOrderNumber`,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * GetPointsOrderPage_PostAsync /api/PointsOrders/GetPointsOrderPage
   * 订单查询
   */
  static async GetPointsOrderPage_PostAsync(
    params: {
      /**订单编号*/ guidString?: string;
      /**商品信息*/
      keyword?: string;
      /**用户信息*/
      userword?: string;
      /**下单时间 开始*/
      start?: Date;
      /**下单时间 结束*/
      end?: Date;
      /**订单状态*/
      status?: PointsOrderStatus;
      offset?: number;
      limit?: number;
    },
    options?: AxiosRequestConfig
  ): Promise<IPagedEnumerable<PointsOrderViewModel>> {
    return requestPackedApi({
      method: "POST",
      url: `/api/PointsOrders/GetPointsOrderPage`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * CancelOrders_PostAsync /api/PointsOrders/CancelOrders
   * 取消订单
   */
  static async CancelOrders_PostAsync(
    data: string[],
    options?: AxiosRequestConfig
  ): Promise<boolean> {
    return requestPackedApi({
      method: "POST",
      url: `/api/PointsOrders/CancelOrders`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * ImportTrackingNumber_PostAsync /api/PointsOrders/ImportTrackingNumber
   * 导入快递信息
   */
  static async ImportTrackingNumber_PostAsync(
    data: FormData,
    options?: AxiosRequestConfig
  ): Promise<TrackingNumberImportInfo[]> {
    return requestPackedApi({
      method: "POST",
      url: `/api/PointsOrders/ImportTrackingNumber`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
}
export class Products {
  /**
   * GetAsync /api/Products/Get
   * 获取产品
   */
  static async GetAsync(
    params: { id: string },
    options?: AxiosRequestConfig
  ): Promise<Product> {
    return requestPackedApi({
      method: "GET",
      url: `/api/Products/Get`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * GetTypeAsync /api/Products/GetType
   * 获取产品类型
   */
  static async GetTypeAsync(
    params: { id: string },
    options?: AxiosRequestConfig
  ): Promise<ProductType> {
    return requestPackedApi({
      method: "GET",
      url: `/api/Products/GetType`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * AdjustProductTypeSortOrder_GetAsync /api/Products/AdjustProductTypeSortOrder
   * 更新产品类型的排序顺序
   */
  static async AdjustProductTypeSortOrder_GetAsync(
    params: { productTypeId: string; newSortOrder: number },
    options?: AxiosRequestConfig
  ): Promise<boolean> {
    return requestPackedApi({
      method: "GET",
      url: `/api/Products/AdjustProductTypeSortOrder`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * GetBatchAsync /api/Products/GetBatch
   * 获取产品批次
   */
  static async GetBatchAsync(
    params: { id: string },
    options?: AxiosRequestConfig
  ): Promise<ProductBatch> {
    return requestPackedApi({
      method: "GET",
      url: `/api/Products/GetBatch`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * FindProductBatchViewById_GetAsync /api/Products/FindProductBatchViewById
   * 产品批次详情查询
   */
  static async FindProductBatchViewById_GetAsync(
    params: { productBatchId: string },
    options?: AxiosRequestConfig
  ): Promise<ProductBatchViewModel> {
    return requestPackedApi({
      method: "GET",
      url: `/api/Products/FindProductBatchViewById`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * GetBatchCodesAsync /api/Products/GetBatchCodes
   * 获取产品批次验证码
   */
  static async GetBatchCodesAsync(
    params: { id: string },
    options?: AxiosRequestConfig
  ): Promise<CodeViewGenModel[]> {
    return requestPackedApi({
      method: "GET",
      url: `/api/Products/GetBatchCodes`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * GetPointAsync /api/Products/GetPoint
   * 获取产品积分设置
   */
  static async GetPointAsync(
    params: { id: string },
    options?: AxiosRequestConfig
  ): Promise<ProductPoint> {
    return requestPackedApi({
      method: "GET",
      url: `/api/Products/GetPoint`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * GetActivitiesAsync /api/Products/GetActivities
   * 获取活动
   */
  static async GetActivitiesAsync(
    params: { id: string },
    options?: AxiosRequestConfig
  ): Promise<ProductActivities> {
    return requestPackedApi({
      method: "GET",
      url: `/api/Products/GetActivities`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * FindProductActivationLogByQr_GetAsync /api/Products/FindProductActivationLogByQR
   * 根据二维码编号查询激活人
   */
  static async FindProductActivationLogByQr_GetAsync(
    params: { qrnumber?: string },
    options?: AxiosRequestConfig
  ): Promise<ProductActivationLogViewModel> {
    return requestPackedApi({
      method: "GET",
      url: `/api/Products/FindProductActivationLogByQR`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * ProductActivationLogsByQr_GetAsync /api/Products/ProductActivationLogsByQR
   * 根据二维码编号查询所有扫码情况
   */
  static async ProductActivationLogsByQr_GetAsync(
    params: { qrnumber?: string },
    options?: AxiosRequestConfig
  ): Promise<ProductActivationLogViewModel[]> {
    return requestPackedApi({
      method: "GET",
      url: `/api/Products/ProductActivationLogsByQR`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * Add_PostAsync /api/Products/Add
   * 添加产品
   */
  static async Add_PostAsync(
    data: ProductNewModel,
    options?: AxiosRequestConfig
  ): Promise<ApiResult> {
    return apiOptions.request({
      method: "POST",
      url: `/api/Products/Add`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * Update_PostAsync /api/Products/Update
   * 更新产品
   */
  static async Update_PostAsync(
    data: ProductEditModel,
    options?: AxiosRequestConfig
  ): Promise<ApiResult> {
    return apiOptions.request({
      method: "POST",
      url: `/api/Products/Update`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * Query_PostAsync /api/Products/Query
   * 查询产品
   */
  static async Query_PostAsync(
    data: SearchCriteria,
    options?: AxiosRequestConfig
  ): Promise<PagedEnumerable<Product>> {
    return requestPackedApi({
      method: "POST",
      url: `/api/Products/Query`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * Remove_PostAsync /api/Products/Remove
   * 移除产品
   */
  static async Remove_PostAsync(
    data: string[],
    options?: AxiosRequestConfig
  ): Promise<ApiResult> {
    return apiOptions.request({
      method: "POST",
      url: `/api/Products/Remove`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * AddType_PostAsync /api/Products/AddType
   * 添加产品类型
   */
  static async AddType_PostAsync(
    data: ProductTypeNewModel,
    options?: AxiosRequestConfig
  ): Promise<ApiResult> {
    return apiOptions.request({
      method: "POST",
      url: `/api/Products/AddType`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * UpdateType_PostAsync /api/Products/UpdateType
   * 更新产品类型
   */
  static async UpdateType_PostAsync(
    data: ProductTypeEditModel,
    options?: AxiosRequestConfig
  ): Promise<ApiResult> {
    return apiOptions.request({
      method: "POST",
      url: `/api/Products/UpdateType`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * QueryType_PostAsync /api/Products/QueryType
   * 产品类型查询
   */
  static async QueryType_PostAsync(
    data: SearchCriteria,
    options?: AxiosRequestConfig
  ): Promise<PagedEnumerable<ProductType>> {
    return requestPackedApi({
      method: "POST",
      url: `/api/Products/QueryType`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * RemoveType_PostAsync /api/Products/RemoveType
   * 移除产品类型
   */
  static async RemoveType_PostAsync(
    data: string[],
    options?: AxiosRequestConfig
  ): Promise<ApiResult> {
    return apiOptions.request({
      method: "POST",
      url: `/api/Products/RemoveType`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * AddBatch_PostAsync /api/Products/AddBatch
   * 添加产品批次
   */
  static async AddBatch_PostAsync(
    data: ProductBatchNewModel,
    options?: AxiosRequestConfig
  ): Promise<CodeViewGenModel[]> {
    return requestPackedApi({
      method: "POST",
      url: `/api/Products/AddBatch`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * UpdateBatch_PostAsync /api/Products/UpdateBatch
   * 更新产品批次
   */
  static async UpdateBatch_PostAsync(
    data: ProductBatchEditModel,
    options?: AxiosRequestConfig
  ): Promise<ApiResult> {
    return apiOptions.request({
      method: "POST",
      url: `/api/Products/UpdateBatch`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * QueryBatch_PostAsync /api/Products/QueryBatch
   * 产品批次查询
   */
  static async QueryBatch_PostAsync(
    data: SearchCriteria,
    options?: AxiosRequestConfig
  ): Promise<IPagedEnumerable<ProductBatchPageView>> {
    return requestPackedApi({
      method: "POST",
      url: `/api/Products/QueryBatch`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * SetPoint_PostAsync /api/Products/SetPoint
   * 产品积分设置
   */
  static async SetPoint_PostAsync(
    data: ProductPointEditModel,
    options?: AxiosRequestConfig
  ): Promise<ApiResult> {
    return apiOptions.request({
      method: "POST",
      url: `/api/Products/SetPoint`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * AddActivities_PostAsync /api/Products/AddActivities
   * 添加活动
   */
  static async AddActivities_PostAsync(
    data: ProductActivitiesNewModel,
    options?: AxiosRequestConfig
  ): Promise<ApiResult> {
    return apiOptions.request({
      method: "POST",
      url: `/api/Products/AddActivities`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * UpdateActivities_PostAsync /api/Products/UpdateActivities
   * 更新活动
   */
  static async UpdateActivities_PostAsync(
    data: ProductActivitiesEditModel,
    options?: AxiosRequestConfig
  ): Promise<ApiResult> {
    return apiOptions.request({
      method: "POST",
      url: `/api/Products/UpdateActivities`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * QueryActivities_PostAsync /api/Products/QueryActivities
   * 查询活动
   */
  static async QueryActivities_PostAsync(
    data: SearchCriteria,
    options?: AxiosRequestConfig
  ): Promise<PagedEnumerable<ProductActivities>> {
    return requestPackedApi({
      method: "POST",
      url: `/api/Products/QueryActivities`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * RemoveActivities_PostAsync /api/Products/RemoveActivities
   * 移除活动
   */
  static async RemoveActivities_PostAsync(
    data: string[],
    options?: AxiosRequestConfig
  ): Promise<ApiResult> {
    return apiOptions.request({
      method: "POST",
      url: `/api/Products/RemoveActivities`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
}
export class Referrals {
  /**
   * GetActivitytAsync /api/Referrals/GetActivityt
   * 获取活动
   */
  static async GetActivitytAsync(
    options?: AxiosRequestConfig
  ): Promise<ReferralActivity> {
    return requestPackedApi({
      method: "GET",
      url: `/api/Referrals/GetActivityt`,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * SetStatus_GetAsync /api/Referrals/SetStatus
   * 修改活动状态
   */
  static async SetStatus_GetAsync(
    params: { id: string; status?: ReferralActivityStatus },
    options?: AxiosRequestConfig
  ): Promise<ApiResult> {
    return apiOptions.request({
      method: "GET",
      url: `/api/Referrals/SetStatus`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * Update_PostAsync /api/Referrals/Update
   * 修改推广活动
   */
  static async Update_PostAsync(
    data: ReferralActivityEditModel,
    options?: AxiosRequestConfig
  ): Promise<ApiResult> {
    return apiOptions.request({
      method: "POST",
      url: `/api/Referrals/Update`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * GetReferralRecordPageView_PostAsync /api/Referrals/GetReferralRecordPageView
   * 推广记录汇总列
   */
  static async GetReferralRecordPageView_PostAsync(
    params: {
      activityId?: string;
      userKeyWord?: string;
      activityKeyWord?: string;
      offset?: number;
      limit?: number;
    },
    options?: AxiosRequestConfig
  ): Promise<IPagedEnumerable<ReferralRecordPageView>> {
    return requestPackedApi({
      method: "POST",
      url: `/api/Referrals/GetReferralRecordPageView`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * ReferralRewardRecordPageView_PostAsync /api/Referrals/ReferralRewardRecordPageView
   * 推广记录详情列
   */
  static async ReferralRewardRecordPageView_PostAsync(
    params: {
      activityId?: string;
      userKeyWord?: string;
      activityKeyWord?: string;
      offset?: number;
      limit?: number;
    },
    options?: AxiosRequestConfig
  ): Promise<IPagedEnumerable<ReferralRewardRecordPageViewModel>> {
    return requestPackedApi({
      method: "POST",
      url: `/api/Referrals/ReferralRewardRecordPageView`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
}
export class RolesManage {
  /**
   * RoleList_PostAsync /api/RolesManage/RoleList
   * 角色列表
   */
  static async RoleList_PostAsync(
    params: { roleName?: string; offset?: number; limit?: number },
    options?: AxiosRequestConfig
  ): Promise<IPagedEnumerable<Role>> {
    return requestPackedApi({
      method: "POST",
      url: `/api/RolesManage/RoleList`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
              * RoleAll_PostAsync /api/RolesManage/RoleAll
              * 角色列表
完整
              */
  static async RoleAll_PostAsync(
    params: { roleName?: string },
    options?: AxiosRequestConfig
  ): Promise<Role[]> {
    return requestPackedApi({
      method: "POST",
      url: `/api/RolesManage/RoleAll`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * SetUserRole_PostAsync /api/RolesManage/SetUserRole
   * 设置用户角色
   */
  static async SetUserRole_PostAsync(
    params: { userId: string; roleId: string; expirationTime?: Date },
    options?: AxiosRequestConfig
  ): Promise<ApiResult> {
    return apiOptions.request({
      method: "POST",
      url: `/api/RolesManage/SetUserRole`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * RemoveUserRole_PostAsync /api/RolesManage/RemoveUserRole
   * 删除用户角色
   */
  static async RemoveUserRole_PostAsync(
    params: { userId: string; roleId: string },
    options?: AxiosRequestConfig
  ): Promise<ApiResult> {
    return apiOptions.request({
      method: "POST",
      url: `/api/RolesManage/RemoveUserRole`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * CreateRole_PostAsync /api/RolesManage/CreateRole
   * 创建角色
   */
  static async CreateRole_PostAsync(
    params: { roleName?: string },
    options?: AxiosRequestConfig
  ): Promise<Role> {
    return requestPackedApi({
      method: "POST",
      url: `/api/RolesManage/CreateRole`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * Delete_PostAsync /api/RolesManage/Delete
   * 删除角色
   */
  static async Delete_PostAsync(
    params: { roleId: string },
    options?: AxiosRequestConfig
  ): Promise<boolean> {
    return requestPackedApi({
      method: "POST",
      url: `/api/RolesManage/Delete`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * ModifyRole_PostAsync /api/RolesManage/ModifyRole
   * 修改角色
   */
  static async ModifyRole_PostAsync(
    params: { roleId: string; roleName?: string },
    options?: AxiosRequestConfig
  ): Promise<ApiResult> {
    return apiOptions.request({
      method: "POST",
      url: `/api/RolesManage/ModifyRole`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * ModifyRoleMenu_PostAsync /api/RolesManage/ModifyRoleMenu
   * 修改角色菜单
   */
  static async ModifyRoleMenu_PostAsync(
    params: { roleId: string },
    data: string[],
    options?: AxiosRequestConfig
  ): Promise<ApiResult> {
    return apiOptions.request({
      method: "POST",
      url: `/api/RolesManage/ModifyRoleMenu`,
      data,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
}
export class Simulate {
  /**
   * GetSimulateStatusAsync /api/Simulate/GetSimulateStatus
   * 获取用户模拟状态
   */
  static async GetSimulateStatusAsync(
    options?: AxiosRequestConfig
  ): Promise<SimulateUserViewModel> {
    return requestPackedApi({
      method: "GET",
      url: `/api/Simulate/GetSimulateStatus`,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * SimulateLogin_PostAsync /api/Simulate/SimulateLogin
   * 模拟用户
   */
  static async SimulateLogin_PostAsync(
    params: {
      /**需要模拟的用户ID*/ userId: string;
    },
    options?: AxiosRequestConfig
  ): Promise<UserViewModel> {
    return requestPackedApi({
      method: "POST",
      url: `/api/Simulate/SimulateLogin`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * ExitSimulate_PostAsync /api/Simulate/ExitSimulate
   * 退出模拟
   */
  static async ExitSimulate_PostAsync(
    options?: AxiosRequestConfig
  ): Promise<ApiResult> {
    return apiOptions.request({
      method: "POST",
      url: `/api/Simulate/ExitSimulate`,
      responseType: "json",
      ...(options || {}),
    });
  }
}
export class SystemBaseInfo {
  /**
   * GetAsync /api/SystemBaseInfo/Get
   * 获取站点基本信息
   */
  static async GetAsync(options?: AxiosRequestConfig): Promise<SystemInfo> {
    return requestPackedApi({
      method: "GET",
      url: `/api/SystemBaseInfo/Get`,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * GetScanProtectionAsync /api/SystemBaseInfo/GetScanProtection
   * 获取防盗刷配置
   */
  static async GetScanProtectionAsync(
    options?: AxiosRequestConfig
  ): Promise<ScanProtection> {
    return requestPackedApi({
      method: "GET",
      url: `/api/SystemBaseInfo/GetScanProtection`,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * GetTenPayAsync /api/SystemBaseInfo/GetTenPay
   * 微信支付key
   */
  static async GetTenPayAsync(
    options?: AxiosRequestConfig
  ): Promise<TenPayApiKeyEntity> {
    return requestPackedApi({
      method: "GET",
      url: `/api/SystemBaseInfo/GetTenPay`,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * GetTencentMapKeyAsync /api/SystemBaseInfo/GetTencentMapKey
   * 腾讯地图key
   */
  static async GetTencentMapKeyAsync(
    options?: AxiosRequestConfig
  ): Promise<string> {
    return requestPackedApi({
      method: "GET",
      url: `/api/SystemBaseInfo/GetTencentMapKey`,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * VerifyHasPassword_GetAsync /api/SystemBaseInfo/VerifyHasPassword
   * 验证是否已经存在支付密码
   */
  static async VerifyHasPassword_GetAsync(
    options?: AxiosRequestConfig
  ): Promise<boolean> {
    return requestPackedApi({
      method: "GET",
      url: `/api/SystemBaseInfo/VerifyHasPassword`,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * UpdateTenPayKey_GetAsync /api/SystemBaseInfo/UpdateTenPayKey
   * 更新支付密码
   */
  static async UpdateTenPayKey_GetAsync(
    params: {
      /**可以通过VerifyHasPassword 知道是否要输入*/ oldKey?: string;
      newKey?: string;
    },
    options?: AxiosRequestConfig
  ): Promise<boolean> {
    return requestPackedApi({
      method: "GET",
      url: `/api/SystemBaseInfo/UpdateTenPayKey`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * SaveBalanceThreshold_GetAsync /api/SystemBaseInfo/SaveBalanceThreshold
   * 保存提醒阈值
   */
  static async SaveBalanceThreshold_GetAsync(
    params: {
      /**金额：分*/ key: number;
    },
    options?: AxiosRequestConfig
  ): Promise<ApiResult> {
    return apiOptions.request({
      method: "GET",
      url: `/api/SystemBaseInfo/SaveBalanceThreshold`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * GetBalanceThresholdAsync /api/SystemBaseInfo/GetBalanceThreshold
   * 获取提醒阈值
   */
  static async GetBalanceThresholdAsync(
    options?: AxiosRequestConfig
  ): Promise<number> {
    return requestPackedApi({
      method: "GET",
      url: `/api/SystemBaseInfo/GetBalanceThreshold`,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * SaveRemainingBalance_GetAsync /api/SystemBaseInfo/SaveRemainingBalance
   * 保存系统金额
   */
  static async SaveRemainingBalance_GetAsync(
    params: {
      /**金额：分*/ key: number;
    },
    options?: AxiosRequestConfig
  ): Promise<ApiResult> {
    return apiOptions.request({
      method: "GET",
      url: `/api/SystemBaseInfo/SaveRemainingBalance`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * GetRemainingBalanceAsync /api/SystemBaseInfo/GetRemainingBalance
   * 获取系统金额
   */
  static async GetRemainingBalanceAsync(
    options?: AxiosRequestConfig
  ): Promise<number> {
    return requestPackedApi({
      method: "GET",
      url: `/api/SystemBaseInfo/GetRemainingBalance`,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * BalanceWarning_GetAsync /api/SystemBaseInfo/BalanceWarning
   * 预警
   */
  static async BalanceWarning_GetAsync(
    options?: AxiosRequestConfig
  ): Promise<boolean> {
    return requestPackedApi({
      method: "GET",
      url: `/api/SystemBaseInfo/BalanceWarning`,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * Save_PostAsync /api/SystemBaseInfo/Save
   * 保存站点基本信息
   */
  static async Save_PostAsync(
    data: SystemInfo,
    options?: AxiosRequestConfig
  ): Promise<ApiResult> {
    return apiOptions.request({
      method: "POST",
      url: `/api/SystemBaseInfo/Save`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * SaveScanProtection_PostAsync /api/SystemBaseInfo/SaveScanProtection
   * 防盗刷配置
   */
  static async SaveScanProtection_PostAsync(
    data: ScanProtection,
    options?: AxiosRequestConfig
  ): Promise<ApiResult> {
    return apiOptions.request({
      method: "POST",
      url: `/api/SystemBaseInfo/SaveScanProtection`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * SaveTenPay_PostAsync /api/SystemBaseInfo/SaveTenPay
   * 微信支付key
   */
  static async SaveTenPay_PostAsync(
    data: TenPayApiKeyEntity,
    options?: AxiosRequestConfig
  ): Promise<ApiResult> {
    return apiOptions.request({
      method: "POST",
      url: `/api/SystemBaseInfo/SaveTenPay`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * SaveTencentMapKey_PostAsync /api/SystemBaseInfo/SaveTencentMapKey
   * 腾讯地图key
   */
  static async SaveTencentMapKey_PostAsync(
    params: { key?: string },
    options?: AxiosRequestConfig
  ): Promise<ApiResult> {
    return apiOptions.request({
      method: "POST",
      url: `/api/SystemBaseInfo/SaveTencentMapKey`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
}
export class UserManage {
  /**
   * GetUserListAsync /api/UserManage/GetUserList
   * 返回用户列表
   */
  static async GetUserListAsync(
    params: {
      /**角色Id*/ roleId?: string;
      /**用户名*/
      userName?: string;
      /**邮箱*/
      email?: string;
      /**电话*/
      phoneNumber?: string;
      offset?: number;
      limit?: number;
    },
    options?: AxiosRequestConfig
  ): Promise<IPagedEnumerable<UserViewModel>> {
    return requestPackedApi({
      method: "GET",
      url: `/api/UserManage/GetUserList`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * GetUserViewListAsync /api/UserManage/GetUserViewList
   * 返回用户列表
   */
  static async GetUserViewListAsync(
    params: {
      /**角色Id*/ roleId?: string;
      /**用户名*/
      userName?: string;
      /**邮箱*/
      email?: string;
      /**电话*/
      phoneNumber?: string;
      offset?: number;
      limit?: number;
    },
    options?: AxiosRequestConfig
  ): Promise<IPagedEnumerable<GuidIdNameViewModel>> {
    return requestPackedApi({
      method: "GET",
      url: `/api/UserManage/GetUserViewList`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * GetUserViewListRefAsync /api/UserManage/GetUserViewListRef
   *
   */
  static async GetUserViewListRefAsync(
    params: {
      roleId?: string;
      userName?: string;
      email?: string;
      phoneNumber?: string;
    },
    options?: AxiosRequestConfig
  ): Promise<GuidIdNameViewModel[]> {
    return requestPackedApi({
      method: "GET",
      url: `/api/UserManage/GetUserViewListRef`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * GetUserViewModelAsync /api/UserManage/GetUserViewModel
   * 用户信息
   */
  static async GetUserViewModelAsync(
    params: {
      /**用户ID*/ userId: string;
    },
    options?: AxiosRequestConfig
  ): Promise<UserViewModel> {
    return requestPackedApi({
      method: "GET",
      url: `/api/UserManage/GetUserViewModel`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * UserLoginLogList_GetAsync /api/UserManage/UserLoginLogList
   * 获取用户的登录日志
   */
  static async UserLoginLogList_GetAsync(
    params: { userId: string; offset?: number; limit?: number },
    options?: AxiosRequestConfig
  ): Promise<IPagedEnumerable<UserLoginLog>> {
    return requestPackedApi({
      method: "GET",
      url: `/api/UserManage/UserLoginLogList`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * LoginLogList_GetAsync /api/UserManage/LoginLogList
   * 获取所有用户的登录日志
   */
  static async LoginLogList_GetAsync(
    params: { offset?: number; limit?: number },
    options?: AxiosRequestConfig
  ): Promise<IPagedEnumerable<UserLoginLog>> {
    return requestPackedApi({
      method: "GET",
      url: `/api/UserManage/LoginLogList`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * RequestLogList_GetAsync /api/UserManage/RequestLogList
   * 获取所有用户的请求日志
   */
  static async RequestLogList_GetAsync(
    params: {
      /**用户行为*/ des?: string;
      offset?: number;
      limit?: number;
    },
    options?: AxiosRequestConfig
  ): Promise<IPagedEnumerable<UserRequestLog>> {
    return requestPackedApi({
      method: "GET",
      url: `/api/UserManage/RequestLogList`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * UserRequestLogList_GetAsync /api/UserManage/UserRequestLogList
   * 获取用户的请求日志
   */
  static async UserRequestLogList_GetAsync(
    params: {
      /**用户Id*/ userId: string;
      /**用户行为*/
      des?: string;
      offset?: number;
      limit?: number;
    },
    options?: AxiosRequestConfig
  ): Promise<IPagedEnumerable<UserRequestLog>> {
    return requestPackedApi({
      method: "GET",
      url: `/api/UserManage/UserRequestLogList`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * LockUser_GetAsync /api/UserManage/LockUser
   * 禁用用户
   */
  static async LockUser_GetAsync(
    params: { userId: string },
    options?: AxiosRequestConfig
  ): Promise<ApiResult> {
    return apiOptions.request({
      method: "GET",
      url: `/api/UserManage/LockUser`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * UnLockUser_GetAsync /api/UserManage/UnLockUser
   * 取消禁用用户
   */
  static async UnLockUser_GetAsync(
    params: {
      /**用户Id*/ userId: string;
    },
    options?: AxiosRequestConfig
  ): Promise<ApiResult> {
    return apiOptions.request({
      method: "GET",
      url: `/api/UserManage/UnLockUser`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * EditUserModel_PostAsync /api/UserManage/EditUserModel
   * 修改用户信息
   */
  static async EditUserModel_PostAsync(
    data: UserEditModel,
    options?: AxiosRequestConfig
  ): Promise<UserViewModel> {
    return requestPackedApi({
      method: "POST",
      url: `/api/UserManage/EditUserModel`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * DeleteUser_PostAsync /api/UserManage/DeleteUser
   * 删除用户
   */
  static async DeleteUser_PostAsync(
    params: {
      /**用户Id*/ userId: string;
    },
    options?: AxiosRequestConfig
  ): Promise<boolean> {
    return requestPackedApi({
      method: "POST",
      url: `/api/UserManage/DeleteUser`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * EditUserPassword_PostAsync /api/UserManage/EditUserPassword
   * 修改用户的密码
   */
  static async EditUserPassword_PostAsync(
    data: UserPasswordChangeEditModel,
    options?: AxiosRequestConfig
  ): Promise<ApiResult> {
    return apiOptions.request({
      method: "POST",
      url: `/api/UserManage/EditUserPassword`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * EditUserExpiration_PostAsync /api/UserManage/EditUserExpiration
   * 修改用户过期时间
   */
  static async EditUserExpiration_PostAsync(
    data: UserExpirationEditModel,
    options?: AxiosRequestConfig
  ): Promise<ApiResult> {
    return apiOptions.request({
      method: "POST",
      url: `/api/UserManage/EditUserExpiration`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * CreateUser_PostAsync /api/UserManage/CreateUser
   * 创建用户
   */
  static async CreateUser_PostAsync(
    data: UserCreateModel,
    options?: AxiosRequestConfig
  ): Promise<UserViewModel> {
    return requestPackedApi({
      method: "POST",
      url: `/api/UserManage/CreateUser`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * EditUserModifyPasswordEnd_PostAsync /api/UserManage/EditUserModifyPasswordEnd
   * 修改用户密码过期时间
   */
  static async EditUserModifyPasswordEnd_PostAsync(
    data: UserExpirationEditModel,
    options?: AxiosRequestConfig
  ): Promise<ApiResult> {
    return apiOptions.request({
      method: "POST",
      url: `/api/UserManage/EditUserModifyPasswordEnd`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
}
export class WeChatApiKeys {
  /**
   * GetAppApiKeyAsync /api/WeChatApiKeys/GetAppApiKey
   * 查找
   */
  static async GetAppApiKeyAsync(
    options?: AxiosRequestConfig
  ): Promise<WeChatApiKeyEntity> {
    return requestPackedApi({
      method: "GET",
      url: `/api/WeChatApiKeys/GetAppApiKey`,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * GetAppCallbackTokenAsync /api/WeChatApiKeys/GetAppCallbackToken
   * 查找
   */
  static async GetAppCallbackTokenAsync(
    options?: AxiosRequestConfig
  ): Promise<WeChatCallbackToken> {
    return requestPackedApi({
      method: "GET",
      url: `/api/WeChatApiKeys/GetAppCallbackToken`,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * SaveAppApiKey_PostAsync /api/WeChatApiKeys/SaveAppApiKey
   * 保存
   */
  static async SaveAppApiKey_PostAsync(
    data: WeChatApiKeyEntity,
    options?: AxiosRequestConfig
  ): Promise<ApiResult> {
    return apiOptions.request({
      method: "POST",
      url: `/api/WeChatApiKeys/SaveAppApiKey`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
}
export class HomePortal {
  /**
   * GetFileUrlAsync /papi/HomePortal/GetFileUrl
   * 文件直链获取
   */
  static async GetFileUrlAsync(
    params: { id: string; imagesResize?: string },
    options?: AxiosRequestConfig
  ): Promise<string> {
    return requestPackedApi({
      method: "GET",
      url: `/papi/HomePortal/GetFileUrl`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * GeCategoryPaged_GetAsync /papi/HomePortal/GeCategoryPaged
   * 获取分类列表
   */
  static async GeCategoryPaged_GetAsync(
    params: { type?: string; name?: string; menuShow?: boolean },
    options?: AxiosRequestConfig
  ): Promise<InformationCategoryView[]> {
    return requestPackedApi({
      method: "GET",
      url: `/papi/HomePortal/GeCategoryPaged`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * GetByInformationIdAsync /papi/HomePortal/GetByInformationId
   * 获取文章最新版本
   */
  static async GetByInformationIdAsync(
    params: {
      /**informationId*/ informationId: string;
    },
    options?: AxiosRequestConfig
  ): Promise<InformationView> {
    return requestPackedApi({
      method: "GET",
      url: `/papi/HomePortal/GetByInformationId`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * GetByInformationByTypeIdAsync /papi/HomePortal/GetByInformationByTypeId
   * 获取文章最新版根据分类
   */
  static async GetByInformationByTypeIdAsync(
    params: {
      typeId: string;
      title?: string;
      content?: string;
      keyword?: string;
      isTop?: boolean;
      author?: string;
      startTime?: Date;
      endTime?: Date;
      sortBy?: InformationSortBy;
      isDescending?: boolean;
      isCover?: boolean;
      tag?: string;
    },
    options?: AxiosRequestConfig
  ): Promise<InformationView[]> {
    return requestPackedApi({
      method: "GET",
      url: `/papi/HomePortal/GetByInformationByTypeId`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * GetAsync /papi/HomePortal/Get
   * 获取站点基本信息
   */
  static async GetAsync(options?: AxiosRequestConfig): Promise<SystemInfo> {
    return requestPackedApi({
      method: "GET",
      url: `/papi/HomePortal/Get`,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * ProductById_GetAsync /papi/HomePortal/ProductById
   * 获取产品详情
   */
  static async ProductById_GetAsync(
    params: { id?: string },
    options?: AxiosRequestConfig
  ): Promise<Product> {
    return requestPackedApi({
      method: "GET",
      url: `/papi/HomePortal/ProductById`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * ProductTypeGetAsync /papi/HomePortal/ProductTypeGet
   * 获取产品类型
   */
  static async ProductTypeGetAsync(
    options?: AxiosRequestConfig
  ): Promise<ProductType[]> {
    return requestPackedApi({
      method: "GET",
      url: `/papi/HomePortal/ProductTypeGet`,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * FindDesignByKey_GetAsync /papi/HomePortal/FindDesignByKey
   * 查询表单结构
   */
  static async FindDesignByKey_GetAsync(
    params: { key?: string },
    options?: AxiosRequestConfig
  ): Promise<FormDesign> {
    return requestPackedApi({
      method: "GET",
      url: `/papi/HomePortal/FindDesignByKey`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * FindDesignById_GetAsync /papi/HomePortal/FindDesignById
   * 查询表单结构
   */
  static async FindDesignById_GetAsync(
    params: { id: string },
    options?: AxiosRequestConfig
  ): Promise<FormDesign> {
    return requestPackedApi({
      method: "GET",
      url: `/papi/HomePortal/FindDesignById`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * GetInformation_PostAsync /papi/HomePortal/GetInformation
   * 查找文章
   */
  static async GetInformation_PostAsync(
    params: {
      title?: string;
      content?: string;
      keyword?: string;
      categoryName?: string;
      isTop?: boolean;
      author?: string;
      startTime?: Date;
      endTime?: Date;
      sortBy?: InformationSortBy;
      isDescending?: boolean;
      isCover?: boolean;
      tag?: string;
      offset?: number;
      limit?: number;
    },
    data: string[],
    options?: AxiosRequestConfig
  ): Promise<IPagedEnumerable<InformationPageView>> {
    return requestPackedApi({
      method: "POST",
      url: `/papi/HomePortal/GetInformation`,
      data,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * ProductGet_PostAsync /papi/HomePortal/ProductGet
   * 获取产品列表
   */
  static async ProductGet_PostAsync(
    params: {
      typeId?: string;
      keyWowrd?: string;
      offset?: number;
      limit?: number;
    },
    data: string[],
    options?: AxiosRequestConfig
  ): Promise<IPagedEnumerable<Product>> {
    return requestPackedApi({
      method: "POST",
      url: `/papi/HomePortal/ProductGet`,
      data,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * SubmitByDesign_PostAsync /papi/HomePortal/SubmitByDesign
   * 提交表单内容
   */
  static async SubmitByDesign_PostAsync(
    data: FormData,
    options?: AxiosRequestConfig
  ): Promise<FormData> {
    return requestPackedApi({
      method: "POST",
      url: `/papi/HomePortal/SubmitByDesign`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
}
/**
 * Files_GetAsync /files/{id}
 * 跳转文件
 */ export async function Files_GetAsync(
  params: { id: string; imagesResize?: string },
  options?: AxiosRequestConfig
): Promise<Blob> {
  return request({
    method: "GET",
    url: `/files/${params.id}`,
    params,
    responseType: "json",
    ...(options || {}),
  });
}
