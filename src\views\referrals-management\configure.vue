<!--
 * @Description:
 * @Author: 景 彡
 * @Date: 2024-11-12 15:20:36
 * @LastEditors: 景 彡
-->
<template>
  <div class="h-full p-16px bg">
    <a-card title="推广返佣配置" style="width: 100%">
      <div class="mb-16px">
        <span class="inline-block w-140px text-center">返佣开关：</span>
        <a-radio-group :value="activityStatus" name="radioGroup" @change="onChange">
          <a-radio :value="ReferralActivityStatus.已启用">开启</a-radio>
          <a-radio :value="ReferralActivityStatus.未启用">关闭</a-radio>
        </a-radio-group>
      </div>
      <c-pro-form
        ref="proFormRef"
        v-model:value="model"
        :read-only="activityStatus === ReferralActivityStatus.已启用"
        :label-col="{ span: 4 }"
        :fields="fields"
        :descriptions="{ column: 1, bordered: true, labelStyle: { width: '140px' } }"
      >
        <template #start>
          <c-range-picker
            v-model:start-time="model!.start!"
            v-model:end-time="model!.end!"
            :read-only="readonly"
            show-time
          >
            <template v-if="!model!.end" #readOnly>
              {{ model!.start ? `${dateTime(model!.start)}-` : '' }}  长期有效
            </template>
          </c-range-picker>
        </template>
      </c-pro-form>
      <div class="mt16px rounded-2 bg-#CCEBDA p-16px">
        <div>请注意：</div>
        <div class="mt-16px pl-16px">
          <p>1. 为防止数据造假，推广注册成功的用户，需激活部分产品二维码方视为有效推广注册，才能获得推广佣金；</p>
          <p>2. 一级返佣是指推广人A的邀请激活用户B时的返佣，二级返佣是指推广人B的邀请激活用户C时，首个推广人A所获取的返佣。</p>
        </div>
      </div>
      <div class="ml-140px mt-16px">
        <a-button v-show="activityStatus === ReferralActivityStatus.未启用" type="primary" @click="onSave">保存配置</a-button>
      </div>
    </a-card>
  </div>
</template>

<script lang='ts' setup>
import type { FormField } from 'ch2-components/types/pro-form/types'
import * as api from '@/api/'
import { ReferralActivityEditModel, ReferralActivityStatus } from '@/api/models'
import { message } from 'ant-design-vue'
import ReferralRewardInput from './components/ReferralRewardInput.vue'

definePage({
  meta: {
    title: '推广返佣配置',
  },
})

const readonly = ref()

const activityStatus = ref(ReferralActivityStatus.未启用)

const model = ref(new ReferralActivityEditModel())

const currentId = ref('')

const proFormRef = useTemplateRef('proFormRef')

const fields = [
  { label: '标题', el: 'input', prop: 'title', required: true, attrs: { placeholder: '请输入标题' }, formItem: {} },
  { label: '描述', el: 'input', prop: 'description', attrs: { placeholder: '请输入描述' }, formItem: {} },
  { label: '激活标准', el: 'input-number', prop: 'number', attrs: { placeholder: '激活标准扫码次数' }, formItem: {} },
  { label: '活动时间', el: 'input', prop: 'start', attrs: { placeholder: '为空则长期有效' }, formItem: {} },
  { label: '一级返佣奖励', el: ReferralRewardInput, prop: 'firstLevel', attrs: { placeholder: '请输入描述' }, formItem: {} },
  { label: '二级返佣奖励', el: ReferralRewardInput, prop: 'secondLevel', attrs: { placeholder: '请输入描述' }, formItem: {} },

] as FormField[]

async function onChange(e) {
  await api.Referrals.SetStatus_GetAsync({ id: currentId.value, status: e.target.value })
  activityStatus.value = e.target.value

  message.success('状态修改成功')
}

async function getData() {
  const res = await api.Referrals.GetActivitytAsync()
  if (res) {
    currentId.value = res.id
    activityStatus.value = res.status
    model.value = viewModelToEditModel(res, model.value)
  }
}

async function onSave() {
  await api.Referrals.Update_PostAsync(model.value)
  message.success('保存成功')
}

onMounted(() => {
  getData()
})
</script>

<style scoped>

</style>
