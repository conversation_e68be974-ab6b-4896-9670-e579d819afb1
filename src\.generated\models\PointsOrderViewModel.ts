import { ClientUserInfoBaseView } from "./ClientUserInfoBaseView";
import { PointsOrderStatus } from "./PointsOrderStatus";
import { ShippingAddressBase } from "./ShippingAddressBase";
export class PointsOrderViewModel {
  /**推广用户*/
  userInfo?: ClientUserInfoBaseView | null | undefined = null;
  /**商品数量*/
  count: number = 0;
  /**积分价格（合计）*/
  pointsTotal: number = 0;
  pointsCommodityId?: GUID = null;
  /**商品标题*/
  title?: string | null | undefined = null;
  /**图片（Path*/
  image?: string | null | undefined = null;
  /**描述*/
  description?: string | null | undefined = null;
  /**规格（18kg/桶）*/
  specs?: string | null | undefined = null;
  /**积分价格*/
  points: number = 0;
  /**订单状态*/
  status: PointsOrderStatus = 0;
  /**下单时间*/
  time: Dayjs = dayjs();
  /**备注*/
  remarks?: string | null | undefined = null;
  /**快递单号*/
  trackingNumber?: string | null | undefined = null;
  /**收获地址*/
  address?: ShippingAddressBase | null | undefined = null;
  /**主键*/
  id: GUID = "00000000-0000-0000-0000-000000000000";
}
