<!--
 * @Description:
 * @Author: 景 彡
 * @Date: 2024-11-01 16:00:40
 * @LastEditors: 景 彡
-->
<template>
  <div class="h-full">
    <c-pro-table
      ref="proTableRef" v-model:search-form="searchForm" :columns="columns"
      :api="api.Feedbacksontroller.GetPaged_PostAsync" row-key="id" align="center" immediate serial-number
      :scroll="{ x: 1440 }"
    >
      <template #header>
        <a-button type="primary" ghost danger @click="() => openVerificationQR = true">
          <c-icon-issues-close-outlined />核验二维码使用情况
        </a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <div v-if="column.dataIndex === 'images' && record.images" class="flex flex-wrap gap8px">
          <ImageView
            v-for="item, index in record.images" :key="index"
            style="width: 50px; height: 50px; object-fit: cover" :src="(item)"
          >
            <template #previewMask>
              <div class="flex gap8px">
                <c-icon-eye-outlined />
                <c-icon-qrcode-outlined title="识别二维码" @click.stop="onScanCode(joinFilePathById(item))" />
              </div>
            </template>
          </ImageView>
        </div>

        <div v-if="column.dataIndex === 'description'">
          <Description :text="record.description" @select-code="selectCode" />
        </div>

        <div v-if="column.dataIndex === 'status'">
          <a-tag v-if="record.status === FeedbackStatus.已回复" color="success">{{ FeedbackStatus[record.status] }}</a-tag>
          <a-popconfirm
            v-else
            title="确认设置为已回复?"
            @confirm="onSet(record)"
          >
            <a-tag color="default" title="设置为已回复">{{ FeedbackStatus[record.status] }}</a-tag>
          </a-popconfirm>
        </div>
      </template>
    </c-pro-table>
    <VerificationQR v-model:open="openVerificationQR" :img-url="qrUrl" :qr-code="qrCode" />
  </div>
</template>

<script lang='ts' setup>
import type { FeedbackView } from '@/api/models'
import type { ColumnProps } from 'ch2-components/types/pro-table/types'
import * as api from '@/api/'
import { FeedbackStatus, FeedbackType } from '@/api/models'
import { message } from 'ant-design-vue'
import Description from './components/Description.vue'

definePage({
  meta: {
    title: '反馈信息管理',
    layoutRoute: {
      meta: {
        layout: 'admin',
        title: '反馈信息管理',
        local: true,
        icon: 'MessageOutlined',
        order: 6,
      },
    },
  },
})

const proTableRef = useTemplateRef('proTableRef')

const searchForm = ref({
  keyWord: '',
  type: null,
  status: null,
})

const columns = reactive<ColumnProps<FeedbackView>[]>([
  { dataIndex: 'description', title: '描述', align: 'left' },
  { dataIndex: 'keyWord', title: '关键词', width: 300, isShow: false, search: { el: 'input', method: 'GET', emptyValue: '' } },
  {
    dataIndex: 'type',
    title: '反馈类型',
    width: 180,
    enum: FeedbackType,
    search: {
      el: 'enum-select',
      method: 'GET',
      attrs: {
        enum: FeedbackType,
      },
    },
  },
  { dataIndex: 'images', title: '图片', align: 'left' },
  { dataIndex: 'phoneNumber', title: '联系方式', width: 160 },
  {
    dataIndex: 'status',
    title: '状态',
    fixed: 'right',
    width: 120,
    search: {
      el: 'enum-select',
      method: 'GET',
      attrs: {
        enum: FeedbackStatus,
      },
    },
  },
])

const openVerificationQR = ref(false)

const qrUrl = ref('')

const qrCode = ref('')

function selectCode(code: string) {
  qrCode.value = code
  qrUrl.value = ''
  openVerificationQR.value = true
}

function onScanCode(url: string) {
  qrCode.value = ''
  qrUrl.value = url
  openVerificationQR.value = true
}

async function onSet(record: FeedbackView) {
  await api.Feedbacksontroller.SetStatus_GetAsync({ id: record.id })
  proTableRef.value?.refresh()
  message.success('设置成功')
}
</script>

<style scoped></style>
