FROM docker.fastoa.co/node:20-alpine AS build
WORKDIR /code
COPY package*.json pnpm-lock.yaml .npmrc ./
RUN npm install pnpm -g
RUN pnpm install --ignore-scripts
COPY . .
ENV NODE_OPTIONS="--max-old-space-size=8192"
RUN pnpm run build

FROM docker.fastoa.co/nginx-br:1.20.1-alpine AS final
RUN rm -rf /usr/share/nginx/html
RUN rm /etc/nginx/conf.d/default.conf
# RUN yum install  git
WORKDIR /usr/share/nginx/html
COPY ./docker/nginx.sites.conf /etc/nginx/conf.d/
COPY ./docker/certs /etc/nginx/testcerts
COPY --from=build /code/dist .
