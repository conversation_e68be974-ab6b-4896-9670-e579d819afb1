<!--
 * @Description:编辑文章标题和正文内容
 * @Author: nono
 * @Date: 2023-06-12 11:54:16
 * @LastEditors: 景 彡
 * @LastEditTime: 2025-04-09 11:37:51
-->
<template>
  <a-drawer v-model:open="modalState" destroy-on-close :mask-closable="false" width="100%" placement="right" title="新增/编辑活动" @close="close">
    <a-spin :spinning="loading">
      <div class="flex">
        <a-card style="width: 600px">
          <c-form ref="formRef" :label-col="{ xs: { span: 6 }, md: { span: 4 } }" :model="form">
            <c-form-item has-feedback label="标题" name="value1" :rules="[{ required: true, message: '请输入标题!' }]">
              <c-input v-model:value="form.value1!" placeholder="请输入标题" allow-clear />
            </c-form-item>
            <c-form-item has-feedback label="排序" name="description">
              <a-input-number v-model:value="form.sort" :min="0" />
            </c-form-item>
            <c-form-item has-feedback label="封面" name="cover">
              <div v-if="form.image" class="coverBox size-140px overflow-hidden">
                <ImageView :src="(form.image)" alt="avatar" :preview="true" :del-ico="true" style="height: 140px; width:140px ; object-fit:cover" @del-image="() => form.image = ''" />
              </div>
              <a-button v-else type="dashed" block class="size-25" @click="avatarUpload">
                <template #icon>
                  <c-icon-plus-outlined />
                </template>
                上传
              </a-button>
            </c-form-item>

            <c-form-item label="类型" name="type">
              <c-enum-select
                v-model:value="form.type"
                :enum="models.CarouselType"
                placeholder="请选择"
                @change="form.value2 = ''"
              />
            </c-form-item>
            <c-form-item v-if="form.type === models.CarouselType.公告通知" label="选择外链文章" name="value2">
              <c-select
                v-model:value="form.value2!"
                :api="api.Notices.FindNotices_GetAsync"
                :params="{ limit: 10, offset: 0 }"

                immediate page
                placeholder="选择"
                :field-names="{ value: 'id', label: 'title' }"
              />
            </c-form-item>
            <c-form-item v-if="form.type === models.CarouselType.抽奖活动" label="选择抽奖活动" name="value2">
              <c-select
                v-model:value="form.value2!"
                :api="api.Lotterys.LotteryActivityPage_PostAsync"
                :params="{ limit: 10, offset: 0 }"
                page
                immediate
                placeholder="选择抽奖活动"
                :field-names="{ value: 'id', label: 'name' }"
              />
            </c-form-item>
            <c-form-item v-if="form.type === models.CarouselType.兑换商品" label="选择商品" name="value2">
              <c-select
                v-model:value="form.value2!"
                :api="api.PointsMalls.GetCommodityPage_PostAsync"
                :params="{ limit: 10, offset: 0 }"
                page
                immediate
                placeholder="选择商品"
                :field-names="{ value: 'id', label: 'title' }"
              />
            </c-form-item>
            <c-form-item v-if="form.type === models.CarouselType.刚玉产品" label="选择刚玉产品" name="value2">
              <c-select
                v-model:value="form.value2!"
                :api="(...agrs) => tableSearch(api.Products.Query_PostAsync, agrs)"
                page
                immediate
                placeholder="选择刚玉产品"
                :field-names="{ value: 'id', label: 'name' }"
              />
            </c-form-item>
            <c-form-item v-if="form.type === models.CarouselType.外链文章" label="外链文章url" name="value2" :rules="[{ required: true, message: '请输入外链文章url!' }]">
              <a-input v-model:value="form.value2!" style="width: 100%" placeholder="请输入外链文章url" allow-clear />
            </c-form-item>
            <c-form-item label="状态" name="status">
              <c-enum-select
                v-model:value="form.status"
                :enum="models.CarouselStatus"
                placeholder="请选择"
              />
            </c-form-item>
            <c-form-item label="分享次数上线" name="count">
              <a-input-number v-model:value="form.count" :min="0" style="width: 100%" />
            </c-form-item>
            <c-form-item label="转发积分" name="points">
              <a-input-number v-model:value="form.points" :min="0" style="width: 100%" />
            </c-form-item>
          </c-form>
        </a-card>

        <div v-show="form.type === models.CarouselType.自定义文章" :calss="{ py16px: readOnly }" class="ml8px w0 flex-1 rounded-4px shadow">
          <Editor v-model:value="form.value2!" closure />
        </div>
      </div>
    </a-spin>
    <template v-if="!readOnly" #extra>
      <a-button style="margin-right: 8px" @click="close">取消</a-button>
      <a-button type="primary" @click="onSave()">保存</a-button>
    </template>
  </a-drawer>
</template>

<script lang="ts" setup>
import * as api from '@/api'
import * as models from '@/api/models'
import { message } from 'ant-design-vue'

const props = defineProps<{ readOnly?: boolean, id?: GUID }>()

const emit = defineEmits(['success'])

definePage({
  meta: {
    title: '新增活动',
    hidden: true,
    hiddenBreadcrumb: true,
  },
})

const modalState = defineModel('open', { default: false })

watch(props, () => {
  if (Guid.isNotNull(props.id) && modalState.value === true) {
    getArticleData(props.id!)
  }
}, { immediate: true })

// useBlockClosure()

const loading = ref<boolean>(false)

const formRef = useTemplateRef('formRef') // form表单ref

const form = ref<models.CarouselConfigEditModel>(new models.CarouselConfigEditModel()) // 表单

async function getArticleData(id: string) {
  try {
    form.value = await api.CarouselConfigs.GetAsync({ id })
  }
  catch (error: any) {
    message.error(error.message)
  }
}

function avatarUpload() {
  useFileMangerModal((files) => {
    form.value.image = files[0]?.id
  }, { multiple: false, immediateReturn: true, menu: [models.FileType.图片] })
}

async function onSave(_f = false) {
  if (!form.value.image)
    return message.error('请上传封面')
  if (form.value.type === models.CarouselType.自定义文章 && !form.value.value2)
    return message.error('请再右侧填写文章内容')
  await formRef.value?.baseEl?.validate().then(async () => {
    try {
      loading.value = true
      if (Guid.isNotNull(form.value.id))
        await api.CarouselConfigs.Update_PostAsync(form.value)
      else await api.CarouselConfigs.Add_PostAsync(form.value)
      emit('success')
      message.success('活动保存成功！')
      loading.value = false
      modalState.value = false
    }
    catch (error: any) {
      message.error(`活动更新失败：${error.message}`)
      loading.value = false
    }
  }).catch((val: any) => {
    console.log('%c [ val ]-92', 'font-size:13px; background:pink; color:#bf2c9f;', val)
  })
}

function close() {
  console.log('%c [  ]-179', 'font-size:13px; background:pink; color:#bf2c9f;', 8888)
  form.value = new models.CarouselConfigEditModel()
  modalState.value = false
}
</script>

<style scoped lang="less">
html,
body {
  height: 100%;
  overflow-y: hidden;
  overflow-x: hidden;
  color: #333;
}
.box {
  height: 100%;
  width: 100%;
  margin: auto;
}
.header-toolbar {
  width: 100%;
  border-bottom: 1px solid #e8e8e8;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.12);
  .editor-toolbar {
    background-color: #fcfcfc;
    display: flex;
    :deep(.w-e-drop-panel) {
      z-index: 99999;
    }
    .btn-box {
      margin-left: 10px;
      height: 40px;
      line-height: 40px;
    }
  }
}

#content {
  width: 100%;
  background-color: rgb(245, 245, 245);
  position: relative;
  display: flex;
}

#editor-container {
  max-width: 1200px;
  flex: 1;
  margin: 0 20px;
  background-color: #fff;
  border: 1px solid #e8e8e8;
  box-shadow: 0 2px 10px rgb(0 0 0 / 12%);
  border-radius: 4px;
  :deep(table) {
    min-width: 50px !important;
  }
}

#title-container {
  padding: 20px 0;
  border-bottom: 1px solid #e8e8e8;
  padding: 15px;
}

#title-container input {
  font-size: 30px;
  border: 0;
  outline: none;
  width: 100%;
  line-height: 1;
}

.editor-text-area {
  min-height: 100vh;
  height: fit-content !important;
  margin-top: 16px;
}
:deep(.ant-input:focus, .ant-input-focused) {
  box-shadow: none;
}
:deep(.w-e-bar-item-group .w-e-bar-item-menus-container) {
  z-index: 22;
}
</style>
