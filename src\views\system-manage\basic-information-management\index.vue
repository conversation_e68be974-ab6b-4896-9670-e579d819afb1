<!--
 * @Description:
 * @Author: 景 彡
 * @Date: 2024-11-01 11:50:33
 * @LastEditors: 景 彡
-->
<template>
  <div class="h-full p-16px py-32px c-bg">
    <a-form
      :model="form"
      name="basic"
      :label-col="{ span: 4 }"
      :wrapper-col="{ span: 16 }"
      autocomplete="off"
      @finish="onFinish"
    >
      <a-form-item
        label="站点标题"
        name="webTitle"
        :rules="[{ required: true, message: '请输入站点标题' }]"
      >
        <a-input v-model:value="form.webTitle!" />
      </a-form-item>

      <a-form-item
        label="站点副标题"
        name="webSubTitle"
      >
        <a-input v-model:value="form.webSubTitle!" />
      </a-form-item>

      <a-form-item
        label="网站图标"
        name="favicon"
      >
        <div v-if="form.favicon" class="coverBox size-140px overflow-hidden">
          <ImageView :src="(form.favicon!)" alt="avatar" :preview="true" :del-ico="true" style="height: 140px; width:140px ; object-fit:cover" @del-image="() => form.favicon = ''" />
        </div>
        <a-button v-else type="dashed" block class="size-25" style="height: 140px; width:140px" @click="avatarUpload">
          <template #icon>
            <c-icon-plus-outlined />
          </template>
          上传
        </a-button>
      </a-form-item>

      <a-form-item
        label="门户网站Url"
        name="siteUrl"
      >
        <a-input v-model:value="form.siteUrl!" />
      </a-form-item>

      <a-form-item
        label="管理站点Url"
        name="manageSiteUrl"
      >
        <a-input v-model:value="form.manageSiteUrl!" />
      </a-form-item>

      <a-form-item
        label="appUrl"
        name="appUrl"
      >
        <a-input v-model:value="form.appUrl!" />
      </a-form-item>

      <a-form-item
        label="备案号"
        name="registration"
      >
        <a-input v-model:value="form.registration!" />
      </a-form-item>

      <a-form-item
        label="公安备案号"
        name="publicSecurityRegistration"
      >
        <a-input v-model:value="form.publicSecurityRegistration!" />
      </a-form-item>
      <a-form-item
        label="客服联系方式"
        name="phone"
      >
        <a-input v-model:value="form.phone!" />
      </a-form-item>
      <a-form-item
        label="邮箱"
        name="email"
      >
        <a-input v-model:value="form.email!" />
      </a-form-item>
      <a-form-item
        label="工作时间"
        name="gzsj"
      >
        <a-input v-model:value="form.gzsj!" />
      </a-form-item>
      <a-form-item
        label="招商电话"
        name="zsdh"
      >
        <a-input v-model:value="form.zsdh!" />
      </a-form-item>
      <a-form-item
        label="联系地址"
        name="lxdz"
      >
        <a-input v-model:value="form.lxdz!" />
      </a-form-item>

      <a-form-item :wrapper-col="{ offset: 4, span: 16 }">
        <a-button type="primary" html-type="submit">保存</a-button>
      </a-form-item>
    </a-form>
  </div>
</template>

<script lang='ts' setup>
import * as api from '@/.generated/apis'
import { FileType, SystemInfo } from '@/.generated/models'
import { message } from 'ant-design-vue'

definePage({
  meta: {
    title: '系统基本信息管理',
    local: true,
  },
})

const form = ref(new SystemInfo())

function avatarUpload() {
  useFileMangerModal((files) => {
    form.value.favicon = files[0]?.id
  }, { multiple: false, immediateReturn: true, menu: [FileType.图片] })
}

async function onFinish() {
  await api.SystemBaseInfo.Save_PostAsync(form.value)
  message.success('保存成功')
}

onMounted(() => {
  api.SystemBaseInfo.GetAsync().then((res) => {
    form.value = res
  })
})
</script>

<style scoped>

</style>
