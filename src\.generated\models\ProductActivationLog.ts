import { PointsTransaction } from "./PointsTransaction";
import { CashbackRecord } from "./CashbackRecord";
import { LotteryCountChange } from "./LotteryCountChange";
/**产品激活日志*/
export class ProductActivationLog {
  /**防伪编码ID*/
  codeId: GUID = "00000000-0000-0000-0000-000000000000";
  /**产品ID*/
  productId: GUID = "00000000-0000-0000-0000-000000000000";
  /**产品名称*/
  productName: string = "";
  /**漆工ID*/
  clientUserId?: GUID = null;
  /**漆工昵称（而非用户名）*/
  userName?: string | null | undefined = null;
  /**电话名称*/
  userPhoneNumber?: string | null | undefined = null;
  /**激活时间*/
  activationTime: Dayjs = dayjs();
  /**激活批次*/
  productBatch: GUID = "00000000-0000-0000-0000-000000000000";
  /**批次时间*/
  productBatchTime: Dayjs = dayjs();
  /**详细地址*/
  streetAddress: string = "";
  /**区*/
  district?: string | null | undefined = null;
  /**城市*/
  city?: string | null | undefined = null;
  /**省份*/
  province?: string | null | undefined = null;
  /**经度*/
  latitude?: number | null | undefined = null;
  /**纬度*/
  longitude?: number | null | undefined = null;
  /**是否领取奖励*/
  isActivation: boolean = false;
  /**是否二次扫码*/
  isReactivated: boolean = false;
  /**是否过期*/
  isError: boolean = false;
  /**异常信息*/
  message?: string | null | undefined = null;
  /**产品类型*/
  typeId: GUID = "00000000-0000-0000-0000-000000000000";
  /**产品类型名称*/
  typeName?: string | null | undefined = null;
  /**获得的积分*/
  points: number = 0;
  /**积分交易记录*/
  pointsTransactionId?: GUID = null;
  /**积分交易记录*/
  pointsTransaction?: PointsTransaction | null | undefined = null;
  /**获得的返现*/
  cashback: number = 0;
  /**返现记录*/
  cashbackRecordId?: GUID = null;
  /**返现记录*/
  cashbackRecord?: CashbackRecord | null | undefined = null;
  /**抽奖活动ID*/
  sweepstakesId: GUID = "00000000-0000-0000-0000-000000000000";
  /**抽奖次数*/
  sweepstakesCount: number = 0;
  lotteryCountChangeId?: GUID = null;
  /**用户抽奖次数修改记录*/
  lotteryCountChange?: LotteryCountChange | null | undefined = null;
  /**主键*/
  id: GUID = "00000000-0000-0000-0000-000000000000";
}
