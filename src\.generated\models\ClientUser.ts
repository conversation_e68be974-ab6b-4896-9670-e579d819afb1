import { IdentityUser } from "./IdentityUser";
import { UserRole } from "./UserRole";
import { Role } from "./Role";
/**用户（漆工）*/
export class ClientUser extends IdentityUser<string> {
  unionId?: string | null | undefined = null;
  sessionKey?: string | null | undefined = null;
  /**最后登录时间*/
  lastLogin: Dayjs = dayjs();
  /**经度（最后登录）*/
  latitude?: number | null | undefined = null;
  /**纬度（最后登录）*/
  longitude?: number | null | undefined = null;
  /**详细地址（最后登录）*/
  streetAddress: string = "";
  /**头像*/
  images?: string | null | undefined = null;
  /**当前积分余额*/
  points: number = 0;
  /**当前返现*/
  cashback: number = 0;
  /**推广人数*/
  promotionCount: number = 0;
  /**用户角色关联表*/
  userRoles?: UserRole[] | null | undefined = [];
  /**用户角色*/
  roles?: Role[] | null | undefined = [];
  /**账号权限限制*/
  limitations: boolean = false;
  /**账号权限限制*/
  limitMessage?: string | null | undefined = null;
  /**软删除标记*/
  softDeleted: boolean = false;
  /**软删除者*/
  deletedBy?: string | null | undefined = null;
  /**软删除时间*/
  deleted?: Dayjs | null | undefined = null;
  /**创建时间*/
  created: Dayjs = dayjs();
  /**修改时间*/
  modified?: Dayjs | null | undefined = null;
  /**创建者*/
  createdBy?: string | null | undefined = null;
  /**修改者*/
  modifiedBy?: string | null | undefined = null;
}
