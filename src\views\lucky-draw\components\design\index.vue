<template>
  <div>
    <div @click="() => open = true"><slot><a>{{ readOnly ? '查看' : '编辑' }}抽奖页面</a></slot></div>
    <c-modal v-model:open="open" title="抽奖活动页面设计" :width="readOnly ? 520 : '800px'" @ok="save()">
      <div class="flex">
        <div v-if="!readOnly" class="b-r-(1px border-secondary solid)">
          <a-divider orientation="left">组件</a-divider>

          <div class="w-280px flex flex-wrap gap4">
            <div v-for="item in luckyDrawDesignComps" :key="item.compName" class="w-80px cursor-pointer b-(1px border-secondary solid) rounded-2 px4 py2 text-center c-text c-bg" @click="addComp(item)">
              {{ Object.entries(DrawComp)!.find(([_, v]) => v === item.compName)![0] }}
            </div>
          </div>
          <!--
          <a-divider orientation="left">基本配置</a-divider>
          <div>
            <div>
              <c-input v-model:value="previewData.title" addon-before="标题" />
            </div>
            <div class="flex">
              <div class="mt2 flex items-center b-(1px border solid) rounded-2">
                <div class="mr2 b-r-(1px border-secondary solid) bg-bg-elevated p2 py1.5">标题颜色</div>
                <input v-model="previewData.titleColor" type="color" addon-before="标题颜色">
              </div>
              <div class="mt2 flex items-center b-(1px border solid) rounded-2">
                <div class="mr2 b-r-(1px border-secondary solid) bg-bg-elevated p2 py1.5">标题背景色</div>
                <input v-model="previewData.titleBgColor" type="color" addon-before="标题背景色">
              </div>
            </div>
            <div class="mt2 flex items-center b-(1px border solid) rounded-2">
              <div class="mr2 b-r-(1px border-secondary solid) bg-bg-elevated p2 py1.5">页面背景色</div>
              <input v-model="previewData.bgColor" type="color" addon-before="页面背景色">
            </div>
          </div> -->

          <a-divider />
          <template v-if="currentItem">
            <div class="mb-4 flex items-center justify-between pr4">
              <h1 class="text-xl font-bold">配置</h1>
              <a-button type="primary" danger @click="removeComp()">删除</a-button>
            </div>
            <div class="mr4 box-border rounded-2 bg-bg-layout p4">
              <div v-if="currentItem?.type === '图片' ">
                <a-button type="dashed" block class="size-25" @click="uploadImage">
                  <template #icon>
                    <c-icon-plus-outlined />
                  </template>
                  上传图片
                </a-button>
              </div>

              <div v-if="currentItem.type === '占位符'">
                <div class="mt2 flex items-center b-(1px border solid) rounded-2">
                  <div class="mr2 b-r-(1px border-secondary solid) bg-bg-elevated p2 py1.5">背景色</div>
                  <input v-model="currentItem.attrs!.backgroundColor" type="color" addon-before="标题颜色">
                </div>
              </div>

              <div v-if="currentItem.type === '标题'">
                <a-divider class="text-18px font-bold">
                  文本设置
                </a-divider>
                <div class="flex flex-wrap gap-1">
                  <div class="relative bg-primary p4">
                    <c-icon-unordered-list-outlined class="absolute left-1 top-1 cursor-pointer c-#fff" title="根节点" @click="selectTitleNode()" />
                    <span
                      v-for="item, i in currentItem.attrs?.chid" :key="i" :class="{ '!c-#fff': titleIndex === i }"
                      class="mr2 cursor-pointer py1 hover:c-#fff" @click="selectTitleNode(item, i)"
                    >{{ item.text }}
                    </span>
                  </div>
                  <a-tag dashed class="cursor-pointer b-dashed bg-transparent" @click="addTitleNode">
                    <c-icon-plus-outlined />
                    新建文本节点
                  </a-tag>

                  <a-tag v-if="titleIndex !== -1" dashed color="red" class="cursor-pointer b-dashed bg-transparent" @click="removeTitleNode()">
                    <c-icon-close-outlined />
                    删除文本节点
                  </a-tag>
                  <span v-else class="text-12px text-warning">当前为根节点</span>
                </div>
                <div v-if="titleNode" class="mt1 flex flex-col gap2">
                  <div class="flex gap2">
                    <a-select
                      v-model:value="titleNode.type"
                      placeholder="填充类型"
                      class="!w-80px"
                    >
                      <a-select-option value="文本">文本</a-select-option>
                      <a-select-option value="变量">变量</a-select-option>
                    </a-select>
                    <a-select
                      v-if="titleNode.type === '变量'"
                      v-model:value="titleNode.text"
                      placeholder="请选择变量"
                      class="flex-1"
                    >
                      <a-select-option v-for="item, key in Variable" :key="item" :value="item">{{ key }}</a-select-option>
                    </a-select>
                    <a-input v-else v-model:value="titleNode.text" class="flex-1" placeholder="输入文本" />
                  </div>
                  <div class="flex flex-col gap2">
                    <a-select
                      v-model:value="titleNode.textAlign"
                      placeholder="请选择文字方向"
                      class="flex-1"
                      addon-before="文字方向"
                    >
                      <a-select-option value="left">居左</a-select-option>
                      <a-select-option value="center">居中</a-select-option>
                      <a-select-option value="right">居右</a-select-option>
                    </a-select>
                    <div class="flex items-center gap2">
                      <c-input-number v-model:value="titleNode.bold" addon-before="加粗" />
                      <input v-model="titleNode.color" addon-before="颜色" type="color">
                    </div>
                    <c-input-number v-model:value="titleNode.size" addon-before="大小" addon-after="px" />
                    <c-input-number v-model:value="titleNode.padding" addon-before="左右边距" addon-after="px" />
                  </div>
                </div>
              </div>

              <div v-if="currentItem.type !== '抽奖'">
                <a-divider class="text-18px font-bold">
                  绑定事件
                </a-divider>
                <a-select
                  v-model:value="currentItem.routeOption"
                  class="w-full"
                  placeholder="请选择绑定的事件"
                >
                  <a-select-option v-for="item, key in RouterUni" :key="item" :value="item">跳转到{{ key }}</a-select-option>
                </a-select>
              </div>

              <a-divider class="text-18px font-bold">
                位置信息
              </a-divider>
              <div class="flex flex-wrap gap2">
                <div class="flex items-center gap2">
                  <c-input v-model:value="currentItem.position.w" addon-before="宽度" />
                  <c-input v-model:value="currentItem.position.h" addon-before="高度" />
                </div>
                <c-input-number v-model:value="currentItem.position.x" addon-before="x" addon-after="px" />
                <c-input-number v-model:value="currentItem.position.y" addon-before="y" addon-after="px" />
                <c-input-number v-model:value="currentItem.position.z" addon-before="层级" />
              </div>
            </div>
          </template>
        </div>
        <div class="px-4">
          <div class="flex">
            <div class="mb-4 flex flex-col gap-4 md:flex-row">
              <div class="flex-1">
                <label for="device-select" class="mb-1 block text-sm font-medium">选择设备</label>
                <select
                  id="device-select"
                  v-model="selectedDevice"
                  class="w-full border border-gray-300 rounded-md bg-white px-3 py-2 shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option v-for="device in devicePresets" :key="device.name" :value="device">
                    {{ device.name }}
                  </option>
                </select>
              </div>
              <div class="flex-1">
                <label for="width-input" class="mb-1 block text-sm font-medium">宽度 (px)</label>
                <input
                  id="width-input"
                  v-model="customWidth"
                  type="number"
                  :disabled="selectedDevice.name !== 'Custom'"
                  class="w-full border border-gray-300 rounded-md bg-white px-3 py-2 shadow-sm disabled:bg-gray-100 disabled:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
              </div>
              <div class="flex-1">
                <label for="height-input" class="mb-1 block text-sm font-medium">高度 (px)</label>
                <input
                  id="height-input"
                  v-model="customHeight"
                  type="number"
                  :disabled="selectedDevice.name !== 'Custom'"
                  class="w-full border border-gray-300 rounded-md bg-white px-3 py-2 shadow-sm disabled:bg-gray-100 disabled:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
              </div>
            </div>
          </div>
          <div class="flex items-center justify-center border rounded-lg bg-bg-layout p-4">
            <div
              class="overflow-hidden border-8 border-gray-800 rounded-4 shadow-lg"
              :style="previewStyle"
            >
              <div class="relative h-full w-full flex flex-col bg-white" :style="{ backgroundColor: previewData.bgColor || '#E33656' }">
                <div class="head h10 w-full center py2" :style="{ color: previewData.titleColor || '#fff', backgroundColor: previewData.titleBgColor || '#E33656' }">
                  <div class="i-material-symbols-arrow-back-ios absolute left-4" />
                  <div class="title">{{ previewData.title || '抽奖' }}</div>
                </div>
                <div class="relative h-[fit-content] overflow-auto text-16px">
                  <preview v-model:current-id="currentId" v-model:preview-data="previewData" :read-only="readOnly" design :prizes-data="prizesData" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </c-modal>
  </div>
</template>

<script setup lang="ts">
import type { LotteryPrizes } from '@/api/models'
import type { ILuckyDrawDesignConfig, ImageAttrs, TitleAttrs } from './utils'
import { FileType } from '@/api/models'
import defaultImage from '@/assets/images/yxj.png'
import { Form } from 'ant-design-vue'
import { computed, ref } from 'vue'
import preview from './preview.vue'
import { DrawComp, luckyDrawDesignComps, RouterUni, Variable } from './utils'

defineProps<{ prizesData: LotteryPrizes[] | null | undefined, readOnly?: boolean }>()

const template = defineModel<string>('value')

const formItemContext = Form.useInjectFormItemContext()

const open = defineModel('open', { default: false })

const defaultConfig: ILuckyDrawDesignConfig = {
  title: '抽奖',
  bgColor: '#E33656',
  titleBgColor: '#E33656',
  titleColor: '#fff',
  comps: [
    { compName: DrawComp.图片, type: '图片', position: { x: 0, y: 0, z: 1 }, attrs: { src: defaultImage }, id: '1' },
    { compName: DrawComp.九宫格, type: '抽奖', position: { x: 57.890625, y: 356.484375, z: 1 }, id: '2' },
    { compName: DrawComp.标题, type: '标题', position: { x: 0, y: 733.28125, z: 1, w: '100%' }, attrs: { color: '#F9DF83', chid: [{ type: '文本', text: '您当前拥有' }, { type: '变量', text: '__COUNT__', size: 32, padding: 8, color: '#b02727' }, { type: '文本', text: '次抽奖资格' }], size: 18, bold: 500, textAlign: 'center', padding: 10 }, id: '3', routeOption: '/pages/luck-draw/count-records?id=__ID__' },
    { compName: DrawComp.占位符, type: '占位符', position: { x: 141.171875, y: 651.015625, z: 1, w: 100, h: 60 }, id: '4', routeOption: '/pages/luck-draw/lottery-records?id=__ID__', attrs: { backgroundColor: '' } },
  ],
}

const previewData = ref<ILuckyDrawDesignConfig>(deepCopy(defaultConfig))

watch(template, () => {
  previewData.value = template.value ? JSON.parse(template.value) : defaultConfig
}, { immediate: true })

const currentId = ref()

const currentItem = computed(() => {
  const temp = previewData.value.comps.find(v => v.id === currentId.value)

  if (temp?.type === '占位符' && !temp.attrs) {
    temp.attrs = { backgroundColor: '' }
  }

  return temp
})

const titleNode = ref<TitleAttrs>()

const titleIndex = ref(-1)

function selectTitleNode(item?: TitleAttrs, i?: number) {
  if (!item || i == null) {
    titleNode.value = currentItem.value?.attrs as TitleAttrs
    titleIndex.value = -1
  }
  else {
    titleNode.value = item
    titleIndex.value = i
  }
}

watch(currentId, () => {
  if (currentItem.value?.type === '标题') {
    selectTitleNode()
  }
})

function addTitleNode() {
  const attrs = currentItem.value?.attrs as TitleAttrs
  titleNode.value = {
    text: '这是文字',
    type: '文本',
  }
  attrs.chid?.push(titleNode.value)
}

function removeTitleNode() {
  const attrs = currentItem.value?.attrs as TitleAttrs

  attrs.chid = attrs.chid?.filter((_, i) => i !== titleIndex.value)
}

function uploadImage() {
  useFileMangerModal((files) => {
    const attrs = currentItem.value!.attrs as ImageAttrs
    attrs!.src = files[0]!.id!
  }, { multiple: false, immediateReturn: true, menu: [FileType.图片] })
}

function addComp(item: typeof luckyDrawDesignComps[0]) {
  if (!(item.limit && previewData.value.comps.filter(v => v.type === item.type).length === item.limit)) {
    const t = { ...deepCopy(item), id: Date.now().toString() } as any
    previewData.value.comps.push(t)
    currentId.value = t.id
  }
}

function removeComp() {
  previewData.value.comps = previewData.value.comps.filter(v => v.id !== currentId.value)
}

function save() {
  template.value = JSON.stringify(previewData.value)
  formItemContext.onFieldChange()
  open.value = false
}

// #region  设备变换

const devicePresets = [
  { name: 'iPhone 12 Pro', width: 390, height: 844 },
  { name: 'iPhone SE', width: 375, height: 667 },
  { name: 'Pixel 5', width: 393, height: 851 },
  { name: 'Samsung Galaxy S20', width: 360, height: 800 },
  { name: 'iPad Air', width: 820, height: 1180 },
  { name: 'Custom', width: 0, height: 0 },
]

const selectedDevice = ref(devicePresets[0]!)
const customWidth = ref(selectedDevice.value.width.toString())
const customHeight = ref(selectedDevice.value.height.toString())

const currentWidth = computed(() =>
  selectedDevice.value.name === 'Custom' ? Number.parseInt(customWidth.value) : selectedDevice.value.width,
)

const currentHeight = computed(() =>
  selectedDevice.value.name === 'Custom' ? Number.parseInt(customHeight.value) : selectedDevice.value.height,
)

const previewStyle = computed(() => ({
  width: `${currentWidth.value}px`,
  height: `${currentHeight.value}px`,
  maxWidth: '100%',
  maxHeight: '70vh',
  transform: currentHeight.value > currentWidth.value ? 'none' : 'rotate(90deg)',
}))

// 监听选择设备的变化
watch(selectedDevice, (newDevice) => {
  if (newDevice.name !== 'Custom') {
    customWidth.value = newDevice.width.toString()
    customHeight.value = newDevice.height.toString()
  }
})
// #endregion
</script>

<style scoped>

</style>
