<template>
  <div @click="onCkick" v-html="descriptionView" />
</template>

<script setup lang="ts">
const { text = '' } = defineProps<{
  text: string
}>()

const emit = defineEmits<{
  selectCode: [value: string]
}>()

const descriptionView = computed(() => {
  return replaceHashTags(text)
})

function replaceHashTags(input: string): string {
  const regex = /#(.*?)#/g // 匹配被##包裹的内容
  return input.replace(regex, (match, p1) => {
    return `<a title="校验编号" code="${p1}">${p1}</a>`
  })
}

function onCkick(e: any) {
  const data = e.target.getAttribute('code')
  if (data) {
    emit('selectCode', data)
  }
}
</script>

<style scoped>

</style>
