<!--
 * @Description:
 * @Author: 景 彡
 * @Date: 2024-10-30 10:35:28
 * @LastEditors: 景 彡
-->
<template>
  <div class="h-full flex gap-4">
    <!-- 左侧树形结构 -->
    <div class="w-1/3 rounded bg-white p-4 shadow">
      <div class="mb-4 flex items-center justify-between">
        <h3 class="text-lg font-medium">产品类型树</h3>
        <div class="flex gap-2">
          <a-button type="primary" size="small" @click="addModalVisible = true">
            <template #icon>
              <c-icon-plus-outlined />
            </template>
            添加
          </a-button>
          <a-button type="default" size="small" @click="refreshTree">
            <template #icon>
              <c-icon-reload-outlined />
            </template>
            刷新
          </a-button>
        </div>
      </div>

      <a-tree
        v-model:expanded-keys="expandedKeys"
        v-model:selected-keys="selectedKeys"
        :tree-data="treeData"
        :show-line="true"
        :show-icon="false"
        @select="onTreeSelect"
      >
        <template #title="{ title, dataRef }">
          <div class="group flex items-center justify-between">
            <span>{{ title }}</span>
            <div class="opacity-0 transition-opacity group-hover:opacity-100">
              <a-input-number
                v-if="dataRef?.isLeaf"
                v-model:value="dataRef.sortOrder"
                :min="0"
                :precision="0"
                size="small"
                style="width: 60px"
                @blur="updateSortOrder(dataRef)"
                @click.stop
              />
              <a-input-number
                v-else-if="dataRef?.level === 'brand'"
                v-model:value="dataRef.brandSortOrder"
                :min="0"
                :precision="0"
                size="small"
                style="width: 60px"
                placeholder="品牌排序"
                @blur="updateBrandSort(dataRef)"
                @click.stop
              />
              <a-input-number
                v-else-if="dataRef?.level === 'mainType'"
                v-model:value="dataRef.mainTypeSortOrder"
                :min="0"
                :precision="0"
                size="small"
                style="width: 60px"
                placeholder="分类排序"
                @blur="updateMainTypeSort(dataRef)"
                @click.stop
              />
            </div>
          </div>
        </template>
      </a-tree>
    </div>

    <!-- 右侧详情面板 -->
    <div class="flex-1 rounded bg-white p-4 shadow">
      <div v-if="selectedNode" class="space-y-4">
        <div class="border-b pb-4">
          <h3 class="mb-2 text-lg font-medium">{{ selectedNode.isLeaf ? '类型详情' : '分类信息' }}</h3>
          <div class="text-sm text-gray-500">
            {{ selectedNode.path }}
          </div>
        </div>

        <div v-if="selectedNode.isLeaf" class="space-y-4">
          <!-- 编辑表单 -->
          <a-form :model="editForm" layout="vertical">
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="品牌">
                  <a-input v-model:value="editForm.brand" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="一级分类">
                  <a-input v-model:value="editForm.mainType" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="类型名称">
                  <a-input v-model:value="editForm.name" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="排序">
                  <a-input-number v-model:value="editForm.sortOrder" :min="0" style="width: 100%" />
                </a-form-item>
              </a-col>
              <a-col :span="24">
                <a-form-item label="描述">
                  <a-textarea v-model:value="editForm.description" :rows="3" />
                </a-form-item>
              </a-col>
            </a-row>

            <div class="flex gap-2">
              <a-button type="primary" @click="saveEdit">保存</a-button>
              <a-button @click="cancelEdit">取消</a-button>
              <a-button type="primary" danger @click="deleteItem">删除</a-button>
            </div>
          </a-form>
        </div>

        <div v-else class="text-center text-gray-500">
          <p>选择一个具体的产品类型来查看和编辑详情</p>
        </div>
      </div>

      <div v-else class="mt-20 text-center text-gray-500">
        <p>请从左侧树中选择一个节点</p>
      </div>
    </div>
  </div>

  <!-- 添加新类型的模态框 -->
  <a-modal v-model:open="addModalVisible" title="添加产品类型" @ok="addNewType">
    <a-form :model="newTypeForm" layout="vertical">
      <a-form-item label="品牌" required>
        <a-input v-model:value="newTypeForm.brand" placeholder="请输入品牌" />
      </a-form-item>
      <a-form-item label="一级分类">
        <a-input v-model:value="newTypeForm.mainType" placeholder="请输入一级分类" />
      </a-form-item>
      <a-form-item label="类型名称" required>
        <a-input v-model:value="newTypeForm.name" placeholder="请输入类型名称" />
      </a-form-item>
      <a-form-item label="描述" required>
        <a-textarea v-model:value="newTypeForm.description" :rows="3" placeholder="请输入描述" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script lang="ts" setup>
import * as api from '@/api/'
import { ProductType } from '@/api/models'
import { message } from 'ant-design-vue'

definePage({
  meta: {
    title: '分类管理',
  },
})

// 树形数据相关
const treeData = ref<any[]>([])
const expandedKeys = ref<string[]>([])
const selectedKeys = ref<string[]>([])
const selectedNode = ref<any>(null)

// 编辑表单
const editForm = ref<ProductType>(new ProductType())

// 添加模态框
const addModalVisible = ref(false)
const newTypeForm = ref<ProductType>(new ProductType())

// 原始数据
const rawData = ref<ProductType[]>([])

// 获取数据并构建树
async function loadData() {
  try {
    const data = await api.Products.QueryType_PostAsync({
      get: { offset: 0, limit: -1, notCounted: true },
      sort: [{ propertyName: 'sortOrder', type: 0 }], // 按sortOrder升序排序
    })

    rawData.value = data.items || []
    buildTree()
  }
  catch (error) {
    message.error('加载数据失败')
    console.error('加载数据失败:', error)
  }
}

// 构建树形结构
function buildTree() {
  const tree: any[] = []
  const brandMap = new Map<string, any>()

  rawData.value.forEach((item) => {
    const brand = item.brand || '未分类品牌'
    const mainType = item.mainType || '未分类'
    const name = item.name || '未命名'

    // 创建或获取品牌节点
    if (!brandMap.has(brand)) {
      const brandNode = {
        title: brand,
        key: `brand-${brand}`,
        children: new Map<string, any>(),
        isLeaf: false,
        path: brand,
      }
      brandMap.set(brand, brandNode)
      tree.push(brandNode)
    }

    const brandNode = brandMap.get(brand)!

    // 创建或获取一级分类节点
    if (!brandNode.children.has(mainType)) {
      const mainTypeNode = {
        title: mainType,
        key: `mainType-${brand}-${mainType}`,
        children: [],
        isLeaf: false,
        path: `${brand} > ${mainType}`,
      }
      brandNode.children.set(mainType, mainTypeNode)
    }

    const mainTypeNode = brandNode.children.get(mainType)!

    // 添加具体类型节点
    mainTypeNode.children.push({
      title: name,
      key: `type-${item.id}`,
      isLeaf: true,
      sortOrder: item.sortOrder,
      data: item,
      path: `${brand} > ${mainType} > ${name}`,
    })
  })

  // 转换Map为数组
  tree.forEach((brandNode) => {
    brandNode.children = Array.from(brandNode.children.values())
  })

  treeData.value = tree

  // 默认展开所有节点
  const allKeys: string[] = []
  const collectKeys = (nodes: any[]) => {
    nodes.forEach((node) => {
      allKeys.push(node.key)
      if (node.children && node.children.length > 0) {
        collectKeys(node.children)
      }
    })
  }
  collectKeys(tree)
  expandedKeys.value = allKeys
}

// 树节点选择
function onTreeSelect(keys: (string | number)[], info: any) {
  if (keys.length > 0) {
    selectedNode.value = info.node.dataRef
    if (selectedNode.value?.isLeaf && selectedNode.value?.data) {
      editForm.value = { ...selectedNode.value.data }
    }
  }
  else {
    selectedNode.value = null
  }
}

// 刷新树
function refreshTree() {
  loadData()
}

// 更新排序
async function updateSortOrder(nodeData: any) {
  if (!nodeData?.data)
    return

  try {
    const record = nodeData.data
    const newSortOrder = Math.max(0, nodeData.sortOrder || 0)

    record.sortOrder = newSortOrder

    await api.Products.UpdateType_PostAsync(record)
    message.success('排序更新成功')

    loadData()
  }
  catch (error) {
    message.error('排序更新失败')
    console.error('排序更新失败:', error)
    loadData() // 重新加载数据
  }
}

// 保存编辑
async function saveEdit() {
  try {
    await api.Products.UpdateType_PostAsync(editForm.value)
    message.success('保存成功')
    loadData() // 重新加载数据
  }
  catch (error) {
    message.error('保存失败')
    console.error('保存失败:', error)
  }
}

// 取消编辑
function cancelEdit() {
  if (selectedNode.value?.data) {
    editForm.value = { ...selectedNode.value.data }
  }
}

// 删除项目
async function deleteItem() {
  if (!selectedNode.value?.data)
    return

  try {
    await api.Products.RemoveType_PostAsync([selectedNode.value.data.id])
    message.success('删除成功')
    selectedNode.value = null
    selectedKeys.value = []
    loadData()
  }
  catch (error) {
    message.error('删除失败')
    console.error('删除失败:', error)
  }
}

// 添加新类型
async function addNewType() {
  try {
    await api.Products.AddType_PostAsync(newTypeForm.value)
    message.success('添加成功')
    addModalVisible.value = false
    newTypeForm.value = new ProductType()
    loadData()
  }
  catch (error) {
    message.error('添加失败')
    console.error('添加失败:', error)
  }
}

// 初始化
onMounted(() => {
  loadData()
})
</script>
