<!--
 * @Description:
 * @Author: 景 彡
 * @Date: 2024-10-30 10:35:28
 * @LastEditors: 景 彡
-->
<template>
  <c-pro-table
    ref="tableRef"
    row-key="id"
    v-bind="queryBind"
    align="center"
    :show-del-btn="false"
    :operation-config="{ fixed: 'right', width: 180 }" show-add-btn immediate is-row-edit double-click-edit operation @save-edit-record="save" @del-record="del"
    @add-row="addRow"
    @cancel-edit="cancelEdit"
  />
</template>

<script lang="ts" setup>
import type { ColumnProps, Scope } from 'ch2-components/types/pro-table/types'
import * as api from '@/api/'
import { ProductType, QueryType } from '@/api/models'
import { message } from 'ant-design-vue'

definePage({
  meta: {
    title: '分类管理',
  },
})

const columns = reactive<ColumnProps<ProductType>[]>([
  { dataIndex: 'brand', title: '品牌', align: 'left', search: { el: 'input' }, queryType: QueryType.Include },
  { dataIndex: 'mainType', title: '一级分类', align: 'left', search: { el: 'input' }, queryType: QueryType.Include },
  { dataIndex: 'name', title: '类型名称', align: 'left', search: { el: 'input' }, queryType: QueryType.Include },
  { dataIndex: 'description', title: '描述', align: 'left', search: { el: 'input' }, queryType: QueryType.Include },
  { dataIndex: 'created', title: '创建时间', width: 180, dateFormat: true, edit: false, sorter: true },
  { dataIndex: 'createdBy', title: '创建者', edit: false },
])

const { tableRef, queryBind } = useTableSearch('tableRef', api.Products.QueryType_PostAsync, columns)

async function del(data: Scope<ProductType>) {
  const res = await api.Products.RemoveType_PostAsync([data.record.id as any])
  if (res) {
    message.success('删除成功')
    tableRef.value?.refresh()
  }
  else {
    message.warn('删除失败')
  }
}

function addRow(datas: ProductType[], editRecord: any) {
  if (!datas.some(v => !v.id || v.id === Guid.empty)) {
    datas.unshift(new ProductType())
    editRecord({ record: datas[0], index: 0 })
  }
}

function cancelEdit(datas: ProductType[]) {
  const i = datas.findIndex(v => !v.id || v.id === Guid.empty)
  if (i > -1)
    datas.splice(i, 1)
}

async function save(data: ProductType) {
  if (Guid.isNotNull(data.id)) {
    await api.Products.UpdateType_PostAsync(data)
  }
  else {
    await api.Products.AddType_PostAsync(data)
  }
  tableRef.value?.refresh()
  message.success('保存成功')
}
</script>
