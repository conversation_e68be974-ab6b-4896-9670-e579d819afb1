<template>
  <c-pro-table
    ref="proTableRef" v-model:search-form="searchForm" :columns="columns"
    :search-fields="searchFields"
    :api="api.ClientUserManage.GetUserListAsync" row-key="id" immediate serial-number operation
    align="center" :scroll="{ x: 600 }"
  >
    <template #header>
      <a-radio-group v-model:value="searchForm.clientUserState" @change="radioChange">
        <a-radio-button :value="null">全部</a-radio-button>
        <a-radio-button
          v-for="status in enumToObject(ClientUserState)"
          :key="status.value"
          :value="status.value"
        >
          {{ status.label }}
        </a-radio-button>
      </a-radio-group>
    </template>
    <template #toolButton>
      <a-button type="primary">漆工导出</a-button>
      <a-button danger @click="stopUsing(null)">批量停用</a-button>
    </template>
    <template #bodyCell="{ column, record }">
      <template v-if="column.dataIndex === 'userName'">
        <a @click="showInfo(record)">{{ record.userName }}</a>
      </template>
      <template v-if="column.dataIndex === 'add'">
        <span>{{ record.add?.addressLine2 }}</span>
      </template>
      <template v-if="column.dataIndex === 'state'">
        <a-tag v-if="record.state === ClientUserState.正常" color="success"> {{ ClientUserState[record.state] }} </a-tag>
        <a-tag v-else-if="record.state === ClientUserState.已停用" color="error"> {{ ClientUserState[record.state] }} </a-tag>
        <a-tag v-else color="warning"> {{ ClientUserState[record.state] }} </a-tag>
      </template>
    </template>
    <template #operation="{ record }">
      <!-- <a-button type="link"> 查看 </a-button> -->
      <a-popover v-if="record.state === ClientUserState.正常" title="输入停用理由" placement="topRight" trigger="click">
        <template #content>
          <div class="flex items-center">
            <a-input v-model:value="stopMessage" style="width: 280px" placeholder="请输入停用理由" />
            <a-button type="primary" class="ml-8px" @click="stopUsing(record)"> 确定停用 </a-button>
          </div>
        </template>
        <a-button type="link" danger> 停用 </a-button>
      </a-popover>
      <!-- <a-button v-if="record.state === ClientUserState.正常" type="link" danger @click="stopUsing(record)"> 停用 </a-button> -->
      <a-button v-else-if="record.state === ClientUserState.已停用" type="link" @click="enable(record)"> 启用 </a-button>
      <a-dropdown>
        <a class="ant-dropdown-link" @click.prevent>
          更多<c-icon-down-outlined />
        </a>
        <template #overlay>
          <a-menu>
            <a-menu-item>
              <a href="javascript:;" @click="modifyPoints(record)">修改积分</a>
            </a-menu-item>
            <a-menu-item>
              <a href="javascript:;" @click="withdrawal(record)">红包提现</a>
            </a-menu-item>
            <!-- <a-menu-item>
              <a href="javascript:;" @click="modifyInfo(record)">修改信息</a>
            </a-menu-item> -->
            <!-- <a-menu-item v-if="record.state === ClientUserState.正常">
              <a style="color: red" href="javascript:;" @click="stopUsing(record)">停用</a>
            </a-menu-item> -->
            <a-menu-item>
              <a href="javascript:;" @click="changePassword(record)">修改密码</a>
            </a-menu-item>
          </a-menu>
        </template>
      </a-dropdown>
    </template>
  </c-pro-table>

  <a-modal v-model:open="editPointOpen" title="修改积分" :mask-closable="false" @ok="editPointsSave">
    <a-form
      ref="formRef"
      :model="editPointForm"
      :label-col="{ span: 8 }"
      :wrapper-col="{ span: 16 }"
      autocomplete="off"
    >
      <a-form-item
        label="积分"
        name="number"
        :rules="[{ required: true, message: '请输入积分!' }]"
      >
        <a-input-number v-model:value="editPointForm.number" :min="0" style="width: 100%" />
      </a-form-item>
      <a-form-item
        label="是否增加积分"
        name="opType"
        :rules="[{ required: true, message: '请选择!' }]"
      >
        <c-boolean-select v-model:value="editPointForm.opType" style="width: 100%" allow-clear />
        <div class="c-error">注意：【是】- 增加积分，【否】- 减少积分</div>
      </a-form-item>
      <a-form-item
        label="描述"
        name="description"
      >
        <a-textarea
          v-model:value="editPointForm.description"
          placeholder="请输入描述"
          :auto-size="{ minRows: 2, maxRows: 5 }"
        />
      </a-form-item>
    </a-form>
  </a-modal>

  <a-modal v-model:open="editPsdOpen" title="修改密码" :footer="null">
    <div class="flex py-16px">
      <div class="w-80px">密码：</div>
      <a-input-group compact>
        <a-input v-model:value="passwardForm.password!" style="width: calc(100% - 200px)" />
        <a-button type="primary" @click="savePassward">提交</a-button>
      </a-input-group>
    </div>
  </a-modal>

  <a-drawer
    v-model:open="infoOpen"
    title="漆工的详细信息"
    width="600px"
    :mask-closable="false"
    placement="right"
    destroy-on-close
  >
    <a-descriptions v-if="infoOpen" bordered :column="1">
      <a-descriptions-item label="用户名">{{ info.userName }}</a-descriptions-item>
      <a-descriptions-item label="昵称">{{ info.name }}</a-descriptions-item>
      <a-descriptions-item label="手机号码">{{ info.phoneNumber }}</a-descriptions-item>
      <a-descriptions-item label="手机号是否验证">{{ info.phoneNumberConfirmed ? '是' : '否' }}</a-descriptions-item>
      <a-descriptions-item label="最近登录时间">{{ dateFormat(info.lastLogin) }}</a-descriptions-item>
      <a-descriptions-item label="账号状态">{{ ClientUserState[info.state] }}</a-descriptions-item>
      <a-descriptions-item label="收货信息">
        <p>收件人姓名：{{ info.add?.name }}</p>
        <p>收件人电话：{{ info.add?.phoneNumber }}</p>
        <p>所在地区：{{ info.add?.addressLine1 }}</p>
        <p>详细地址：{{ info.add?.addressLine2 }}</p>
      </a-descriptions-item>
    </a-descriptions>
  </a-drawer>

  <Withdrawal v-model:open="withdrawalOpen" :user-id="editPointForm.userId" />
</template>

<script lang="ts" setup>
// import type { ClientUserViewModel } from '@/api/models'
import type { _SearchField } from 'ch2-components/lib/pro-table/types'
import type {
  ColumnProps,
} from 'ch2-components/types/pro-table/types'
import * as api from '@/.generated/apis'
import { ClientUserViewModel, UserPasswordChangeEditModel } from '@/.generated/models'
import { ClientUserState } from '@/api/models'
import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import modal from 'ant-design-vue/es/modal'
import Withdrawal from './components/Withdrawal.vue'

definePage({
  meta: {
    title: '漆工管理',
    layoutRoute: {
      meta: {
        layout: 'admin',
        title: '漆工管理',
        local: true,
        icon: 'UsergroupAddOutlined',
        order: 5,
      },
    },
  },
})

const stopMessage = ref('')

const formRef = ref()

const proTableRef = useTemplateRef('proTableRef')

const searchForm = ref({
  userName: '',
  phoneNumber: '',
  clientUserState: null,
})

const searchFields = ref<_SearchField[]>([
  {
    prop: 'clientUserState',
    method: 'GET',
  },
])

const columns = reactive<ColumnProps<ClientUserViewModel>[]>([
  {
    dataIndex: 'userName',
    title: '用户名',
    key: 'userName',
    edit: false,
    search: { el: 'input', method: 'GET', attrs: {} },
  },
  {
    dataIndex: 'name',
    title: '昵称',
    key: 'name',
    edit: false,
  },
  {
    dataIndex: 'phoneNumber',
    title: '手机号码',
    key: 'phoneNumber',
    edit: false,
    search: { el: 'input', method: 'GET', attrs: {} },
  },
  {
    dataIndex: 'points',
    title: '积分',
    key: 'points',
    edit: false,
  },
  {
    dataIndex: 'add',
    title: '收货地址',
    key: 'add',
  },
  {
    dataIndex: 'lastLogin',
    title: '最近登录时间',
    key: 'lastLogin',
    edit: false,
    dateFormat: true,
  },
  {
    dataIndex: 'streetAddress',
    title: '最近定位',
    key: 'streetAddress',
    truncatedText: true,
    edit: false,
  },
  {
    dataIndex: 'state',
    title: '账号状态',
    key: 'state',
  },
])

function stopUsing(record: ClientUserViewModel) {
  modal.confirm({
    title: `确定停用【${record.name}-${record.phoneNumber}】漆工吗？`,
    icon: h(ExclamationCircleOutlined),
    content: '停用后漆工无法扫码获取奖励，请谨慎操作！',
    okText: '确定',
    okType: 'danger',
    cancelText: '取消',
    async onOk() {
      await api.ClientUserManage.LimitUser_PostAsync({ userId: record.id, message: stopMessage.value })
      proTableRef.value?.refresh()
      message.success('停用成功')
      stopMessage.value = ''
    },
    onCancel() {
      console.log('Cancel')
    },
  })
}

async function enable(record: ClientUserViewModel) {
  await api.ClientUserManage.UnLimitUser_PostAsync({ userId: record.id })
  proTableRef.value?.refresh()
  message.success('启用成功')
}

const editPointForm = ref({
  userId: '',
  number: 0,
  opType: false,
  description: '',
})

const editPointOpen = ref(false)

async function modifyPoints(record: ClientUserViewModel) {
  editPointForm.value.userId = record.id
  editPointOpen.value = true
  // await api.ClientUserManage.EditPoints_GetAsync()
}

const withdrawalOpen = ref(false)

/**
 * 红包提现
 * @param record
 */
function withdrawal(record: ClientUserViewModel) {
  editPointForm.value.userId = record.id
  withdrawalOpen.value = true
}

async function editPointsSave() {
  const res = await api.ClientUserManage.EditPoints_GetAsync(editPointForm.value)
  if (res.success) {
    editPointOpen.value = false
    message.success('操作成功')
  }
  else {
    message.error(res.message || '操作失败')
  }
}

const infoOpen = ref(false)

const info = ref(new ClientUserViewModel())

async function showInfo(record: ClientUserViewModel) {
  info.value = await api.ClientUserManage.GetUserViewModelAsync({ userId: record.id })
  console.log('%c [ info.value ]-296', 'font-size:13px; background:pink; color:#bf2c9f;', info.value)
  infoOpen.value = true
}

function radioChange() {
  nextTick(() => {
    proTableRef.value?.search()
  })
}

const editPsdOpen = ref(false)

const passwardForm = ref(new UserPasswordChangeEditModel())

function changePassword(record: ClientUserViewModel) {
  passwardForm.value.id = record.id
  editPsdOpen.value = true
}

async function savePassward() {
  if (!passwardForm.value.password)
    return message.error('请输入密码')
  await api.ClientUserManage.EditUserPassword_PostAsync(passwardForm.value)
  message.success('操作成功')
  editPsdOpen.value = false
}
</script>
