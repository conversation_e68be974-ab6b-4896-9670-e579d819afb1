<template>
  <a-layout class="layout-box" :class="{ 'layout-dark': globalStore.themeConfig.isDark }">
    <a-layout-header class="vertical-layout-header" :class="{ 'vertical-layout-dark': theme === 'dark' }">
      <div v-if="width > 460" class="logo">
        {{ projectName }}
      </div>
      <Menu v-if="width > 460" :style="menuStyle" mode="horizontal" :theme="theme" />
      <RightHeader />
    </a-layout-header>
    <LayoutContainer class="layout-container" />
  </a-layout>
</template>

<script setup lang="ts">
import { useAppStore } from '@/stores'
import { useWindowSize } from '@vueuse/core'
import { computed } from 'vue'
import RightHeader from '../components/Header/RightHeader.vue'
import LayoutContainer from '../components/LayoutContainer.vue'
import Menu from '../components/Menu/index.vue'

const projectName = import.meta.env.VITE_APP_TITLE

const globalStore = useAppStore()

const theme = computed(() => (globalStore.themeConfig.darkMenu ? 'dark' : 'light'))

const { width } = useWindowSize()

const menuStyle = computed(() => {
  return { maxWidth: '700px', width: `${width.value - 260}px` }
})
</script>

<style scoped lang="less">
@mobile-breakpoint: 768px;

.layout-box {
  min-height: 100vh;

  .vertical-layout-header {
    background: @colorBgContainer;
    display: flex;
    gap: 16px;
  }

  &.layout-dark {
    .vertical-layout-header {
      background: #001529;
    }
  }

  @media screen and (max-width: @mobile-breakpoint) {
    .layout-container {
      width: 100% !important;
    }
  }

  .logo {
    padding-left: calc(@size * 1px);
  }

  .layout-container {
    padding-top: 60px;
    margin: auto;
    width: 80%;
  }

  #components-layout-demo-custom-trigger .trigger:hover {
    color: #1890ff;
  }

  #components-layout-demo-custom-trigger .logo {
    height: 32px;
    background: rgba(255, 255, 255, 0.3);
    margin: 16px;
  }

  .site-layout .site-layout-background {
    background: @colorBgLayout;
  }

  .ant-layout-footer {
    padding: 4px 60px;
    margin-top: 0px;
    margin-bottom: 10px;
  }

  .maximize {
    position: fixed;
    width: 100%;
    z-index: 1;
    backdrop-filter: blur(8px);
    border-bottom: 1px solid @colorBorderSecondary;

    .ant-menu-light {
      background: none;
    }

    .tool-bar-ri {
      position: relative;
      align-items: center;

      :deep(.anticon) {
        font-size: 18px;
        vertical-align: baseline;
      }
    }

    :deep(.ant-avatar) {
      width: 30px !important;
      height: 30px !important;
      line-height: 30px !important;
      font-size: 15px !important;
    }

    .ant-menu-horizontal {
      border: none !important;
    }
  }

  .vertical-layout-dark {
    background: #001529;

    .tool-bar-ri,
    .logo {
      color: rgba(255, 255, 255, 0.65);
    }
  }
}
</style>
