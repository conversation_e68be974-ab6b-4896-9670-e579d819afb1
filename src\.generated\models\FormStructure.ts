import { FormCompOptions } from "./FormCompOptions";
export class FormStructure {
  /**组件*/
  component?: string | null | undefined = null;
  field?: string | null | undefined = null;
  label?: string | null | undefined = null;
  /**默认值*/
  value?: string | null | undefined = null;
  required?: boolean | null | undefined = null;
  placeholder?: string | null | undefined = null;
  options?: FormCompOptions[] | null | undefined = [];
  verificationMessage?: string | null | undefined = null;
  regexe?: string | null | undefined = null;
}
