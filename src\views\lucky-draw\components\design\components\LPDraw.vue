<template>
  <div v-if=" data?.prizes" class="zp-box" :style="{ '--nums': data?.prizes.length }">
    <div ref="panziRef" class="panzi">
      <div class="bck-box" :style="`transform: rotate(${-90 + 180 / data.prizes.length}deg)`">
        <div
          v-for="(i, index) in data?.prizes"
          :key="index"
          class="bck"
          :style="`transform: rotate(${-index * 360 / data.prizes.length}deg) skew(${-90 + 360 / data.prizes.length}deg);`"
        />
      </div>
      <div
        v-for="(i, index) in data.prizes"
        :key="index"
        class="jiang"
        :style="`transform: rotate(${-index * 360 / data.prizes.length}deg) translateY(-6.3rem);`"
      >
        <span class="title">{{ i.name }}</span>
        <div class="img">
          <img :src="joinImageById(i.images!)">
        </div>
      </div>
    </div>
    <div class="start-btn" @click="start()">抽奖</div>
  </div>
</template>

<script lang="ts" setup>
import { message } from 'ant-design-vue'
import { computed, ref } from 'vue'
import { useInjectionLuckDrawData } from '../useDraw'

const { data, joinImageById, drawPrizeApi, luckDrawConfig, prizes: prizesInfo } = useInjectionLuckDrawData()

const winner = ref(2) // 指定获奖下标
const loading = ref(false) // 防止多次点击
const panziElement = useTemplateRef('panziRef')

// 动画类计算
const animationClass = computed(() => {
  return `wr${winner.value}`
})

// 开始抽奖
async function start() {
  if (!loading.value) {
    if (panziElement.value) {
      panziElement.value.classList.remove(animationClass.value)
    }
    const res = await drawPrizeApi()
    winner.value = data?.value.prizes?.findIndex(v => v.id === res.id) ?? -1
    if (winner.value === -1) {
      message.error('奖品获取错误')
    }
    winCallback()
    loading.value = true
  }
}

// 中奖回调
function winCallback() {
  setTimeout(() => {
    if (panziElement.value) {
      panziElement.value.classList.add(animationClass.value)
    }
  }, 0)

  // 3秒后显示结果
  setTimeout(() => {
    loading.value = false
    if (prizesInfo.value)
      luckDrawConfig?.onLuckDrawCallback(prizesInfo.value)
  }, 3000)
}
</script>

<style lang="less" scoped>
@btn_size: 4rem; // 抽奖按钮尺寸
@time: 3s; // 转动多少秒后停下的时间
@nums: var(--nums);
.zp-box {
  user-select: none;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  width: 100%;
  height: 100%;
  .start-btn {
    display: inline-block;
    background: #f53737;
    cursor: pointer;
    width: @btn_size;
    height: @btn_size;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    color: white;
    font-size: 24;
    font-weight: bold;
    z-index: 2;
    position: relative;

    &::before {
      content: '';
      width: 0;
      height: 0;
      border: 1rem solid transparent;
      border-top: 2rem solid transparent;
      border-bottom: 2rem solid #f53737;
      position: absolute;
      top: -3.2em;
      z-index: -1;
    }
  }

  .panzi {
    overflow: hidden;
    border-radius: 50%;
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    border: 10px solid #f74e4e;
    box-sizing: border-box;

    .jiang {
      position: absolute;

      .title {
        font-size: 12px;
        color: #a62a2a;
      }

      .img {
        margin: 0.2rem auto;
        width: 2.5rem;
        height: 2.5rem;
        line-height: 2.5rem;
        color: white;
        text-align: center;
        img {
          max-height: 100%;
          max-width: 100%;
        }
      }
    }
  }

  .bck-box {
    overflow: hidden;
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    border-radius: 50%;

    .bck {
      position: absolute;
      width: 100%;
      height: 100%;
      opacity: 1;
      top: -50%;
      left: 50%;
      transform-origin: 0% 100%;
    }

    .bck:nth-child(2n) {
      background: #fffbf4;
    }

    .bck:nth-child(2n + 1) {
      background: #ffeee2;
    }
  }

  .wr0,
  .wr1,
  .wr2,
  .wr3,
  .wr4,
  .wr5,
  .wr6,
  .wr7 {
    animation-duration: @time;
    animation-timing-function: ease;
    animation-fill-mode: both;
    animation-iteration-count: 1;
  }

  .wr0 {
    animation-name: play0;
  }
  .wr1 {
    animation-name: play1;
  }
  .wr2 {
    animation-name: play2;
  }
  .wr3 {
    animation-name: play3;
  }
  .wr4 {
    animation-name: play4;
  }
  .wr5 {
    animation-name: play5;
  }
  .wr6 {
    animation-name: play6;
  }
  .wr7 {
    animation-name: play7;
  }

  @keyframes play0 {
    to {
      transform: rotate(calc(5 * 360deg + 360deg / @nums * 0));
    }
  }
  @keyframes play1 {
    to {
      transform: rotate(calc(5 * 360deg + 360deg / @nums * 1));
    }
  }
  @keyframes play2 {
    to {
      transform: rotate(calc(5 * 360deg + 360deg / @nums * 2));
    }
  }
  @keyframes play3 {
    to {
      transform: rotate(calc(5 * 360deg + 360deg / @nums * 3));
    }
  }
  @keyframes play4 {
    to {
      transform: rotate(calc(5 * 360deg + 360deg / @nums * 4));
    }
  }
  @keyframes play5 {
    to {
      transform: rotate(calc(5 * 360deg + 360deg / @nums * 5));
    }
  }
  @keyframes play6 {
    to {
      transform: rotate(calc(5 * 360deg + 360deg / @nums * 6));
    }
  }
  @keyframes play7 {
    to {
      transform: rotate(calc(5 * 360deg + 360deg / @nums * 7));
    }
  }
}
</style>
