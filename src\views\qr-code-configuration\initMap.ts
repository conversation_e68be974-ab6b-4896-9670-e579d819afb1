import type { GeoLocation } from '@/api/models'
/*
 * @Description:
 * @Author: 景 彡
 * @Date: 2024-12-03 15:10:12
 * @LastEditors: 景 彡
 */
import { SystemBaseInfo } from '@/api'
import { message } from 'ant-design-vue'

export type AreaType = 'allowed' | 'prohibited'

const initMap = {
  scriptNode: null as HTMLScriptElement | null,
  mapLoaded: false,

  async install() {
    if (this.mapLoaded) {
      return Promise.resolve((window as any).TMap)
    }

    const AK = await SystemBaseInfo.GetTencentMapKeyAsync()

    const TMap_URL = `https://map.qq.com/api/gljs?v=1.exp&libraries=tools,service&key=${AK}&callback=onMapCallback`

    this.scriptNode = document.createElement('script')
    this.scriptNode.setAttribute('type', 'text/javascript')
    this.scriptNode.setAttribute('src', TMap_URL)

    // 使用 onload 确认脚本加载完成
    return new Promise((resolve, reject) => {
      this.scriptNode!.onload = () => {
        if (typeof (window as any).TMap !== 'undefined') {
          this.mapLoaded = true
          resolve((window as any).TMap)
        }
        else {
          // 如果 TMap 还未初始化，则等待 onMapCallback
          (window as any).onMapCallback = () => {
            this.mapLoaded = true
            resolve((window as any).TMap)
          }
        }
      }

      this.scriptNode!.onerror = () => {
        reject(new Error('Failed to load TMap script'))
      }

      document.body.appendChild(this.scriptNode!)
    })
  },

  destroy() {
    if (this.scriptNode) {
      this.scriptNode.remove()
      this.scriptNode = null
    }
    delete (window as any).onMapCallback
    this.mapLoaded = false
  },
}

export function useInitMap(useId: () => string, options?: {
  immediate?: boolean
  onSuggestionChange?: (data: ISuggestion, type: AreaType) => void
  onClick?: (data: Location) => void
}, mapOptions?: any) {
  const map = shallowRef()

  const mapId = `map-${useId()}`

  const mapSearchModel = reactive({
    provinceList: [] as District[],
    cityList: {} as Record<string, District[]>,
    results: [] as Array<any>,
    search: null as any,
    suggest: null as any,
    fold: false,
  })

  const infoWindow = shallowRef([] as any)

  let district: any

  const init = async () => {
    if (map.value) {
      removeMarker()
      return
    }
    await initMap.install()
    if (!TMap) {
      message.error('地图组件未能正常初始化')
      throw new Error('地图组件未能正常初始化')
    }
    await nextTick()
    console.log('%c [ mapId ]-98', 'font-size:13px; background:pink; color:#bf2c9f;', mapId)
    map.value = new TMap.Map(mapId, mapOptions || {
      zoom: 4, // 设置地图缩放级别
      viewMode: '2D',
      center: new TMap.LatLng(25.207313, 110.283999),

    })

    map.value.on('click', (evt: any) => {
      if (options?.onClick)
        options.onClick(evt.latLng)
    })

    district = new TMap.service.District({
      // 新建一个行政区划类
      polygon: 2, // 返回行政区划边界的类型
    })

    getDistrict(['广西壮族自治区', '桂林市'])
  }

  async function getDistrict([province, city]: [string, string]) {
    if (!mapSearchModel.provinceList.length) {
      await district.getChildren().then((result: { result: any[] }) => {
        mapSearchModel.provinceList = result.result[0]
      })
    }
    const provinceInfo = mapSearchModel.provinceList.find(v => v.fullname === province)
    if (!provinceInfo)
      return
    if (!(province in mapSearchModel.cityList)) {
      await district
        .getChildren({ id: provinceInfo.id })
        .then((result: any) => {
          mapSearchModel.cityList[provinceInfo.fullname] = result.result[0]
        })
    }
    const cityInfo = mapSearchModel.cityList[provinceInfo.fullname]?.find(v => v.fullname === city)
    if (cityInfo?.location) {
      map.value.setCenter(cityInfo.location)
    }
    else {
      map.value.setCenter(provinceInfo.location)
    }
    map.value.setZoom(8)
    mapSearchByRegion(city ?? province)
  }

  function mapSearchByRegion(region: string) {
    mapSearchModel.search = new TMap.service.Search({ pageSize: 10 }) // 新建一个地点搜索类
    mapSearchModel.suggest = new TMap.service.Suggestion({
      // 新建一个关键字输入提示类
      pageSize: 10, // 返回结果每页条目数
      region, // 限制城市范围
      regionFix: true, // 搜索无结果时是否固定在当前城市
    })
  }

  function mapSearchByKeyWord(e: any) {
    // 使用者在搜索框中输入文字时触发
    const keyword = e.target.value
    if (keyword && map.value) {
      mapSearchModel.suggest
        .getSuggestions({ keyword, location: map.value.getCenter() })
        .then((result: any) => {
          mapSearchModel.results = result.data as any
        })
        .catch((error: any) => {
          console.log(error)
        })
    }
  }

  const setSuggestion = useDebounceFn((item: ISuggestion) => {
    if (map.value) {
      infoWindow.value.forEach((infoWindow: { destroy: () => void }) => {
        infoWindow.destroy()
      })

      const _infoWindow = new TMap.InfoWindow({
        map: map.value,
        position: item.location,
        content:
        `<div style="width: 260px">
        <h3>${item?.title || ''}</h3><p>地址：${item.address || ''}</p>
        <p>坐标：${item.location.lat.toFixed(6)},${item.location.lng.toFixed(6)}</p>
        <div style="display: flex;width: 100%;justify-content: space-around;">
        <button class="yunxu" style="background-color: var(--ch2-color-primary); color: white; border: none; padding: 8px 12px; font-size: 14px; border-radius: 5px; cursor: pointer; transition: background-color 0.3s;">添加允许区域</button>
        <button class="jinzhi" style="background-color: var(--ch2-color-error); color: white; border: none; padding: 8px 12px; font-size: 14px; border-radius: 5px; cursor: pointer; transition: background-color 0.3s;">添加禁止区域</button>
        </div></div>`,
        offset: { x: 0, y: -10 },
      })

      const dom = _infoWindow.dom as HTMLDivElement
      const allowedButton = dom.querySelector('.yunxu') as HTMLButtonElement
      const prohibitedButton = dom.querySelector('.jinzhi') as HTMLButtonElement

      const fn = (e: MouseEvent, type: 'allowed' | 'prohibited') => {
        e.stopPropagation()
        e.preventDefault()
        if (options?.onSuggestionChange)
          options?.onSuggestionChange(item, type)
        _infoWindow.close()
      }

      allowedButton!.onclick = e => fn(e, 'allowed')
      prohibitedButton!.onclick = e => fn(e, 'prohibited')

      infoWindow.value.push(_infoWindow)
    };
  }, 100)

  const activeCircle = shallowRef<any>()

  const allowedArea1Circle = shallowRef<any>()
  const prohibitedAreaCircle = shallowRef<any>()

  function setActiveCircle(data: GeoLocation & { id: string }, type: AreaType) {
    const color = {
      allowed: {
        fillColor: 'rgba(41,91,255,0.16)',
        borderColor: 'rgba(41,91,255,1)',
      },
      prohibited: {
        fillColor: 'rgba(255, 2, 2,0.16)',
        borderColor: 'rgba(255, 2, 2,0.66)',
      },
    }

    if (activeCircle.value) {
      activeCircle.value.destroy()
    }

    activeCircle.value = new TMap.MultiCircle({
      map: map.value,
      styles: { // 设置圆形样式
        circle: new TMap.CircleStyle({
          color: color[type].fillColor,
          showBorder: true,
          borderColor: color[type].borderColor,
          borderWidth: 1,
        }),
      },
      geometries: [
        {
          id: data.id,
          styleId: 'circle',
          center: new TMap.LatLng(data.latitude, data.longitude),
          radius: data.radiusKm * 1000,
          properties: data,
        },
      ],
    })
  }

  function initMask(allowed: Array<GeoLocation & { id: string }>, prohibited: Array<GeoLocation & { id: string }>) {
    removeMarker()

    allowedArea1Circle.value = new TMap.MultiCircle({
      map: map.value,
      styles: { // 设置圆形样式
        circle: new TMap.CircleStyle({
          color: 'rgba(41,91,255,0.16)',
          showBorder: true,
          borderColor: 'rgba(41,91,255,1)',
          borderWidth: 1,
        }),
      },
      geometries: allowed.map(item => (
        {
          id: item.id,
          styleId: 'circle',
          center: new TMap.LatLng(item.latitude, item.longitude),
          radius: item.radiusKm * 1000,
          properties: item,
        })),
    })

    prohibitedAreaCircle.value = new TMap.MultiCircle({
      map: map.value,
      styles: { // 设置圆形样式
        circle: new TMap.CircleStyle({
          color: 'rgba(255, 2, 2 ,0.16)',
          showBorder: true,
          borderColor: 'rgba(255, 2, 2 ,0.66)',
          borderWidth: 1,

        }),
      },
      geometries: prohibited.map(item => (
        {
          id: item.id,
          styleId: 'circle',
          center: new TMap.LatLng(item.latitude, item.longitude),
          radius: item.radiusKm * 1000,
          properties: item,
        })),
    })
  }

  function addMask(data: GeoLocation & { id: string }, type: AreaType) {
    if (type === 'allowed') {
      allowedArea1Circle.value.add([
        {
          id: data.id,
          styleId: 'circle',
          center: new TMap.LatLng(data.latitude, data.longitude),
          radius: data.radiusKm * 1000,
          properties: data,
        },
      ])
    }
    else {
      prohibitedAreaCircle.value.add([
        {
          id: data.id,
          styleId: 'circle',
          center: new TMap.LatLng(data.latitude, data.longitude),
          radius: data.radiusKm * 1000,
          properties: data,
        },
      ])
    }
  }

  function updateMask(data: GeoLocation & { id: string }, type: AreaType) {
    const temp = {
      id: data.id,
      styleId: 'circle',
      center: new TMap.LatLng(data.latitude, data.longitude),
      radius: data.radiusKm * 1000,
      properties: data,
    }
    if (type === 'allowed') {
      allowedArea1Circle.value.updateGeometries([temp])
    }
    else {
      prohibitedAreaCircle.value.updateGeometries([temp])
    }
    activeCircle.value.updateGeometries([temp])
  }

  function removeMarker() {
    if (allowedArea1Circle.value) {
      allowedArea1Circle.value.destroy()
    }
    if (prohibitedAreaCircle.value) {
      prohibitedAreaCircle.value.destroy()
    }

    if (activeCircle.value) {
      activeCircle.value.destroy()
    }
  }

  function removeByid(id: string, type: AreaType) {
    if (activeCircle.value) {
      const data = activeCircle.value.getGeometryById(id)
      if (data)
        activeCircle.value.destroy()
    }
    if (type === 'allowed') {
      allowedArea1Circle.value.remove([id])
    }
    else {
      prohibitedAreaCircle.value.remove([id])
    }
  }

  onMounted(async () => {
    if (options?.immediate) {
      init()
    }
  })

  onUnmounted(() => {
    initMap.destroy()
  })

  return { removeByid, setActiveCircle, addMask, updateMask, initMask, mapId, map, init, mapSearchByRegion, mapSearchByKeyWord, mapSearchModel, setSuggestion, getDistrict }
}

export interface ISuggestion {
  id: string
  title?: string
  address?: string
  category?: string
  type?: number
  location: Location
  adcode?: number
  province?: string
  city?: string
  district?: string
  _distance?: number
}

interface Location {
  lat: number
  lng: number
  height?: number
}

export type DOMOverlay<T extends object> = {
  readonly map: any
  position: [number, number] // new TMap.LatLng(39.984104, 116.307503); [longitude,latitude]
} & T

interface District {
  id: string
  name: string
  fullname: string
  pinyin: string[]
  location: Location
  polygon: Location[][]
}

export function useDOMOverlay<T extends object>({
  createDOM,
  onInit,
  updateDOM,
  onDestroy,
}: {
  createDOM: (this: DOMOverlay<T>) => any
  onInit?: (this: DOMOverlay<T>, options: DOMOverlay<T>) => void
  updateDOM: (this: DOMOverlay<T> & { dom: any }) => any
  onDestroy?: (this: DOMOverlay<T> & { dom: any }) => any
}) {
  function Dom<T extends object>(this: DOMOverlay<T>, options: DOMOverlay<T>) {
    TMap.DOMOverlay.call(this, {
      ...options,
      position: new TMap.LatLng(Number(options.position[1]), Number(options.position[0])),
    })
  }

  Dom.prototype = new TMap.DOMOverlay()

  Dom.prototype.onDestroy = onDestroy
  Dom.prototype.updateDOM = updateDOM
  Dom.prototype.createDOM = createDOM
  if (!onInit) {
    Dom.prototype.onInit = function onIni(this: DOMOverlay<T>, options: DOMOverlay<T>) {
      Object.keys(options).forEach((k) => {
        (this as any)[k] = (options as any)[k]
      })
    }
  }
  else {
    Dom.prototype.onInit = onInit
  }
  return { Dom } as unknown as { Dom: new (options: DOMOverlay<T>) => any }
}

export function Mask() {
  return useDOMOverlay<{ data: any }>({
    createDOM() {
      const box = document.createElementNS('http://www.w3.org/2000/svg', 'svg')
      box.setAttribute('style', '--color:red')
      box.setAttribute('viewBox', '0 0 1024 1024')
      box.setAttribute('class', 'map-icon')
      box.setAttribute('width', '24')
      box.setAttribute('height', '24')
      const path = document.createElementNS('http://www.w3.org/2000/svg', 'path')
      path.setAttribute(
        'd',
        'M895.616384 347.812188q0 10.22977-0.511489 19.436563t-1.534466 19.436563q-9.206793 84.907093-37.338661 163.164835t-71.096903 150.377622-99.228771 138.613387-121.734266 127.872128q-9.206793 11.252747-23.528472 11.252747-15.344655 0-24.551449-11.252747-65.470529-61.378621-122.245754-128.895105t-100.251748-141.170829-71.608392-152.935065-36.315684-165.210789q0-8.183816-0.511489-15.344655t-0.511489-15.344655q0-71.608392 28.131868-135.032967t76.211788-110.481518 113.038961-74.677323 138.613387-27.62038 138.101898 27.62038 112.527473 74.677323 76.211788 110.481518 28.131868 135.032967zM540.643357 507.396603q33.758242 0 63.424575-12.787213t51.66034-34.26973 34.781219-50.637363 12.787213-61.89011-12.787213-61.89011-34.781219-50.637363-51.66034-34.26973-63.424575-12.787213-63.424575 12.787213-52.171828 34.26973-35.292707 50.637363-12.787213 61.89011 12.787213 61.89011 35.292707 50.637363 52.171828 34.26973 63.424575 12.787213z',
      )
      path.setAttribute('p-id', '2440')
      box.append(path)
      return box
    },
    updateDOM() {
      if (!this.map) {
        return
      }
      // 经纬度坐标转容器像素坐标
      const pixel = this.map.projectToContainer(this.position)
      // 使饼图中心点对齐经纬度坐标点
      const left = `${pixel.getX() - this.dom.clientWidth / 2}px`
      const top = `${pixel.getY() - this.dom.clientHeight / 2}px`
      this.dom.style.transform = `translate(${left}, ${top})`
    },
  })
}
