import { GeoLocation } from "./GeoLocation";
import { AddressComponentRes } from "./AddressComponentRes";
/**防盗刷*/
export class ScanProtection {
  /**产品批次有效期（小时）*/
  batchValidityHours?: number | null | undefined = null;
  /**每日扫码次数限制*/
  dailyScanLimit?: number | null | undefined = null;
  /**每日扫码地点移动范围（公里）*/
  maxDailyScanDistanceKm?: number | null | undefined = null;
  /**是否强制获取用户设备ID*/
  requireDeviceId?: boolean | null | undefined = null;
  /**允许扫码的地理范围：为空允许所有地方*/
  allowedArea?: GeoLocation[] | null | undefined = [];
  /**禁止扫码的地理范围：为空允许所有地方*/
  prohibitedArea?: GeoLocation[] | null | undefined = [];
  /**允许扫码的国内或特定区域：为空允许所有地方*/
  allowedRegions?: AddressComponentRes[] | null | undefined = [];
  /**禁用扫码的特定区域扫码：为空允许所有地方*/
  prohibitedRegions?: AddressComponentRes[] | null | undefined = [];
  /**邀请用户设备ID重复封禁*/
  blockInviteOnDuplicateDeviceId?: boolean | null | undefined = null;
}
