<!--
 * @Description:
 * @Author: luckymiaow
 * @Date: 2023-05-15 14:50:21
 * @LastEditors: 景 彡
-->
<!-- 通知消息 -->
<template>
  <a-popover placement="bottom">
    <template #content>
      <div class="cursor-pointer hover:c-primary" @click="toLink()">
        <span>新订单数量：</span>
        <b class="c-primary">{{ newOrder }}</b>
      </div>

      <div class="mt4 cursor-pointer hover:c-primary" @click="toLink()">
        <span>未发货订单：</span>
        <b class="c-red-6">{{ notShippedYetCount }}</b>
      </div>

      <div class="mt4 cursor-pointer hover:c-primary" @click="router.push('/article-management/form-design')">
        <span>未处理的报名：</span>
        <b class="c-red-6">{{ unFormCount }}</b>
      </div>

      <div v-if="isBalanceWarning" class="mt4 cursor-pointer hover:c-primary" @click="router.push('/system-manage/amount-management/')">
        <span>红包余额预警，请及时充值：</span>
        <b class="c-red-6">{{ balanceWarningInfo / 100 }} 元</b>
      </div>
      <!-- <a-tabs v-model:active-key="activeKey" class="w[270px]">
        <a-tab-pane key="1">
          <template #tab>
            通知
            <a-badge :count="noticeCount">
              <SoundOutlined />
            </a-badge>
          </template>
          <MsgTab />
        </a-tab-pane>
      </a-tabs> -->
    </template>
    <template #icon />
    <template #cancelButton />
    <template #okButton />
    <a-badge :count="allCount" c-inherit>
      <BellOutlined class="text-1.6em" />
    </a-badge>
  </a-popover>
</template>

<script setup lang="ts">
import * as api from '@/.generated/apis'
import { routerMenu } from '@/router'
import { BellOutlined } from '@ant-design/icons-vue'
import { notification } from 'ant-design-vue'
import { ref } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

const newOrder = ref<number>(0)

const unFormCount = ref(0)

const notShippedYetCount = ref<number>(0)

const isBalanceWarning = ref(false)

const allCount = computed(() => {
  let res = newOrder.value + notShippedYetCount.value
  if (isBalanceWarning.value) {
    res += 1
  }
  return res
})

async function toLink() {
  await api.PointsOrders.ReadOrderNumber_GetAsync()
  router.push('/order-management/')
}

const orderCountRoute = computed(() => {
  return routerMenu.value.find(v => v.path === '/order-management/')
})

const unFormCountRoute = computed(() => {
  const articleManagement = routerMenu.value.find(item => item.path === '/article-management')
  if (articleManagement) {
    return articleManagement.children?.find(child => child.path === '/article-management/form-design')
  }

  return null
})

async function getCount() {
  newOrder.value = await api.PointsOrders.GetNewOrderNumberAsync()
}

async function getNotShippedYetCount() {
  notShippedYetCount.value = await api.PointsOrders.GetPointsOrderNumberAsync()
  unFormCount.value = await api.FormDesigns.GetUnFormDataStateAsync()
  if (orderCountRoute.value?.meta) {
    orderCountRoute.value.meta.badge = notShippedYetCount.value
  }

  if (unFormCountRoute.value?.meta) {
    unFormCountRoute.value.meta.unFormCount = unFormCount.value
  }
}

const balanceWarningInfo = ref<number>(0)

const balanceWarningInfoOpen = ref(false)

async function balanceWarning() {
  isBalanceWarning.value = await api.SystemBaseInfo.BalanceWarning_GetAsync()
  if (isBalanceWarning.value) {
    balanceWarningInfo.value = await api.SystemBaseInfo.GetRemainingBalanceAsync()
    if (!balanceWarningInfoOpen.value) {
      notification.warning({
        message: '红包余额不足',
        description: `当前余额为${balanceWarningInfo.value / 100}元，为保障系统的正常运行，请及时充值`,
      })
      balanceWarningInfoOpen.value = true
    }
  }
}

const getDataInterval = setInterval(() => {
  getNotShippedYetCount()
  getCount()
  balanceWarning()
}, 60000)

onUnmounted(() => {
  clearInterval(getDataInterval)
})

onMounted(() => {
  getCount()
  balanceWarning()
  getNotShippedYetCount()
})
</script>

<style scoped></style>
