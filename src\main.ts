import directives from '@/directives/index'
import pinia from '@/stores'
import request from '@/utils/remote-request'
import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import { setupRouter } from './router/setupRouter'
import 'virtual:uno.css'
import '@/assets/default.less'
import './utils/eventBus'

const app = createApp(App)
app.use(request)
app.use(pinia)
setupRouter(app)
app.use(directives)

router.isReady().then(() => app.mount('#app'))
