<!--
 * @Description:
 * @Author: 景 彡
 * @Date: 2024-11-12 14:59:23
 * @LastEditors: 景 彡
-->
<template>
  <div class="flex gap-16px">
    <c-input-money v-model:value="model.cashback!" addon-before="￥" addon-after="元" :step="10" :unit="UnitType.元" />
    <c-input-number v-model:value="model.points!" addon-before="+" addon-after="积分" :min="1">
      <template #readOnly>
        <span class="mr-4px">{{ model.points }}</span>积分
      </template>
    </c-input-number>
    <c-input-number v-model:value="model.lotteryActivityCount!" addon-before="+" addon-after="抽奖次数" :min="1">
      <template #readOnly>
        <span class="mr-4px">{{ model.lotteryActivityCount }}</span>抽奖次数
      </template>
    </c-input-number>
  </div>
</template>

<script setup lang="ts">
import { ReferralRewards } from '@/api/models'
import { Form } from 'ant-design-vue'
import { UnitType } from 'ch2-components/lib/input-money/types'

const formItemContext = Form.useInjectFormItemContext()

const value = defineModel('value', { default: () => new ReferralRewards() })

const model = ref(new ReferralRewards())

watch(value, () => {
  if (value.value) {
    model.value = value.value
  }
}, { immediate: true })

watch([() => model.value.cashback, () => model.value.points, () => model.value.lotteryActivityCount], () => {
  value.value = model.value
  formItemContext.onFieldChange()
})
</script>

<style scoped>

</style>
