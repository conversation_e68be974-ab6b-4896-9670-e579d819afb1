import { $auth } from '@/permission'
/*
 * @Author: 龙兆柒 <EMAIL>
 * @Date: 2023-05-05 08:58:23
 * @LastEditors: luckymiaow
 * @LastEditTime: 2023-05-15 16:13:55
 * @FilePath: \ch2-template-vue\src\directives\auth.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/**
 * v-auth
 * 按钮权限指令
 */
import { type Directive, type DirectiveBinding, nextTick } from 'vue'

const auth: Directive = {
  created: utilHas,
  updated: utilHas,
  mounted: utilHas,
}

function utilHas(el: Element, binding: DirectiveBinding<any>) {
  nextTick(async () => {
    let { value } = binding
    if (!value)
      value = el.textContent
    if ($auth(value) === false && el)
      el.remove()
  })
}

export default auth
