import { ProductStatus } from "./ProductStatus";
import { ProductType } from "./ProductType";
/**产品表*/
export class Product {
  /**产品名称*/
  name?: string | null | undefined = null;
  /**描述*/
  description?: string | null | undefined = null;
  /**特点*/
  feature?: string | null | undefined = null;
  /**规格（18kg/桶）*/
  specs?: string | null | undefined = null;
  /**详情（富文本）*/
  details?: string | null | undefined = null;
  /**图片（Path*/
  image?: string | null | undefined = null;
  /**价格*/
  price: number = 0;
  /**产品状态*/
  status: ProductStatus = 0;
  /**产品类型*/
  typeId: GUID = "00000000-0000-0000-0000-000000000000";
  /**产品类型*/
  type?: ProductType | null | undefined = null;
  /**产品生产编号*/
  productionNumber?: string | null | undefined = null;
  /**软删除标记*/
  softDeleted: boolean = false;
  /**软删除者*/
  deletedBy?: string | null | undefined = null;
  /**软删除时间*/
  deleted?: Dayjs | null | undefined = null;
  /**创建时间*/
  created: Dayjs = dayjs();
  /**修改时间*/
  modified?: Dayjs | null | undefined = null;
  /**创建者*/
  createdBy?: string | null | undefined = null;
  /**修改者*/
  modifiedBy?: string | null | undefined = null;
  /**主键*/
  id: GUID = "00000000-0000-0000-0000-000000000000";
}
