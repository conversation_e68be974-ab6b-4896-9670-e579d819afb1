{"name": "ch2-template-vue", "type": "module", "version": "0.1.24", "private": true, "scripts": {"serve": "vite --config ./config/vite.config.dev.ts", "dev": "vite --config ./config/vite.config.dev.ts --debug hmr", "build": "vite build --config ./config/vite.config.prod.ts", "build:test": "pnpm size-check", "doc": "ch2-doc", "lint": "eslint .", "lint:fix": "eslint . --fix", "preview": "vite preview", "lint-staged": "lint-staged", "check:env": "node ./config/checkLocalEnv.js", "cui": "pnpm add ch2-components", "size-check": "npx vite-bundle-visualizer -c ./config/vite.config.prod.ts", "postinstall": "npx simple-git-hooks & pnpm check:env"}, "dependencies": {"@ant-design/icons-vue": "7.0.1", "@microsoft/signalr": "^7.0.14", "@types/file-saver": "^2.0.7", "@vueuse/core": "^10.11.1", "ant-design-vue": "4.1.2", "axios": "^1.7.7", "ch2-components": "^3.4.52", "ckeditor5": "^43.1.1", "dayjs": "^1.11.13", "decimal.js": "^10.4.3", "echarts": "^5.5.1", "file-saver": "^2.0.5", "iframe": "^1.0.0", "jsqr": "^1.4.0", "jszip": "^3.10.1", "lodash-es": "^4.17.21", "mitt": "^3.0.1", "pinia": "2.0.36", "pinia-plugin-persistedstate": "^3.2.3", "sortablejs": "^1.15.3", "tinycolor2": "^1.6.0", "vue": "3.5.10", "vue-i18n": "^9.14.0", "vue-router": "^4.4.5", "vue3-colorpicker": "^2.3.0", "vue3-json-viewer": "^2.2.2", "xlsx": "^0.18.5"}, "devDependencies": {"@antfu/eslint-config": "^3.8.0", "@babel/core": "^7.25.2", "@iconify/json": "^2.2.253", "@tsconfig/node-lts-strictest-esm": "^18.12.1", "@types/fs-extra": "^9.0.13", "@types/lodash-es": "^4.17.12", "@types/node": "^18.19.53", "@types/tinycolor2": "^1.4.6", "@unocss/eslint-config": "^0.62.4", "@unocss/preset-icons": "^0.58.9", "@vitejs/plugin-vue": "^5.1.4", "@vitejs/plugin-vue-jsx": "3.1.0", "@vue/compiler-sfc": "^3.5.8", "colors": "^1.4.0", "eslint": "9.11.1", "eslint-plugin-format": "^0.1.2", "fs-extra": "^11.2.0", "highlight.js": "^11.10.0", "less": "^4.2.0", "lint-staged": "^13.3.0", "path": "^0.12.7", "postcss": "^8.4.47", "prettier": "^3.3.3", "simple-git-hooks": "^2.11.1", "swagger-generator-api": "^1.0.29", "ts-node": "^10.9.2", "typescript": "^5.6.2", "unocss": "^0.58.9", "unplugin-auto-import": "^0.18.3", "unplugin-vue-components": "0.27.3", "unplugin-vue-router": "^0.10.8", "vite": "5.0.10", "vite-bundle-visualizer": "^0.10.1", "vite-plugin-compression": "^0.5.1", "vite-plugin-html": "3.2.1", "vite-plugin-vue-devtools": "^7.4.6", "vite-plugin-vue-layouts": "^0.11.0", "vue-component-type-helpers": "^1.8.27"}, "overrides": {"@babel/helper-module-imports": "~7.22.15"}, "pnpm": {"overrides": {"@babel/helper-module-imports": "~7.22.15"}}, "resolutions": {"@babel/helper-module-imports": "~7.22.15"}, "simple-git-hooks": {"pre-commit": "pnpm lint-staged"}, "lint-staged": {"*": "eslint --fix"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}