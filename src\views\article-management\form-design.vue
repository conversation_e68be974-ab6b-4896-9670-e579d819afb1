<!--
 * @Description: ^_^
 * @Author: sharebravery
 * @Date: 2023-05-29 10:56:47
-->
<template>
  <div>
    <c-pro-table
      row-key="id" immediate
      :show-del-btn="false"
      show-add-btn
      :columns="columns"
      :api="api.FormDesigns.FindDesign_GetAsync" operation
      @add-row="stateChange()"
    >
      <template #operation="{ record }">
        <a-button type="link" @click="stateChange(record)">
          编辑
        </a-button>
        <a-button type="link" @click="() => formDataViewRef?.lookData(record)">
          查看数据
        </a-button>
      </template>
    </c-pro-table>

    <c-modal v-model:visible="visible" wrap-class-name="form-design-modal" :full-screen="false" full-modal :footer="null" title="设计表单">
      <div class="flex flex-1 flex-col">
        <div>
          <a-form layout="inline" :model="form">
            <div class="w-full flex items-center justify-between b-1px b-#ddd b-solid pr16px">
              <a-descriptions bordered size="small">
                <a-descriptions-item label="名称">
                  <a-form-item>
                    <a-input v-model:value="form.name" placeholder="请输入名称" />
                  </a-form-item>
                </a-descriptions-item>
                <a-descriptions-item label="KEY">
                  <a-form-item>
                    <a-input v-model:value="form.key" :disabled="form.id !== Guid.empty" placeholder="请输入KEY" />
                  </a-form-item>
                </a-descriptions-item>
                <a-descriptions-item label="描述">
                  <a-form-item>
                    <a-input v-model:value="form.des" placeholder="请输入描述" />
                  </a-form-item>
                </a-descriptions-item>

                <a-descriptions-item label="关联到文章">
                  <a-form-item>
                    <c-select
                      v-model:value="form.informationId" immediate :filter-option="false" :api="api.Informations.GetInformation_PostAsync"
                      show-search
                      :post-params="[]" :params="informationParams" :field-names="{ label: 'title', value: 'id' }" :page="true" @search="fetchInformation"
                    />
                  </a-form-item>
                </a-descriptions-item>
              </a-descriptions>
              <div>
                <a-button type="primary" @click="save()">
                  保存
                </a-button>

                <a-button class="ml16px" type="primary" @click="save(true)">
                  保存并关闭
                </a-button>
              </div>
            </div>
          </a-form>
        </div>
        <div class="mt8px h0 min-h400px flex flex-1 b-1px b-#ddd b-solid">
          <div class="box-border w200px gap16px b-1px b-#ddd b-r-solid bg-#FAFAFA p16px">
            <div class="flex flex-wrap gap16px">
              <div
                v-for="item, key in componentList" :key="key"
                class="box-border h-[fit-content] w[calc(50%-8px)] cursor-pointer rounded-6px bg-primary p8px text-center text-18px c-white font-600 shadow-[rgba(0,0,0,0.15)_0_2px_8px] hover:bg-primary-active"
                @click="addComp(key)"
              >
                {{ item.label }}
              </div>
            </div>
          </div>
          <div class="w0 flex-1 overflow-y-auto pl8px pr16px pt-16px">
            <a-form :layout="form.layout">
              <div ref="formDesignRef">
                <div
                  v-for="item, i in formStructure" :key="item.field!"
                  class="draggable-item row-from mb16px w-full flex items-center"
                  :class="{ 'bg-primary-active ': currentFormItem?.field === item.field }"
                  @click="selectComp(i)"
                >
                  <div class="hand-move mr8px cursor-move hover:c-#1890FF">
                    <c-icon-holder-outlined />
                  </div>
                  <a-form-item :label="item.label" class="w0 flex-1 !mb0" :required="item.required">
                    <component :is="(componentList as any)[item.component!].el" v-bind="item" style="width: 100%;" />
                  </a-form-item>

                  <div class="ml8px mr8px cursor-pointer c-red-6" title="删除" @click.stop="() => formStructure.splice(i, 1)">
                    <c-icon-delete-outlined />
                  </div>
                </div>
                <i v-if="!formStructure.length">点击左侧组件进行添加</i>
              </div>
            </a-form>
          </div>

          <div
            class="w400px flex gap16px b-1px b-#ddd b-l-solid"
          >
            <a-descriptions v-if="currentFormItem" bordered size="small" :column="1" class="w-full">
              <a-descriptions-item label="名称">
                <a-input v-model:value="currentFormItem.label" />
              </a-descriptions-item>
              <a-descriptions-item label="字段名">
                <a-input v-model:value="currentFormItem.field" disabled />
              </a-descriptions-item>
              <a-descriptions-item label="默认值">
                <a-input v-model:value="currentFormItem.value" />
              </a-descriptions-item>
              <a-descriptions-item label="选项">
                <template v-for="(tag, index) in currentFormItem.options" :key="tag">
                  <a-tag closable @close="() => currentFormItem?.options?.splice(index, 1)">
                    {{ tag.label }}
                  </a-tag>
                </template>
                <a-input
                  v-if="optionState.inputVisible"
                  v-model:value="optionState.inputValue"
                  type="text"
                  size="small"
                  auto-focus
                  :style="{ width: '78px' }"
                  @blur="setOption()"
                  @keyup.enter="setOption()"
                />
                <a-tag v-else style="background: #fff; border-style: dashed" @click="() => optionState.inputVisible = true">
                  <c-icon-plus-outlined />
                  添加选项
                </a-tag>
              </a-descriptions-item>
              <a-descriptions-item label="是否必填">
                <c-boolean-select v-model:value="currentFormItem.required" class="!w-full" />
              </a-descriptions-item>
              <a-descriptions-item label="占位符">
                <c-input v-model:value="currentFormItem.placeholder" />
              </a-descriptions-item>
            <!-- <a-descriptions-item label="校验正则">
              <c-input v-model:value="currentFormItem.regexe" />
            </a-descriptions-item> -->
            <!-- <a-descriptions-item label="校验提示词">
              <c-input v-model:value="currentFormItem.verificationMessage" />
            </a-descriptions-item> -->
            </a-descriptions>
          </div>
        </div>
      </div>
    </c-modal>

    <FormDataView ref="formDataViewRef" />
  </div>
</template>

<script setup lang="tsx">
import type { ColumnProps } from 'ch2-components/lib/pro-table/types'
import * as api from '@/api'
import { FormDesign, FormState, FormStructure } from '@/api/models'
import { Input, message, notification, Select, Textarea } from 'ant-design-vue'
import { debounce } from 'lodash-es'
import Sortable from 'sortablejs'
import { computed, nextTick, reactive, ref, watch } from 'vue'
import FormDataView from './components/FormDataView.vue'

definePage({
  meta: {
    title: '表单管理',
  },
})

const appStore = useAppStore()

const formDataViewRef = useTemplateRef('formDataViewRef')

const columns = reactive<ColumnProps<FormDesign>[]>([
  {
    dataIndex: 'name',
    title: '表单名称',
    key: 'name',
    search: {
      el: 'input',
      method: 'GET',
      emptyValue: '',
    },
  },
  {
    dataIndex: 'key',
    title: 'KEY',
    key: 'key',
  },

  { dataIndex: 'updateTime', title: '更新时间', key: 'updateTime', dateFormat: true },
  {
    dataIndex: ['information', 'title'],
    title: '关联文章',
    key: 'information',
    bodyCell: ({ record }) => {
      return (
        <a
          href={record.information?.url ? record.information : `${appStore.setting.siteUrl}/business-school/detail/${record.information?.informationId}`}
          target="_blank"
        >
          {record.information?.title}
        </a>
      )
    },
  },
  {
    dataIndex: 'unCount',
    title: '未处理数量',
    key: 'unCount',
  },
  {
    dataIndex: 'formState',
    title: '状态',
    key: 'formState',
    enum: FormState,
    search: {
      el: 'enum-select',
      method: 'GET',
      attrs: { enum: FormState },
    },
  },
])

const form = ref(new FormDesign())

const { componentList, addComp, formDesignRef, initFormDesign, formStructure, selectComp, currentFormItem, optionState } = useFormDesign(data => form.value.formStructure = data)

const visible = ref(false)

function stateChange(record?: FormDesign) {
  if (record) {
    notification.warn({
      message: '修改表单结构会导致对应字段的数据丢失，请谨慎操作！',
    })
  }
  form.value = record || new FormDesign()
  visible.value = true
  initFormDesign(form.value.formStructure)
}

function setOption() {
  if (optionState.inputValue)
    currentFormItem.value?.options?.push({ label: optionState.inputValue, value: optionState.inputValue })

  optionState.inputVisible = false
  optionState.inputValue = ''
}

function save(close = false) {
  form.value.layout = 'horizontal'
  api.FormDesigns.SaveDesign_PostAsync(form.value).then((data) => {
    form.value = data
    if (close)
      visible.value = false
    message.success('保存成')
  })
}

function useFormDesign(change?: (data: FormStructure[]) => void) {
  const formDesignRef = ref<HTMLDivElement>()

  const formStructure = ref<FormStructure[]>([])

  const optionState = reactive({
    inputVisible: false,
    inputValue: '',
  })

  const currentIndex = ref(-1)

  const currentFormItem = computed(() => currentIndex.value > -1 ? formStructure.value[currentIndex.value] : null)

  const componentList = {
    input: {
      el: Input,
      label: '输入框',
      attrs: { style: { } },
    },
    select: {
      el: Select,
      label: '选择器',
    },
    textarea: {
      el: Textarea,
      label: '文本域',
    },
  }

  const addComp = (comp: keyof typeof componentList) => {
    const item = new FormStructure()
    item.component = comp
    item.label = componentList[comp].label
    item.field = `${comp}-${Date.now().toString()}`
    formStructure.value.push(item)
    currentIndex.value = formStructure.value.length - 1
  }

  const initFormDesign = (data?: FormStructure[] | undefined | null) => {
    formStructure.value = data || []
    nextTick(() => {
      const _sortable = new Sortable(formDesignRef.value!, {
        animation: 150,
        handle: '.hand-move',
        onEnd({ oldIndex, newIndex }) {
          formStructure.value.splice(newIndex || 0, 0, formStructure.value.splice(oldIndex || 0, 1)[0])
        },
      })
    })
  }

  const selectComp = (i: number) => {
    currentIndex.value = i
  }

  watch(formStructure, (v) => {
    change && change(v)
  })

  return { componentList, addComp, formDesignRef, initFormDesign, formStructure, selectComp, currentFormItem, optionState }
}

const informationParams = ref({ title: '' })

const fetchInformation = debounce((v) => {
  informationParams.value = {
    title: v,
  }
}, 300)
</script>

<style scoped lang="less">
.header {
  .search-btn {
    margin-right: 16px;
  }
  .header-button {
    margin-top: 16px;
  }
}
</style>

<style lang="less">
.form-design-modal {
  .ant-modal-content {
    .ant-modal-body {
      display: flex;
      height: calc(100vh - 55px);
    }
  }
}
.form-design-data-modal {
  .ant-modal-body {
    padding: 8px;
  }
  .table-main {
    margin-top: 0;
    padding: 0;
  }
}
</style>
