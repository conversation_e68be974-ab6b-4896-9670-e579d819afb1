<!--
 * @Description:
 * @Author: 景 彡
 * @Date: 2024-12-26 09:23:03
 * @LastEditors: 景 彡
-->
<template>
  <div class="h-full bg-#fff p-16px">
    <a-alert message="设置后系统会进行相应扣款，到达阈值会提醒用户" type="success" />
    <div class="mt-32px flex items-center">
      <label class="inline-block w-120px text-right">余额设置：</label>
      <a-input-group compact>
        <c-input-money v-model:value="remainingBalance" style="width: 400px" :unit="UnitType.元" show-unit />
        <a-button type="primary" @click="balanceSave">保存</a-button>
      </a-input-group>
    </div>
    <div class="mt-32px flex items-center">
      <label class="inline-block w-120px text-right">提醒阈值：</label>
      <a-input-group compact>
        <c-input-money v-model:value="balanceThreshold" style="width: 400px" :unit="UnitType.元" show-unit />
        <a-button type="primary" @click="remindSave">保存</a-button>
      </a-input-group>
    </div>
  </div>
</template>

<script lang='ts' setup>
import * as api from '@/.generated/apis'
import { message } from 'ant-design-vue'
import { UnitType } from 'ch2-components/lib/input-money/types'

definePage({
  meta: {
    title: '金额阈值管理',
    local: true,
  },
})

/** 阈值 */
const balanceThreshold = ref(0)

/** 系统金额 */
const remainingBalance = ref(0)

function getData() {
  api.SystemBaseInfo.GetBalanceThresholdAsync().then((res) => {
    balanceThreshold.value = res
  })
  api.SystemBaseInfo.GetRemainingBalanceAsync().then((res) => {
    remainingBalance.value = res
  })
}

async function balanceSave() {
  await api.SystemBaseInfo.SaveRemainingBalance_GetAsync({ key: remainingBalance.value })
  message.success('余额保存成功')
}

// 提醒阈值
async function remindSave() {
  await api.SystemBaseInfo.SaveBalanceThreshold_GetAsync({ key: balanceThreshold.value })
  message.success('提醒阈值保存成功')
}

onMounted(() => {
  getData()
})
</script>

<style scoped>

</style>
