/*
 * @Author: 龙兆柒 <EMAIL>
 * @Date: 2022-06-29 09:43:14
 * @LastEditors: 龙兆柒
 * @LastEditTime: 2023-07-30 17:41:52
 * @FilePath: \ch2-template-vue\src\router\index.ts
 * @Description:
 */

import type { RouteRecordRaw } from 'vue-router'
import { createRouter, createWebHistory } from 'vue-router'
import { getMenu } from './utils/getMenu'
import { HomeRoute } from './utils/useRouteConfig'

const router = createRouter({
  history: createWebHistory(),
  scrollBehavior: (_to, _from, savedPosition) => {
    if (savedPosition)
      return savedPosition

    return { top: 0 }
  },

  routes: [{
    path: '/',
    redirect: HomeRoute.path,
    meta: { hidden: true },
  }],
})

const _routerMenu = ref<RouteRecordRaw[]>([])
const _routerAllMenu = ref<RouteRecordRaw[]>([])

/**
 * 由setupRouter赋值，避免循环依赖产生
 */
export class dynamicRouter {
  static Register = () => Promise.resolve()
  static Delete = () => { }
  static readonly setRoutes = (r: RouteRecordRaw[], allR: RouteRecordRaw[], _local: RouteRecordRaw[]) => {
    _routerMenu.value = getMenu(r)
    _routerAllMenu.value = getMenu(allR, false)
  }
}

export const routerMenu = computed<RouteRecordRaw[]>(() => {
  return _routerMenu.value
})

export const routerAllMenu = computed<RouteRecordRaw[]>(() => {
  return _routerAllMenu.value
})

eventBus.on('login-success', async () => {
  await dynamicRouter.Register()
  const $route = router.currentRoute.value
  const to
    = $route.query.redirect && $route.query.redirect !== '/404' && $route.query.redirect !== '/logoutReload'
      ? $route.query.redirect
      : HomeRoute.path

  router.push({ path: to as any })
  await signalR.start()
})

export default router
