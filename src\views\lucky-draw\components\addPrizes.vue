<!--
 * @Description:
 * @Author: 景 彡
 * @Date: 2024-11-01 18:13:40
 * @LastEditors: 景 彡
-->
<template>
  <a-drawer v-model:open="open" title="添加奖品" width="600px" :mask-closable="false" destroy-on-close placement="right">
    <c-pro-form
      ref="proFormRef"
      v-model:value="model" :read-only="false" :label-col="{ span: 4 }" :fields="fields"
      :descriptions="{ column: 1, bordered: true, labelStyle: { width: '200px' } }"
    >
      <template #number>
        <c-input-money v-if="model.type === models.LotteryPrizesType.现金红包" v-model:value="model.number!" addon-before="￥" addon-after="元" :step="10" :unit="UnitType.元" :min="0" />
        <a-input-number v-else id="inputNumber" v-model:value="model.number!" :min="model.type === models.LotteryPrizesType.谢谢惠顾 ? 0 : 1" style="width: 100%" />
      </template>
      <!-- <template #end>
          <c-range-picker v-model:start-time="model.start" v-model:end-time="model.end" :read-only="readonly"
            show-time />
        </template>> -->
      <template #images>
        <div v-if="model.images" class="coverBox size-140px overflow-hidden">
          <ImageView
            :src="(model.images)" alt="avatar" :preview="true" :del-ico="true"
            style="height: 140px; width:140px ; object-fit:cover" @del-image="() => model.images = ''"
          />
        </div>
        <a-button v-else type="dashed" block class="size-25" @click="avatarUpload">
          <template #icon>
            <c-icon-plus-outlined />
          </template>
          上传
        </a-button>
      </template>
    </c-pro-form>

    <template #extra>
      <a-button style="margin-right: 8px" @click="open = false">取消</a-button>
      <a-button type="primary" @click="save()">保存</a-button>
    </template>
  </a-drawer>
</template>

<script setup lang='tsx'>
import type { FormField } from 'ch2-components/types/pro-form/types'
import * as api from '@/api'
import * as models from '@/api/models'
import { LotteryPrizesType } from '@/api/models'
import InputPercentage from '@/components/InputPercentage.vue'
import { message } from 'ant-design-vue'
import { UnitType } from 'ch2-components/lib/input-money/types'
import { ref } from 'vue'

const props = defineProps<{
  actiyityId: string
}>()

const emit = defineEmits<{
  (e: 'success'): void
}>()

const model = defineModel('value', { default: new models.LotteryPrizesEdit() })

const proFormRef = useTemplateRef('proFormRef')

const open = ref(false)

const fields = shallowRef<FormField[]>([
  { label: '奖品类型', el: 'enum-select', prop: 'type', required: true, attrs: { placeholder: '请选择活动状态', enum: models.LotteryPrizesType, onChange() {
    if (!model.value.name) {
      model.value.name = LotteryPrizesType[model.value.type]
    }
  } }, formItem: {} },
  { label: '奖品名称', el: 'input', prop: 'name', required: true, attrs: { placeholder: '请输入奖品名称' }, formItem: {} },
  { label: '现金(元)/积分/抽奖次数', el: 'input', prop: 'number', required: true, attrs: { placeholder: '请输入现金(分)/积分/抽奖次数' }, formItem: {} },
  { label: '图片', el: 'input', prop: 'images', required: true, attrs: { placeholder: '请输入图片（Path' }, formItem: {} },
  {
    label: '中奖概率',
    el: InputPercentage,
    prop: 'probability',
    attrs: { placeholder: '请输入中将概率', style: { width: '100%' } },
    formItem: {},
  },
  { label: '描述', el: 'textarea', prop: 'description', required: true, attrs: { placeholder: '请输入描述' }, formItem: {} },
])

async function save() {
  if (model.value.type !== models.LotteryPrizesType.谢谢惠顾 && model.value.number <= 0)
    return message.error('现金(元)/积分/抽奖次数数量必须大于0')
  await proFormRef.value?.validate()
  await api.Lotterys.SavePrizeByActiyityId_PostAsync({ actiyityId: props.actiyityId }, model.value)
  emit('success')
  open.value = false
  message.success('保存成功')
}

function avatarUpload() {
  useFileMangerModal((files) => {
    model.value.images = files[0]?.id
  }, { multiple: false, immediateReturn: true, menu: [models.FileType.图片] })
}

defineExpose({
  open,
})
</script>

<style scoped lang='less'></style>
