<!--
 * @Description:
 * @Author: 景 彡
 * @Date: 2024-11-04 10:53:26
 * @LastEditors: 景 彡
-->
<!-- eslint-disable promise/param-names -->
<!-- eslint-disable unused-imports/no-unused-vars -->
<template>
  <a-spin :spinning="loading">
    <a-card title="基本信息">
      <a-form ref="formRef" :label-col="{ xs: { span: 3 }, md: { span: 4 } }" :model="form">
        <a-form-item label="通知类型" name="noticeType">
          <c-enum-select
            v-model:value="form.noticeType"
            :enum="models.NoticeType"
            placeholder="请输入"
            read-only
          />
        </a-form-item>
        <a-form-item label="通知范围" name="scope">
          <c-enum-select
            v-model:value="form.scope"
            :enum="models.NoticeScope"
            placeholder="请输入"
          />
        </a-form-item>
        <template v-if="form.scope !== models.NoticeScope.公开">
          <a-card :title="form.scope === models.NoticeScope.部分可见 ? '选择可见用户' : '不可见用户' " :body-style="{ padding: '0', marginTop: 0 }">
            <c-pro-table
              v-model:search-form="searchForm"
              :columns="columns"
              :api="api.ClientUserManage.GetUserListAsync"
              row-key="id"
              immediate
              :operation="false"
              :row-selection="rowSelection"
              :show-search="false"
              :show-tool-btn="false"
              :hide-header="true"
            />
          </a-card>
        </template>
      </a-form>
      <div class="mt-16px text-right">
        <a-button type="primary" ghost @click="onSave(models.NoticeStatus.草稿)">保存为草稿</a-button>
        <a-button class="ml-16px" type="primary" @click="onSave(models.NoticeStatus.已发送)">立即发送</a-button>
      </div>
    </a-card>
  </a-spin>
</template>

<script lang="ts" setup>
import * as api from '@/api'
import * as models from '@/api/models'
import { deepCopy } from '@/utils/util'
import { Form, message } from 'ant-design-vue'
import { ref, watch } from 'vue'

const props = defineProps<{ formData: models.Notice }>()

const emits = defineEmits<(event: 'update:formData', formData: models.Notice) => void>()

const saveSucces = inject<() => void>('saveSucces')

const formRef = ref(0) // form表单ref

const loading = ref<boolean>(false)

const form = ref<models.NoticeEditModel>(new models.NoticeEditModel()) // 表单

const rowSelection = ref({
  selectedRowKeys: form.value.clientUserIds,
  onChange: (selectedRowKeys: any) => {
    rowSelection.value.selectedRowKeys = selectedRowKeys
    form.value.clientUserIds = selectedRowKeys
  },
})

watch(() => props.formData, (v) => {
  if (!v)
    return
  form.value = deepCopy(v)
  rowSelection.value.selectedRowKeys = form.value.clientUserIds
}, { immediate: true })

// eslint-disable-next-line unused-imports/no-unused-vars
const { validate, validateInfos } = Form.useForm(form)

async function onSave(status: models.NoticeStatus) {
  await validate()
    .then(async () => {
      try {
        form.value.title = props.formData.title
        if (!form.value.title)
          return message.error('请填写标题')
        loading.value = true

        form.value.content = props.formData.content
        const res = await api.Notices.SaveNotice_PostAsync({ status }, form.value)
        emits('update:formData', res)
        saveSucces && saveSucces()
        message.success('通知保存成功！')
        loading.value = false
      }
      catch (error: any) {
        message.error(`通知更新失败：${error.message}`)
        loading.value = false
      }
    })
    .catch((val) => {
      console.log('%c [ val ]-92', 'font-size:13px; background:pink; color:#bf2c9f;', val)
    })
}

const searchForm = ref({
  userName: '',
  phoneNumber: '',
})

const columns = ref([
  {
    dataIndex: 'name',
    title: '姓名',
  },
  {
    dataIndex: 'phoneNumber',
    title: '电话',
  },

])

defineExpose({ onSave })
</script>

<style scoped lang="less">
.coverBox {
  position: relative;
  width: 200px;
  height: 200px;

  .icon {
    position: absolute;
    right: 0;
    top: 0;
    background-color: #ccc;
    width: 20px;
    height: 20px;
    line-height: 20px;
    cursor: pointer;
  }
}
.push-article {
  background-color: #fff;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.12);
  padding: 10px;
  border-radius: 4px;
  margin-right: 20px;
}
.avatar-uploade {
  :deep(.ant-upload) {
    width: 200px;
    height: 192px;
  }
}
</style>
