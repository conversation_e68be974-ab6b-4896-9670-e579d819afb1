import { RoleName } from './RoleName'

export function existenceRole(targe: RoleName[] | RoleName) {
  const { active: userInfo } = useUserStore()
  const roles = userInfo.roles
  return roles?.some(item => targe.includes(item.name as any))
}

export function isAdmin() {
  const { active: userInfo } = useUserStore()
  return userInfo.roles.some(v => v.name === RoleName.超级管理员)
}

/**
 * 业务权限判断
 * @param value
 * @returns
 */
export function $auth(value: RoleName | RoleName[]): boolean {
  const { active: userInfo } = useUserStore()
  const currentPageRoles = userInfo.roles || []

  if (Array.isArray(value)) {
    const hasPermission = value.some(item => currentPageRoles.includes(item))
    return hasPermission
  }
  else {
    return currentPageRoles.includes(value)
  }
}
