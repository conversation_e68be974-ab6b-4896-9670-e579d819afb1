ssl_certificate /etc/nginx/testcerts/fullchain.cer;
ssl_certificate_key /etc/nginx/testcerts/test.key;
gzip_static  on;
server {
    listen 80;
    listen 443 ssl http2;
    listen 1443 ssl http2;
    root   /usr/share/nginx/html;
    index  index.html index.htm ;

    server_tokens off;

    location ~* /(api|files|uploaded-images|swagger|permission-api|.well-known|connect|doc|msgHub) {
        proxy_pass   "$scheme://************:$server_port";
        proxy_pass_header Server;
        proxy_set_header Host "$host:$server_port";
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Scheme $scheme;
    }

    location / {
        try_files $uri $uri/ /index.html;
    }

    client_max_body_size 500M;
    client_body_buffer_size 1M;
}
