<!-- eslint-disable unused-imports/no-unused-vars -->
<!--
 * @Description:
 * @Author: nono
 * @Date: 2023-06-12 11:54:16
 * @LastEditors: 景 彡
 * @LastEditTime: 2024-12-24 15:35:34
-->
<template>
  <c-modal v-model:visible="visible" :title="title" width="35%" :style="{ top: '0px' }" @ok="onSave" @cancel="onCancel">
    <c-form ref="formRef" :model="form" :label-col="{ span: 3 }" :wrapper-col="{ span: 20 }" :rules="rules">
      <c-form-item required has-feedback label="标题" name="name" v-bind="validateInfos.name">
        <c-input v-model:value="form.name" placeholder="请输入标题" allow-clear />
      </c-form-item>
      <c-form-item has-feedback label="类型" name="description" v-bind="validateInfos.description">
        <c-enum-select v-model:value="form.categoryType" :enum="models.CategoryType" allow-clear show-count />
      </c-form-item>
      <c-form-item required has-feedback label="分类">
        <a-tree-select
          v-model:value="form.parentId"
          style="width: 100%"
          :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
          placeholder="请选择"
          allow-clear
          :tree-data="treeData"
          :field-names="{ children: 'children', label: 'name', value: 'id' }"
          show-checked-strategy="SHOW_ALL"
          show-search
          tree-node-filter-prop="name"
        />
      </c-form-item>

      <c-form-item required has-feedback label="说明" name="description" v-bind="validateInfos.description">
        <c-textarea v-model:value="form.description" placeholder="请输入说明" :rows="5" allow-clear />
      </c-form-item>

      <c-form-item has-feedback label="是否显示" name="menuShow" v-bind="validateInfos.menuShow">
        <c-boolean-select v-model:value="form.menuShow" placeholder="请选择是否在菜单中显示" allow-clear />
      </c-form-item>
      <c-form-item has-feedback label="是否置顶" name="menuTop" v-bind="validateInfos.menuTop">
        <c-boolean-select v-model:value="form.menuTop" placeholder="是否处于菜单中最上层" allow-clear />
      </c-form-item>
    </c-form>
  </c-modal>
</template>

<script lang="ts" setup>
import * as api from '@/api'
import * as models from '@/api/models'
import { Form, message } from 'ant-design-vue'
import { nextTick, reactive, ref, watch } from 'vue'

const emit = defineEmits(['success'])

const visible = ref(false) // 是否显示

const formRef = ref() // form表单ref

const title = ref<'新增分类' | '编辑分类'>('新增分类')

const form = ref<models.InformationCategoryEditModel>(new models.InformationCategoryEditModel()) // 表单

const categoryData = ref<models.InformationCategory>(new models.InformationCategory()) // 接受父级传过来的分类对象

watch(visible, async (v) => {
  if (v) {
    getOptions()
    await nextTick()
    form.value = viewModelToEditModel(categoryData.value, new models.InformationCategoryEditModel())

    if (!categoryData.value.id) {
      form.value = new models.InformationCategoryEditModel()
      form.value.menuShow = true
      title.value = '新增分类'
    }
    else {
      title.value = '编辑分类'
    }
  }
})

/** 表单验证规则 */
const rules = reactive({
  title: [{ required: true, message: '请输入!', trigger: 'change' }],
  content: [{ required: true, message: '请输入!', trigger: 'change' }],
})

const { resetFields, validate, validateInfos } = Form.useForm(form, rules)

async function onCancel() {
  visible.value = false
  resetFields()
}

async function onSave() {
  await validate()
    .then(async () => {
      try {
        if (!form.value.name || !form.value.description)
          return message.error('填写必填项')

        if (!Guid.isNotNull(categoryData.value.id)) {
          await api.InformationCategories.CreateInformationCategory_PostAsync(form.value)
          message.success('分类添加成功！')
        }
        else {
          await api.InformationCategories.ModifyInformationCategory_PostAsync(form.value)
          message.success('分类修改成功！')
        }
        emit('success')
        onCancel()
      }
      catch (error: any) {
        message.error(`资讯信息更新失败：${error.message}`)
      }
    })
    .catch(() => {
      throw new Error('字段校验未通过')
    })
}

const treeData = ref<models.InformationCategoryView[]>([])

async function getOptions() {
  treeData.value = await api.InformationCategories.GeCategoryPaged_GetAsync({})
}

defineExpose({
  visible,
  categoryData,
})
</script>

<style scope lang="less"></style>
