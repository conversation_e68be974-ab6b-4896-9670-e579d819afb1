import { InformationCategory } from "./InformationCategory";
import { InfoImage } from "./InfoImage";
import { InformationStatus } from "./InformationStatus";
/**文章列表*/
export class InformationPageView {
  /**主键*/
  id: GUID = "00000000-0000-0000-0000-000000000000";
  /**文章访问Id*/
  informationId: GUID = "00000000-0000-0000-0000-000000000000";
  /**分类*/
  informationCategory?: InformationCategory[] | null | undefined = [];
  /**标题*/
  title?: string | null | undefined = null;
  /**简述*/
  description?: string | null | undefined = null;
  /**置顶*/
  isTop: boolean = false;
  /**发布作者Id*/
  postAuthorId: number = 0;
  /**填写作者名*/
  authorNames?: string | null | undefined = null;
  /**文章创建时间*/
  createdTime: Dayjs = dayjs();
  /**文章更新时间*/
  updatedTime: Dayjs = dayjs();
  /**来源*/
  source?: string | null | undefined = null;
  /**阅读人数*/
  views: number = 0;
  /**封面（相对路径的url）*/
  cover?: string | null | undefined = null;
  /**文章内的头部图片*/
  infoImages?: InfoImage[] | null | undefined = [];
  /**文章状态*/
  status: InformationStatus = 0;
  /**微信公众号文章*/
  msgid?: string | null | undefined = null;
  /**url*/
  url?: string | null | undefined = null;
  /**标签，以,分割*/
  tags?: string | null | undefined = null;
  /**附件*/
  enclosureObj?: InfoImage[] | null | undefined = [];
  /**关联表单id*/
  formDesignId?: GUID = null;
}
