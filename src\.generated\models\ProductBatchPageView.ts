import { Product } from "./Product";
import { ProductBatchStatus } from "./ProductBatchStatus";
export class ProductBatchPageView {
  /**批次数量*/
  count: number = 0;
  /**激活数量*/
  activationCount: number = 0;
  /**关联产品*/
  productId: GUID = "00000000-0000-0000-0000-000000000000";
  /**产品表*/
  product?: Product | null | undefined = null;
  /**时间*/
  date: Dayjs = dayjs();
  /**批次状态*/
  status: ProductBatchStatus = 0;
  /**备注*/
  remark?: string | null | undefined = null;
  /**软删除标记*/
  softDeleted: boolean = false;
  /**软删除者*/
  deletedBy?: string | null | undefined = null;
  /**软删除时间*/
  deleted?: Dayjs | null | undefined = null;
  /**创建时间*/
  created: Dayjs = dayjs();
  /**修改时间*/
  modified?: Dayjs | null | undefined = null;
  /**创建者*/
  createdBy?: string | null | undefined = null;
  /**修改者*/
  modifiedBy?: string | null | undefined = null;
  /**主键*/
  id: GUID = "00000000-0000-0000-0000-000000000000";
}
