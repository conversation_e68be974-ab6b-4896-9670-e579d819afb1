import { ProductStatus } from "./ProductStatus";
/**二维码生产配置 使用详情*/
export class ProductBatchViewModel {
  id: GUID = "00000000-0000-0000-0000-000000000000";
  /**产品名称*/
  name?: string | null | undefined = null;
  /**图片*/
  image?: string | null | undefined = null;
  /**价格*/
  price: number = 0;
  /**规格（18kg/桶）*/
  specs?: string | null | undefined = null;
  /**产品状态*/
  status: ProductStatus = 0;
  /**产品生产编号*/
  productionNumber?: string | null | undefined = null;
  /**批次数量*/
  count: number = 0;
  /**激活数量*/
  activationCount: number = 0;
  /**发放的积分总数*/
  points: number = 0;
  /**发放的返现总数*/
  cashback: number = 0;
  /**发放的抽奖次数总数*/
  sweepstakesCount: number = 0;
}
