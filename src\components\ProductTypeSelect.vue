<template>
  <c-cascader v-model:value="selectValue" :options="options" show-search @change="updateValue" />
</template>

<script setup lang="ts">
import * as api from '@/api/'

const modelValue = defineModel('value')

const selectValue = ref([])

const options = ref<any>([])

function updateValue() {
  modelValue.value = selectValue.value.slice(-1)?.[0]
}

function findNodeFullPath(tree: any, id: any) {
  if (!tree || !id)
    return []

  const findPath = (nodes: any[], targetId: any, path: any[] = []): any[] | null => {
    for (const node of nodes) {
      const currentPath = [...path, node.value]
      if (node.value === targetId)
        return currentPath

      if (node.children) {
        const childPath = findPath(node.children, targetId, currentPath)
        if (childPath)
          return childPath
      }
    }
    return null
  }

  return findPath(tree, id) || []
}

function updateSelectValue() {
  const data = findNodeFullPath(options.value, modelValue.value)

  selectValue.value = data as any
}

watch(modelValue, () => {
  updateSelectValue()
}, { immediate: true })

onMounted(async () => {
  const data = await api.Products.QueryType_PostAsync({
    get: {
      limit: -1,
      offset: 0,
      notCounted: false,
    },
  })
  const brandMap = new Map<string, any>()

  data.items?.forEach((item) => {
    const brand = item.brand
    if (!brand)
      return // 如果品牌不存在则跳过

    // 获取或创建品牌节点
    let brandNode = brandMap.get(brand)
    if (!brandNode) {
      brandNode = {
        label: brand,
        value: brand,
        children: new Map<string, any>(),
      }
      brandMap.set(brand, brandNode)
    }

    const mainType = item.mainType
    const name = item.name

    // 存在主类型时处理二级分类
    if (mainType) {
      let mainTypeNode = brandNode.children.get(mainType)
      if (!mainTypeNode) {
        mainTypeNode = {
          label: mainType,
          value: mainType,
          children: new Map<string, any>(),
        }
        brandNode.children.set(mainType, mainTypeNode)
      }

      // 添加型号到主类型下
      if (name) {
        mainTypeNode.children.set(name, {
          label: name,
          value: item.id,
          children: [], // 叶子节点使用空数组
        })
      }
    }
    // 没有主类型时直接挂在品牌下
    else if (name) {
      brandNode.children.set(name, {
        label: name,
        value: item.id,
        children: [], // 叶子节点使用空数组
      })
    }
  })

  const convertMapToTree = (map: Map<string, any>): any =>
    Array.from(map.values()).map(node => ({
      ...node,
      children: node.children instanceof Map
        ? convertMapToTree(node.children)
        : node.children,
    }))

  const treeData = convertMapToTree(brandMap)

  options.value = treeData

  updateSelectValue()
})
</script>

<style scoped>

</style>
