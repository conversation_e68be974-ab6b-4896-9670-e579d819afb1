import { InformationCategory } from "./InformationCategory";
export class InformationCategoryView {
  name?: string | null | undefined = null;
  description?: string | null | undefined = null;
  /**文章分类*/
  data?: InformationCategory | null | undefined = null;
  children?: InformationCategoryView[] | null | undefined = [];
  informationCount: number = 0;
  /**创建时间*/
  created: Dayjs = dayjs();
  /**修改时间*/
  modified?: Dayjs | null | undefined = null;
  /**创建者*/
  createdBy?: string | null | undefined = null;
  /**修改者*/
  modifiedBy?: string | null | undefined = null;
  /**主键*/
  id: GUID = "00000000-0000-0000-0000-000000000000";
}
