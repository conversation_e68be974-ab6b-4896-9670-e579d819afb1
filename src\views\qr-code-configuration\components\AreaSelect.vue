<template>
  <div>
    <div>
      <div v-for="item, i in modelData" :key="i" class="mb4px">
        <span>{{ item.province }} {{ item.city }} {{ item.district }}  {{ item.street }}</span>
        <span
          title="删除该配置" class="i-material-symbols-delete-outline-sharp ml4px inline-block cursor-pointer v-bottom c-error"
          @click.stop="removeItem(i)"
        />
      </div>
    </div>

    <a-popconfirm
      :icon="null"
      @confirm="addItem"
    >
      <template #title>
        <c-cascader
          v-model:value="editModel"
          :options="cityJson" placeholder="请输入省份/城市"
          :field-names="{ label: 'name', value: 'name' }"
          class="mb8px w-full"
          :dropdown-style="{ zIndex: 1099 }"
        />
        <c-textarea
          v-model:value="street"
          :rows="2"
          placeholder="请输入详细地址"
          class="w-full"
        />
      </template>
      <a-button type="primary" title="添加" class="mt8px w-full" size="small" ghost>添加</a-button>
    </a-popconfirm>
  </div>
</template>

<script setup lang="ts">
import type { AddressComponentRes } from '@/.generated/models'
import { Form, message } from 'ant-design-vue'
import region from '../../../assets/region.json'

const formItemContext = Form.useInjectFormItemContext()

const modelData = defineModel<Array<AddressComponentRes>>('value', { default: [] })

const cityJson = region.map(v => ({ ...v, children: v.children?.map(p => ({ ...p, children: [] })) }))

const street = ref('')

const editModel = ref([])

function removeItem(i: number) {
  modelData.value.splice(i, 1)
  formItemContext.onFieldChange()
}

function addItem() {
  if (!modelData.value)
    modelData.value = []
  if (editModel.value.length < 2) {
    message.error('请选择完整的地址')
    return
  }
  modelData.value.push({
    nation: '中国',
    province: editModel.value?.[0],
    city: editModel.value?.[1],
    district: editModel.value?.[2],
    street: street.value,
  })
  formItemContext.onFieldChange()
  editModel.value = []
  street.value = ''
  console.log('%c [ modelData.value ]-42', 'font-size:13px; background:pink; color:#bf2c9f;', modelData.value)
}
</script>

<style scoped>

</style>
