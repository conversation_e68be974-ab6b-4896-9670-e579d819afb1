<!-- 切换数据源需要重新渲染 ,或者修改id的值来触发修改 -->
<template>
  <div class="ch2-editor w-full">
    <div v-show="!readOnly">
      <div class="header-toolbar">
        <div class="editor-toolbar justify-center">
          <div ref="toolbar" />
        </div>
      </div>
      <div ref="contentRef" class="editor-text-area" />
    </div>
    <div v-if="readOnly" class="editor-text-area px16px" v-html="replaceImgSrc(modelValue)" />
  </div>
</template>

<script setup lang="ts">
const props = defineProps<{
  closure?: boolean
  readOnly?: boolean
  id?: string
}>()

const { toolbar, contentRef, install, editor, onAttrs } = useEditor()

const modelValue = defineModel('value', { default: '' })

const lockFlag = ref(false)

onMounted(async () => {
  if (props.closure)
    useBlockClosure()

  if (!editor.value) {
    nextTick(install)
  }
})

watch(() => props.id, () => {
  editor.value?.setData({ content: replaceImgSrc(modelValue.value) || '' })
})

watch(modelValue, async () => {
  if (!editor.value) {
    await nextTick(install)
  }
  if (editor.value) {
    onAttrs.change = () => {}
    if (!lockFlag.value)
      editor.value?.setData({ content: replaceImgSrc(modelValue.value) || '' })
    onAttrs.change = change
  }
}, { immediate: true })

function change() {
  console.log('%c [  ]-44', 'font-size:13px; background:pink; color:#bf2c9f;', 1)
  lockFlag.value = true
  modelValue.value = editor.value?.getData({ rootName: 'content' }) || ''
}

function useBlockClosure() {
  function onbeforeunload(e: { returnValue: string }) {
    const dialogText = 'close'
    e.returnValue = dialogText
    return dialogText
  }

  onUnmounted(() => {
    window.removeEventListener('beforeunload', onbeforeunload)
  })

  onMounted(async () => {
    window.addEventListener('beforeunload', onbeforeunload)
  })
}
</script>

<style lang="less" scoped>
#editor-container {
  flex: 1;
  background-color: #fff;
  border: 1px solid #e8e8e8;
  box-shadow: 0 2px 10px rgb(0 0 0 / 12%);
  border-radius: 4px;
  :deep(table) {
    min-width: 50px !important;
  }
}
</style>

<style lang="less">

</style>
