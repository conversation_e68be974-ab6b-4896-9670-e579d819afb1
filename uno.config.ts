import presetIcons from '@unocss/preset-icons'
import {
  defineConfig,
  presetAttributify,
  presetTypography,
  presetUno,
  presetWebFonts,
  transformerDirectives,
  transformerVariantGroup,
} from 'unocss'
import { getColors } from './src/theme/utils'

export default defineConfig({
  theme: {
    colors: getColors(),
  },
  rules: [
    [/^c-bg$/, () => ({ 'background-color': 'var(--ch2-color-bg-container)' })], // 组件背景色
    [/^bg$/, () => ({ 'background-color': 'var(--ch2-color-bg-container)' })], // 组件背景色
    [/^body-bg$/, () => ({ 'background-color': 'var(--color-ch2-bg-container)' })], // 块背景色
    [/^text-color$/, () => ({ color: 'var(--ch2-color-primary:)' })], // 主文字色
    [/^flex-(\d+)$/, ([, d]) => ({ flex: d })],
  ],
  shortcuts: [
    ['center', 'flex items-center justify-center'],
    ['un-btn', 'px-4 py-1 rounded inline-block bg-primary text-color cursor-pointer !outline-none hover:bg-primary-hover disabled:cursor-default disabled:bg-gray-1 disabled:opacity-50'],
    ['icon-btn', 'inline-block cursor-pointer select-none opacity-75 transition duration-200 ease-in-out hover:opacity-100 hover:text-primary-hover'],
    ['icon', 'inline-block vertical-sub'],
  ],
  presets: [
    presetUno(),
    presetAttributify(),
    presetIcons({
      scale: 1.2,
      warn: true,
      autoInstall: true,
    }),
    presetTypography(),
    presetWebFonts({
      fonts: {
        sans: 'DM Sans',
        serif: 'DM Serif Display',
        mono: 'DM Mono',
      },
    }),
  ],
  transformers: [
    transformerDirectives(),
    transformerVariantGroup(),
  ],
  safelist: 'prose m-auto text-left'.split(' '),
})
