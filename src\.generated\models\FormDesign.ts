import { FormStructure } from "./FormStructure";
import { FormData } from "./FormData";
import { FormState } from "./FormState";
import { Information } from "./Information";
/**在线表单*/
export class FormDesign {
  /**表单名称*/
  name?: string | null | undefined = null;
  /**描述*/
  des?: string | null | undefined = null;
  /**表单的KEY，可以作为查询依据*/
  key?: string | null | undefined = null;
  /**表单属性*/
  layout?: string | null | undefined = null;
  /**表单结构*/
  formStructure?: FormStructure[] | null | undefined = [];
  /**表单采集的数据*/
  formData?: FormData[] | null | undefined = [];
  createTime: Dayjs = dayjs();
  updateTime: Dayjs = dayjs();
  formState: FormState = 0;
  /**关联文章*/
  informationId?: GUID = null;
  /**关联文章*/
  information?: Information | null | undefined = null;
  /**软删除标记*/
  softDeleted: boolean = false;
  /**软删除者*/
  deletedBy?: string | null | undefined = null;
  /**软删除时间*/
  deleted?: Dayjs | null | undefined = null;
  /**创建时间*/
  created: Dayjs = dayjs();
  /**修改时间*/
  modified?: Dayjs | null | undefined = null;
  /**创建者*/
  createdBy?: string | null | undefined = null;
  /**修改者*/
  modifiedBy?: string | null | undefined = null;
  /**主键*/
  id: GUID = "00000000-0000-0000-0000-000000000000";
}
