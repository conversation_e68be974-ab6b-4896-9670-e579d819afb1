<!--
 * @Author: xinner
 * @Date: 2022-03-24 10:44:37
 * @LastEditors: 景 彡
 * @LastEditTime: 2024-12-26 10:21:13
 * @Description:
-->

<template>
  <div class="flex flex-wrap gap-4 p-20px c-bg">
    <div v-for="item, i in headerList" :key="i" class="min-w-250px flex flex-1 justify-between rounded-lg bg-#F2F5FA px-16px py-24px">
      <div class="h-50px w-50px center rounded-16px c-bg">
        <div :class="item.icon" class="text-32px c-primary" />
      </div>
      <div class="ml-16px">
        <p class="font-bold">
          <span>{{ item.text.title }}</span>
          <span class="ml-16px text-16px"> {{ item.text.value }} </span>个
        </p>
        <div class="flex justify-end gap2">
          <p v-for="p, j in item.text.subtitle" :key="j" class="mt-16px text-right text-12px">
            <span class="c-#666">{{ p.text }}</span>
            <span :class="{ '!c-#0AB389': Number(p.value) > 0 }" class="ml-8px c-#E33C64">{{ p.value }}</span>
          </p>
        </div>
      </div>
    </div>
  </div>

  <div class="grid grid-cols-[repeat(4,minmax(150px,1fr))] mt-24px gap-8 px-16px">
    <div v-for="item in routeLinks" :key="item.route" class="home-nav h-64px min-w-150px center flex-1 c-bg" @click="router.push(item.route)">
      <div :class="item.icon" class="text-24px c-primary" />
      <div class="ml-16px text-18px font-bold">{{ item.title }}</div>
    </div>
  </div>

  <div class="mt-24px p-16px c-bg">
    <div class="flex items-center justify-between">
      <div class="flex items-center">
        <div class="h-20px w-5px rounded-[0_4px_4px_0] bg-primary" />
        <div class="ml-8px text-16px font-bold">产品激活概况</div>
      </div>
      <a-space :size="12">
        <!-- <a-select
          v-model:value="year"
          style="width: 120px"
        >
          <a-select-option value="jack">2024</a-select-option>
          <a-select-option value="lucy">2023</a-select-option>
          <a-select-option value="disabled">2022</a-select-option>
          <a-select-option value="Yiminghe">2021</a-select-option>
        </a-select> -->
        <a-range-picker v-model:value="monthParams" :allow-clear="false" @change="updateActivation()" />
      </a-space>
    </div>

    <div ref="lineChartRef" class="h-260px w-100%" />
  </div>
</template>

<script lang="ts" setup>
import type { EChartsOption } from 'echarts'
import * as api from '@/api'
import { HomeStatistics } from '@/api/models'
import * as echarts from 'echarts'
import { useRouter } from 'vue-router'

definePage({
  meta: {
    title: '首页',
    layout: 'admin',
    local: true,
    icon: 'HomeOutlined',
    layoutRoute: {
      meta: {
        title: '首页',
        icon: 'HomeOutlined',
        order: 9999,
      },
    },
  },
})

const router = useRouter()

const userStore = useUserStore()

// const year = ref(2024)

const monthParams = ref<[Dayjs, Dayjs]>([dayjs().subtract(1, 'month').startOf('month'), dayjs().endOf('month')])

const [lineChartRef,, initECharts, updateECharts] = useECharts('lineChartRef')

let updateActivationTimer: any
/**
 * 初始化激活折线图
 */
function initActivation() {
  const option: EChartsOption = {
    // 你的代码
    title: {
      text: '',
      textStyle: {
        color: '#fff',
        fontSize: 12,
      },
    },
    grid: {
      top: 30,
      left: 30,
      right: 30,
      bottom: 30,
      containLabel: true,
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'line',
      },
      backgroundColor: 'rgba(9, 179, 136, 0.6)',
      borderColor: 'rgba(9, 179, 136, 1)',
      textStyle: {
        color: '#FFF',
      },
    },
    xAxis: {
      type: 'category',
      data: ['8:00', '9:00', '10:00', '11:00', '12:00', '13:00'],
      axisLine: {
        lineStyle: {
          color: 'rgba(60,132,163,0.4)', // x轴线颜色
        },
      },
      axisTick: {
        show: false, // 是否显示x轴的刻度
      },
      axisLabel: {
        interval: 0,
        color: '#000',
        fontSize: 12,
      },
      boundaryGap: false, // true折线图以x轴刻度为中心点  false折线图折线从头开始
    },
    yAxis: {
      type: 'value',
      axisTick: {
        show: false,
      },
      axisLine: {
        show: false,
      },
      axisLabel: {
        color: '#FFF',
        fontSize: 12,
        formatter(value) {
          return `${value}`
        },
      },
      splitLine: {
        show: true,
        lineStyle: {
          type: 'dashed',
          color: 'rgba(60,132,163,0.2)',
        },
      },
    },
    series: [],
  }

  initECharts(option)
  updateActivation()

  updateActivationTimer = setInterval(() => {
    updateActivation()
  }, 10000)
}

onUnmounted(() => {
  updateActivationTimer && clearTimeout(updateActivationTimer)
})

async function updateActivation() {
  const base = {
    name: '',
    type: 'line',
    smooth: true,
    areaStyle: {
      color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
        {
          offset: 0,
          color: 'rgba(9, 179, 136, 1)',
        },
        {
          offset: 1,
          color: 'rgba(9, 179, 136, 0)',
        },
      ]),
    },
    lineStyle: {
      width: 1,
      color: 'rgba(9, 179, 136, 1)',
    },
    // 设置节点样式
    showSymbol: true,
    symbol: 'circle', // 可以选择 circle, diamond, pin 等
    symbolSize: 6, // 节点大小
    itemStyle: {
      color: 'rgba(9, 179, 136, 1)', // 节点颜色
    },
    data: [15, 29, 32, 5, 14, 42],
  }

  const series: Array<typeof base> = []

  const data = await api.DataAnalysis.ProductActivationStatistics_GetAsync({ start: monthParams.value?.[0]?.toDate(), end: monthParams.value?.[1]?.toDate() })

  const xAxisData = data.map(v => dayjs(v.activationTime).format('YYYY-MM-DD'))

  const typeMap = new Map<string, string>()
  data.forEach((v) => {
    v.keyValues?.forEach((v) => {
      if (!typeMap.has(v.typeId)) {
        typeMap.set(v.typeId, v.name || '未定义分类')
      }
    })
  })

  Array.from(typeMap.keys()).forEach((typeId) => {
    const obj = {
      ...base,
      name: typeMap.get(typeId)!,
      data: data.map(item => item.keyValues?.find(v => v.typeId === typeId)?.number || 0),
    }
    series.push(obj)
  })

  updateECharts({ series, xAxis: { data: xAxisData } })
}

const routeNames = computed(() => userStore.active.roles.flatMap(v => v.menu))

const routeLinks = computed(() => [
  {
    title: '用户管理',
    icon: 'i-mingcute-user-setting-line',
    route: '/system-manage/user/',
  },
  {
    title: '订单管理',
    icon: 'i-material-symbols-list-alt-outline-sharp',
    route: '/order-management/',
  },
  {
    title: '兑换商品管理',
    icon: 'i-mdi-gift-outline',
    route: '/points-mall/',
  },
  {
    title: '二维码生产管理',
    icon: 'i-mdi-qrcode-edit',
    route: '/qr-code-configuration/',
  },
  {
    title: '漆工管理',
    icon: 'i-ci-users-group',
    route: '/lacquer-worker-management/',
  },
  {
    title: '产品管理',
    icon: 'i-carbon-carbon-for-ibm-product',
    route: '/product-manager',
  },
  {
    title: '经销商管理',
    icon: 'i-gis-location-poi-o',
    route: '/dealer/',
  },
  {
    title: '抽奖活动管理',
    icon: 'i-game-icons-fallout-shelter',
    route: '/lucky-draw/',
  },
  {
    title: '积分获取记录',
    icon: 'i-lsicon-circle-star-outline',
    route: '/points-red-envelope/',
  },
  {
    title: '红包提现记录',
    icon: 'i-f7-money-yen-circle',
    route: '/points-red-envelope/',
  },
  {
    title: '统计报表',
    icon: 'i-token-xdata',
    route: '/statistics/',
  },
  {
    title: '推广返佣配置',
    icon: 'i-prime-share-alt',
    route: '/referrals-management/configure',
  },
].filter(v => routeNames.value.includes(v.route)))

const statistics = ref(new HomeStatistics())

const headerList = computed(() => [
  {
    icon: 'i-material-symbols-list-alt-outline-sharp',
    text: {
      title: '今日新增兑换订单',
      value: statistics.value.pointsOrderToday,
      subtitle: [
        {
          text: '总兑换订单总数',
          value: statistics.value.pointsOrderTotal,
        },
      ],
    },
  },
  {
    icon: 'i-ci-users-group',
    text: {
      title: '今日新增漆工',
      value: statistics.value.clientUserToday,
      subtitle: [
        {
          text: '总漆工人数',
          value: statistics.value.clientUserTotal,
        },
      ],
    },
  },
  {
    icon: 'i-f7-money-yen-circle',
    text: {
      title: '今日提现红包',
      value: statistics.value.cashbackRecordTotay,
      subtitle: [
        {
          text: '上月',
          value: statistics.value.cashbackRecordLastMonth,
        },
        {
          text: '上周',
          value: statistics.value.cashbackRecordLastWeek,
        },
      ],
    },
  },
  {
    icon: 'i-tabler-qrcode',
    text: {
      title: '今日二维码生产',
      value: statistics.value.productBatchToday,
      subtitle: [
        {
          text: '上月',
          value: statistics.value.productBatchLastMonth,
        },
        {
          text: '上周',
          value: statistics.value.productBatchLastWeek,
        },
      ],
    },
  },
  {
    icon: 'i-icon-park-outline-ad-product',
    text: {
      title: '今日激活卡片',
      value: statistics.value.productActivationToday,
      subtitle: [
        {
          text: '总兑激活卡片',
          value: statistics.value.productActivationTotal,
        },
      ],
    },
  },
])

async function getData() {
  statistics.value = await api.DataAnalysis.HomeStatistics_GetAsync()
}

onMounted(() => {
  getData()
  nextTick(() => {
    initActivation()
  })
})
</script>

<style scoped lang="less">
.home-nav {
  cursor: pointer;
  &:hover {
    box-shadow: 0px 2px 3px rgba(0, 0, 0, 0.25);
  }
}
</style>
