import { FormItem } from "./FormItem";
import { FormDesign } from "./FormDesign";
import { FormDataState } from "./FormDataState";
export class FormData {
  ip?: string | null | undefined = null;
  /**浏览器指纹*/
  fingerprint?: string | null | undefined = null;
  /**存储的数据*/
  formItem?: FormItem[] | null | undefined = [];
  /**在线表单*/
  formDesign?: FormDesign | null | undefined = null;
  formDesignId?: GUID = null;
  createTime: Dayjs = dayjs();
  formDataState: FormDataState = 0;
  operationTime: Dayjs = dayjs();
  /**软删除标记*/
  softDeleted: boolean = false;
  /**软删除者*/
  deletedBy?: string | null | undefined = null;
  /**软删除时间*/
  deleted?: Dayjs | null | undefined = null;
  /**创建时间*/
  created: Dayjs = dayjs();
  /**修改时间*/
  modified?: Dayjs | null | undefined = null;
  /**创建者*/
  createdBy?: string | null | undefined = null;
  /**修改者*/
  modifiedBy?: string | null | undefined = null;
  /**主键*/
  id: GUID = "00000000-0000-0000-0000-000000000000";
}
