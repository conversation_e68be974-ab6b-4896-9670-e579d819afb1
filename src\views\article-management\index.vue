<!--
 * @Description:
 * @Author: 景 彡
 * @Date: 2024-11-07 09:00:59
 * @LastEditors: 景 彡
-->
<template>
  <div class="p16px bg">
    <div class="header mb16px">
      <c-form layout="inline">
        <a-row :gutter="[16, 16]">
          <a-col :span="6">
            <c-form-item label="标题">
              <a-input
                v-model:value="searchForm.title"
                placeholder="请输入标题名称"
                allow-clear
              />
            </c-form-item>
          </a-col>
          <a-col :span="6">
            <c-form-item label="内容">
              <a-input
                v-model:value="searchForm.content"
                placeholder="请输入文章内容"
                allow-clear
              />
            </c-form-item>
          </a-col>
          <a-col :span="6">
            <c-form-item label="作者">
              <a-input
                v-model:value="searchForm.author"
                placeholder="请输入作者姓名"
                allow-clear
              />
            </c-form-item>
          </a-col>
          <a-col :span="6">
            <c-form-item label="分类">
              <a-tree-select
                v-model:value="categoryIds"
                tree-node-filter-prop="name"
                :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                placeholder="请选择"
                allow-clear show-search multiple
                :tree-data="categoryTreeData"
                :field-names="{ children: 'children', label: 'name', value: 'id' }"
                show-checked-strategy="SHOW_ALL"
              />
            </c-form-item>
          </a-col>
          <a-col :span="6">
            <c-form-item label="标签">
              <a-input
                v-model:value="searchForm.tag"
              />
            </c-form-item>
          </a-col>
          <a-col :span="6">
            <c-form-item label="置顶">
              <c-boolean-select v-model:value="searchForm.isTop" placeholder="是否置顶" allow-clear />
            </c-form-item>
          </a-col>
          <a-col :span="6">
            <c-form-item label="时间">
              <a-range-picker v-model:value="spanTime" class="w-202px" allow-clear @change="onChangeTime" />
            </c-form-item>
          </a-col>
          <a-col :span="6">
            <div class="flex justify-end">
              <a-button type="primary" class="search-btn" @click="listRef.fetchData()">
                <c-icon-search-outlined />搜索
              </a-button>
              <a-button class="search-btn" @click="reSetForm()">
                <c-icon-sync-outlined />重置
              </a-button>
            </div>
          </a-col>
        </a-row>
      </c-form>

      <a-row justify="space-between" class="header-button">
        <a-col>
          <a-radio-group v-model:value="searchForm.status" button-style="solid" @change="listRef.fetchData()">
            <a-radio-button :value="undefined">
              全部
            </a-radio-button>
            <a-radio-button
              v-for="status in enumToObject(models.InformationStatus)"
              :key="status.value"
              :value="status.value"
            >
              {{ status.label }}
            </a-radio-button>
          </a-radio-group>
        </a-col>

        <a-col>
          <a-button type="primary" @click="onEdit(null)">
            <c-icon-plus-outlined />新增文章
          </a-button>
          <!-- <a-divider type="vertical" /> -->
          <!-- <ImportMpWeChat :fetch-data="() => listRef.fetchData()" /> -->
        </a-col>
      </a-row>
      <a-divider style="margin:20px 0px 0px" />
    </div>
    <c-list ref="listRef" :api="api.Informations.GetInformation_PostAsync" :get-params="searchForm" :post-params="categoryIds" immediate pagination>
      <template #renderItem="{ item }">
        <a-list-item key="item.id">
          <a-list-item-meta>
            <template #title>
              <h3>
                <a href="#" class="list-color" @click="onEdit(item)">{{ item.title }}</a>
              </h3>
            </template>
            <template #description>
              <p class="list-color">
                {{ item.description }}
              </p>
              <p>
                <a-tag color="cyan">
                  {{ models.InformationStatus[item.status] }}
                </a-tag>
                <a-tag v-if="item.informationCategory" color="processing">
                  {{ item.informationCategory.map(v => v.name).join(",") }}
                </a-tag>
                <template v-if="item.tags">
                  <a-tag v-for="t in item.tags.split(',')" :key="t" :title=" `标签：${t}`" color="#ff784e">
                    {{ t }}
                  </a-tag>
                </template>
                <a-tag v-if="item.msgid" color="#19AF19">
                  <c-icon-wechat-outlined @click="preview(item)" /> 微信转载
                </a-tag>

                <a-tag v-if="item.isTop" color="orange">
                  <c-icon-fire-outlined />
                  已置顶
                </a-tag>
              </p>
              <p>
                <span>{{
                  dayjs(item.updatedTime).format("YYYY-MM-DD HH:mm:ss")
                }}</span>
                <a-divider type="vertical" />
                <span>{{ item.views }} 阅读</span>
                <a-divider type="vertical" />
                <a @click="preview(item)">预览</a>
                <template v-if="item.formDesignId">
                  <a-divider type="vertical" />
                  <a-tag
                    class="cursor-pointer" color="orange" title="前往表单管理查看" @click="() => formDataViewRef?.lookDataById(item.formDesignId)"
                  >
                    表单收集
                  </a-tag>
                </template>
              </p>
            </template>
          </a-list-item-meta>

          <ImageView v-if=" item.cover" :src="(item.cover)" width="200px" height="140px" />

          <template #actions>
            <a-dropdown>
              <a class="ant-dropdown-link" @click.prevent>
                <b>. . .</b>
              </a>
              <template #overlay>
                <a-menu>
                  <a-menu-item>
                    <a href="javascript:;" @click="setTop(item)">{{ item.isTop ? '取消置顶' : '置顶文章' }}</a>
                  </a-menu-item>

                  <a-menu-item v-if="item.status === models.InformationStatus.草稿">
                    <a href="javascript:;" @click="onPublishArticle(item, models.InformationStatus.已发布)">发布文章</a>
                  </a-menu-item>
                  <a-menu-item v-if="[models.InformationStatus.已归档, models.InformationStatus.草稿].includes(item.status)">
                    <a href="javascript:;" @click="onEdit(item)">编辑文章</a>
                  </a-menu-item>
                  <a-menu-item v-if="item.status === models.InformationStatus.草稿">
                    <a href="javascript:;" @click="onSetStatus(item, models.InformationStatus.已归档)">归档文章</a>
                  </a-menu-item>
                  <a-menu-item v-if="item.status === models.InformationStatus.已发布">
                    <a href="javascript:;" @click="onSetStatus(item, models.InformationStatus.草稿)">取消发布</a>
                  </a-menu-item>
                  <a-menu-item v-if="item.status !== models.InformationStatus.已删除">
                    <a href="javascript:;" @click="onDelete(item)">删除文章</a>
                  </a-menu-item>
                  <a-menu-item v-if="item.status === models.InformationStatus.已删除">
                    <a href="javascript:;" @click="onCompletelyDelete(item)">彻底删除</a>
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </template>
        </a-list-item>
      </template>
    </c-list>
    <FormDataView ref="formDataViewRef" />
  </div>
</template>

<script setup lang="ts">
import * as api from '@/api'
import * as models from '@/api/models'
import { useAppStore } from '@/stores'
import { enumToObject } from '@/utils/enum'
import { DatePicker, Form, message, Modal } from 'ant-design-vue'
import dayjs from 'dayjs'
import { h, onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import FormDataView from './components/FormDataView.vue'

definePage({
  meta: {
    title: '文章管理',
    layoutRoute: {
      meta: {
        layout: 'admin',
        title: '官网管理',
        local: true,
        icon: 'ReadOutlined',
        order: 4,
      },
    },
  },
})

const appStore = useAppStore()

const formDataViewRef = useTemplateRef('formDataViewRef')

class ListQuery {
  title?: string
  content?: string
  categoryId?: number
  isTop?: boolean
  author?: string
  startTime?: Date
  endTime?: Date
  status?: models.InformationStatus
  sortBy: models.InformationSortBy = models.InformationSortBy.UpdatedTime
  tag?: string
}

type RangeValue = [dayjs.Dayjs, dayjs.Dayjs]

const categoryIds = ref<string[]>([])

const searchForm = ref<ListQuery>(new ListQuery())

const spanTime = ref<RangeValue>()

const listRef = ref() // 编辑资讯列表ref

const router = useRouter()

function onChangeTime(date: any) {
  if (date) {
    searchForm.value.startTime = date[0].$d
    searchForm.value.endTime = date[1].$d
  }
}

async function onSetStatus(record: models.Information, status: models.InformationStatus, updateTime?: Date) {
  try {
    await api.Informations.SetStatusByInformationId_PostAsync({ informationId: record.informationId, status, updateTime })
    message.success('设置成功')
    listRef.value.refreshData()
  }
  catch (error: any) {
    message.error(error.message)
  }
}

function onPublishArticle(record: models.Information, status: models.InformationStatus) {
  let time = new Date()
  Modal.confirm({
    title: '请选择发布时间',
    content: h(DatePicker, {
      showTime: true,
      placeholder: '请选择发布时间',
      onChange(date: any) {
        time = date.toDate()
      },
    }),
    onOk: async () => {
      await onSetStatus(record, status, time)
    },
  })
}

// 操作资讯信息
async function onEdit(record: models.Information | null) {
  const routeData = router.resolve({
    path: '/article-management/editor-new',
    query: { informationId: record?.informationId },
  })
  window.open(routeData.href, '_blank')
}

async function onDelete(record: models.Information) {
  try {
    await api.Informations.DeleteByInformationId_PostAsync({ informationId: record.informationId })
    message.success('删除成功')
    listRef.value.refreshData()
  }
  catch (error: any) {
    message.error(error.message)
  }
}

async function onCompletelyDelete(record: models.Information) {
  try {
    await api.Informations.RemoveCompletelyByInformationId_PostAsync({ informationId: record.informationId })
    message.success('彻底回收站文章成功')
    listRef.value.refreshData()
  }
  catch (error: any) {
    message.error(error.message)
  }
}

const { resetFields } = Form.useForm(searchForm)

function reSetForm() {
  resetFields()
  spanTime.value = undefined
  listRef.value.fetchData()
}

function preview(item: models.Information) {
  if (item.url)
    window.open(item.url)
  else
    window.open(`${appStore.setting.siteUrl}/news-and-information/detail/${item.informationId}`)
}

async function setTop(item: models.Information) {
  await api.Informations.SetInformationTop_PostAsync({
    informationId: item.informationId,
    top: !item.isTop,
  })
  message.success('设置成功')
  item.isTop = !item.isTop
}

const { categoryTreeData } = useCategoryTreeData()

function useCategoryTreeData() {
  const categoryTreeData = ref<models.InformationCategoryView[]>([])

  const getData = async () => {
    categoryTreeData.value = await api.InformationCategories.GeCategoryPaged_GetAsync(null)
  }

  const getTypePath = (tree: models.InformationCategoryView[], targetId: number, parents: string[] = []) => {
    for (const node of tree) {
      const currentPath = [...parents, node.name!] // 包含当前节点
      if (node.id === targetId)
        return currentPath // 找到目标节点，返回路径

      if (node.children) {
        const result = getTypePath(node.children, targetId, currentPath) as string[]
        if (result)
          return result // 返回找到的路径
      }
    }
    return null // 如果未找到
  }

  onMounted(getData)

  return { categoryTreeData, getTypePath: (id: number) => getTypePath(categoryTreeData.value, id)?.join('/') }
}
</script>

<style scoped lang="less">
.header {
  .search-btn {
    margin-right: 16px;
  }
  .header-button {
    margin-top: 16px;
  }
}
:deep(.ant-row) {
  width: 100%;
}
</style>
