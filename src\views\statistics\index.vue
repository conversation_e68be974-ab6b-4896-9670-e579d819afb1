<!--
 * @Description:
 * @Author: 景 彡
 * @Date: 2024-11-08 11:39:09
 * @LastEditors: 景 彡
-->
<template>
  <div class="h-76px flex items-center p-16px c-bg">
    <span class="inline-block w-100px">选择时间范围</span>
    <c-range-picker v-model:start-time="params.start!" v-model:end-time="params.end!" style="width: 100%" />
  </div>
  <div class="mt-16px p-16px">
    <div class="grid grid-cols-[repeat(4,minmax(150px,1fr))] mt-24px gap-8 px-16px">
      <div class="home-nav h-64px min-w-150px center flex-1 cursor-pointer shadow-[0_2px_3px_rgba(0,0,0,0.25)] c-bg hover:shadow-[rgba(0,0,0,0.4)_0px_2px_4px,rgba(0,0,0,0.3)_0px_7px_13px_-3px,rgba(0,0,0,0.2)_0px_-3px_0px_inset]">
        <div class="i-material-symbols-list-alt-outline-sharp text-24px c-primary" />
        <div class="ml-16px text-18px font-bold">兑换订单报表</div>
      </div>
      <div class="home-nav h-64px min-w-150px center flex-1 cursor-pointer shadow-[0_2px_3px_rgba(0,0,0,0.25)] c-bg hover:shadow-[rgba(0,0,0,0.4)_0px_2px_4px,rgba(0,0,0,0.3)_0px_7px_13px_-3px,rgba(0,0,0,0.2)_0px_-3px_0px_inset]">
        <div class="i-mdi-gift-outline text-24px c-primary" />
        <div class="ml-16px text-18px font-bold">推广情况报表</div>
      </div>
      <div class="home-nav h-64px min-w-150px center flex-1 cursor-pointer shadow-[0_2px_3px_rgba(0,0,0,0.25)] c-bg hover:shadow-[rgba(0,0,0,0.4)_0px_2px_4px,rgba(0,0,0,0.3)_0px_7px_13px_-3px,rgba(0,0,0,0.2)_0px_-3px_0px_inset]">
        <div class="i-mdi-qrcode-edit text-24px c-primary" />
        <div class="ml-16px text-18px font-bold">二维码生产报表</div>
      </div>

      <div class="home-nav h-64px min-w-150px center flex-1 cursor-pointer shadow-[0_2px_3px_rgba(0,0,0,0.25)] c-bg hover:shadow-[rgba(0,0,0,0.4)_0px_2px_4px,rgba(0,0,0,0.3)_0px_7px_13px_-3px,rgba(0,0,0,0.2)_0px_-3px_0px_inset]">
        <div class="i-carbon-carbon-for-ibm-product text-24px c-primary" />
        <div class="ml-16px text-18px font-bold">产品激活报表</div>
      </div>

      <div class="home-nav h-64px min-w-150px center flex-1 cursor-pointer shadow-[0_2px_3px_rgba(0,0,0,0.25)] c-bg hover:shadow-[rgba(0,0,0,0.4)_0px_2px_4px,rgba(0,0,0,0.3)_0px_7px_13px_-3px,rgba(0,0,0,0.2)_0px_-3px_0px_inset]">
        <div class="i-lsicon-circle-star-outline text-24px c-primary" />
        <div class="ml-16px text-18px font-bold">积分报表</div>
      </div>
      <div class="home-nav h-64px min-w-150px center flex-1 cursor-pointer shadow-[0_2px_3px_rgba(0,0,0,0.25)] c-bg hover:shadow-[rgba(0,0,0,0.4)_0px_2px_4px,rgba(0,0,0,0.3)_0px_7px_13px_-3px,rgba(0,0,0,0.2)_0px_-3px_0px_inset]">
        <div class="i-f7-money-yen-circle text-24px c-primary" />
        <div class="ml-16px text-18px font-bold">现金红包报表</div>
      </div>
    </div>
  </div>
</template>

<script lang='ts' setup>
definePage({
  meta: {
    title: '统计报表',
    layoutRoute: {
      meta: { layout: 'admin', title: '统计报表', local: true, icon: 'BarChartOutlined', order: 3 },

    },
  },
})

const params = ref({ start: null, end: null })
</script>

<style scoped>

</style>
