import { RecordSource } from "./RecordSource";
export class CashbackRecordPageView {
  /**增量*/
  number: number = 0;
  /**时间*/
  time: Dayjs = dayjs();
  /**简短描述*/
  shortDescription?: string | null | undefined = null;
  /**描述*/
  description?: string | null | undefined = null;
  clientUserId: GUID = "00000000-0000-0000-0000-000000000000";
  /**漆工昵称*/
  userName?: string | null | undefined = null;
  /**电话名称*/
  userPhoneNumber?: string | null | undefined = null;
  /**来源*/
  source: RecordSource = 0;
  /**来源ID
产品扫码 => 产品ID
推广返佣 => 推广活动ID
抽奖 => 奖品ID
人工发放 => 发放人ID*/
  sourceId?: GUID = null;
  /**防伪ID*/
  productSecurityCodeId?: GUID = null;
  /**产品活动ID*/
  productActivitiesId?: GUID = null;
  /**经度*/
  latitude: number = 0;
  /**纬度*/
  longitude: number = 0;
  /**主键*/
  id: GUID = "00000000-0000-0000-0000-000000000000";
}
