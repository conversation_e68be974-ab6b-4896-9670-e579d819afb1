<!--
 * @Author: 龙兆柒
 * @Date: 2024-08-15 14:57:46
 * @LastEditors: 龙兆柒
 * @LastEditTime: 2024-08-28 17:23:44
 * @FilePath: \gxedu-doubleminus-vue\src\layouts\layout\components\LayoutContainer.vue
 * @Description:
-->
<template>
  <a-layout class="layout-content">
    <slot />
    <a-layout-content :class="{ maximize: globalStore.themeConfig.maximize }" class="layout-content-in">
      <a-breadcrumb v-if="globalStore.themeConfig.breadcrumb" :routes="breadcrumbRoutes" style="margin-bottom: 5px">
        <template #itemRender="{ route: breadcrumbRoute, paths }:any">
          <span v-if="breadcrumbRoutes.indexOf(breadcrumbRoute) === breadcrumbRoutes.length - 1">
            {{ breadcrumbRoute.meta?.title }}
          </span>
          <router-link v-else :to="`/${paths.join('/')}`">
            {{ breadcrumbRoute.meta?.title }}
          </router-link>
        </template>
      </a-breadcrumb>
      <MainSize />
      <Loading :spinning="spinning">
        <RouterView v-slot="{ Component, route: breadcrumbRoute }">
          <keep-alive :include="cacheIncludes">
            <component :is="getComponent(Component, breadcrumbRoute)" v-if="showPage" :key="getComponentName(breadcrumbRoute.fullPath)" />
          </keep-alive>
        </RouterView>
      </Loading>
    </a-layout-content>
    <a-layout-footer
      v-if="globalStore.themeConfig.footer" class="text-center"
      style="padding: 0px; margin-bottom: 10px;"
    >
      {{ title }} {{ copyright }}
    </a-layout-footer>
  </a-layout>
</template>

<script setup lang='ts'>
import { useAppStore } from '@/stores'
import { getComponentName } from '@/utils/util'
import { ref, watchEffect } from 'vue'
import { useRoute } from 'vue-router'
import MainSize from '../components/Main/components/Maximize.vue'
import { useKeepAliveAndRefreshPage } from '../hooks/useKeepAliveAndRefreshPage'

const globalStore = useAppStore()

const copyright = import.meta.env.VITE_APP_COPYRIGHT

const title = import.meta.env.VITE_APP_TITLE

const breadcrumbRoutes = ref()

const route = useRoute()

watchEffect(() => {
  breadcrumbRoutes.value = Object.values(route.matched.reduce((a, b) => ((a[b.path] = b), a), {} as any))
})

const { cacheIncludes, getComponent, showPage } = useKeepAliveAndRefreshPage()

const spinning = computed(() => {
  return !showPage.value || globalStore.routerLoading
})
</script>

<style lang="less" scoped>
.layout-content {
  transition: all 0.4s ease;
  height: auto;

  .layout-content-in {
    padding: calc(@size * 1px);
    overflow: initial;
    display: flex;
    flex-direction: column;
  }

  .maximize {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    background: @colorBgLayout;
    z-index: 101;
  }
}
</style>
