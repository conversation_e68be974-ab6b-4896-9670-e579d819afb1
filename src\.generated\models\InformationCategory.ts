import { CategoryType } from "./CategoryType";
/**文章分类*/
export class InformationCategory {
  /**分类名称*/
  name?: string | null | undefined = null;
  /**说明*/
  description?: string | null | undefined = null;
  /**父级id*/
  parentId: GUID = "00000000-0000-0000-0000-000000000000";
  /**是否在菜单中显示*/
  menuShow: boolean = false;
  /**是否处于菜单中最上层*/
  menuTop: boolean = false;
  /**创建时间*/
  creationDate: Dayjs = dayjs();
  /**分类类型*/
  categoryType: CategoryType = 0;
  /**软删除标记*/
  softDeleted: boolean = false;
  /**软删除者*/
  deletedBy?: string | null | undefined = null;
  /**软删除时间*/
  deleted?: Dayjs | null | undefined = null;
  /**创建时间*/
  created: Dayjs = dayjs();
  /**修改时间*/
  modified?: Dayjs | null | undefined = null;
  /**创建者*/
  createdBy?: string | null | undefined = null;
  /**修改者*/
  modifiedBy?: string | null | undefined = null;
  /**主键*/
  id: GUID = "00000000-0000-0000-0000-000000000000";
}
