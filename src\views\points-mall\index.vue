<!--
 * @Description:
 * @Author: 景 彡
 * @Date: 2024-10-30 17:13:34
 * @LastEditors: 景 彡
-->
<template>
  <div class="h-full">
    <c-pro-table
      ref="proTableRef"
      v-model:search-form="searchForm"
      :columns="columns"
      :api="api.PointsMalls.GetCommodityPage_PostAsync"
      :search-fields="searchFields"
      row-key="id"
      :row-selection="rowSelection"
      align="center"
      immediate serial-number operation :operation-config="{ fixed: 'right' }"
      :scroll="{ x: 1440 }"
    >
      <template #header>
        <a-radio-group v-model:value="searchForm.pointsTransactionQuery" @change="radioChange">
          <a-radio-button :value="null">全部</a-radio-button>
          <a-radio-button
            v-for="status in enumToObject(PointsTransactionQuery)"
            :key="status.value"
            :value="status.value"
          >
            {{ status.label }}
          </a-radio-button>
        </a-radio-group>
      </template>
      <template #toolButton>
        <a-button type="primary" @click="onEdit()">添加商品</a-button>
        <a-button type="primary" @click="putOnShelves">批量上架</a-button>
        <a-button tdanger @click="removeFromShelves">批量下架</a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <ImageView
          v-if="column.dataIndex === 'image' && record.image"
          style="width: 50px; height: 50px; object-fit: cover"
          :src="(record.image)"
        />
        <span v-if="column.dataIndex === 'shelfTime'" class="text-12px c-text-secondary"> {{ dateTime(record.shelfTime) }} ~ {{ dateTime(record.removeTime) }}</span>
        <a-tag v-if="column.dataIndex === 'isRecommend'" title="点击修改推荐状态" :color="record.isRecommend ? 'blue' : 'default'" class="cursor-pointer" @click="onSetRecommend(record)"> {{ record.isRecommend ? '已推荐' : '未推荐' }} </a-tag>
        <span v-if="column.dataIndex === 'status'">
          <a-tag v-if="record.status === PointsCommodityStatus.未上架" color="default">{{ PointsCommodityStatus[record.status] }}</a-tag>
          <a-tag v-else-if="record.status === PointsCommodityStatus.可兑换" color="success">{{ PointsCommodityStatus[record.status] }}</a-tag>
          <a-tag v-else color="warning">{{ PointsCommodityStatus[record.status] }}</a-tag>
        </span>
      </template>
      <template #operation="{ record }">
        <a-dropdown>
          <a class="ant-dropdown-link" @click.prevent>
            更多操作
            <c-icon-down-outlined />
          </a>
          <template #overlay>
            <a-menu>
              <a-menu-item>
                <a @click="onEdit(record, true)"> 查看 </a>
              </a-menu-item>
              <a-menu-divider />
              <a-menu-item>
                <a @click="onEdit(record)"> 编辑 </a>
              </a-menu-item>
              <a-menu-item>
                <a-popconfirm title="是否确认删除当前商品" @confirm="onDel(record)">
                  <a class="!c-error"> 删除商品 </a>
                </a-popconfirm>
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
        <!-- <a-button type="link" @click="onEdit(record)"> 编辑 </a-button> -->
      </template>
    </c-pro-table>

    <a-drawer
      v-model:open="modalState"
      title="添加/编辑商品信息"
      width="80vw"

      destroy-on-close :mask-closable="false"
      placement="right"
    >
      <div class="flex">
        <div class="w-500px">
          <c-pro-form
            ref="proFormRef"
            v-model:value="model"
            :read-only="readonly"
            :label-col="{ span: 2 }"
            :fields="fields"
            colon
            :descriptions="{ column: 1, bordered: true, labelStyle: { width: '140px' } }"
          >
            <template #image>
              <div v-if="model.image" class="coverBox size-80px overflow-hidden">
                <ImageView
                  :src="(model.image)"
                  alt="avatar"
                  :preview="true"
                  :del-ico="true"
                  style="height: 80px; width: 80px; object-fit: cover"
                  @del-image="() => (model.image = '')"
                />
              </div>
              <a-button
                v-else
                type="dashed"
                block
                class="size-25"
                @click="avatarUpload"
              >
                <template #icon>
                  <c-icon-plus-outlined />
                </template>
                上传
              </a-button>
            </template>>
          </c-pro-form>
        </div>

        <div :calss="{ py16px: readonly }" class="ml8px w0 flex-1 rounded-4px shadow">
          <div class="p-16px">编辑商品详情</div>
          <Editor v-model:value="model.details!" :read-only="readonly" />
        </div>
      </div>

      <template #extra>
        <a-button style="margin-right: 8px" @click="onClose">取消</a-button>
        <a-button type="primary" @click="save()">保存</a-button>
      </template>
    </a-drawer>
  </div>
</template>

<script lang="ts" setup>
import type { PointsCommodityPageView } from '@/api/models'
import type { _SearchField } from 'ch2-components/lib/pro-table/types'
import type { FormField } from 'ch2-components/types/pro-form/types'
import type { ColumnProps } from 'ch2-components/types/pro-table/types'
import * as api from '@/api/'

import {
  FileType,
  InitialMode,
  PointsCommodity,
  PointsCommodityStatus,
  PointsTransactionQuery,
} from '@/api/models'
import { message } from 'ant-design-vue'

definePage({
  meta: {
    title: '兑换商品管理',
    layoutRoute: {
      meta: {
        layout: 'admin',
        title: '兑换商品管理',
        local: true,
        icon: 'ShopOutlined',
        order: 10,
      },
    },
  },
})

const proTableRef = useTemplateRef('proTableRef')

const searchForm = ref({
  keyWord: '',
  pointsTransactionQuery: null,
})

const searchFields = ref<_SearchField[]>([
  {
    prop: 'pointsTransactionQuery',
    method: 'GET',
  },
])

const columns = reactive<ColumnProps<PointsCommodityPageView>[]>([
  { dataIndex: 'title', title: '商品标题', align: 'left' },
  { dataIndex: 'redemptionCount', title: '兑换人数' },
  { dataIndex: 'stock', title: '库存' },
  { dataIndex: 'stockAlertThreshold', title: '库存警戒' },
  {
    dataIndex: 'keyWord',
    title: '关键词',
    isShow: false,
    search: { el: 'input', method: 'GET', emptyValue: '' },
    align: 'left',
  },
  { dataIndex: 'image', title: '图片' },
  { dataIndex: 'points', title: '积分' },
  { dataIndex: 'specs', title: '规格' },

  { dataIndex: 'isRecommend', title: '是否推荐', fixed: 'right' },
  { dataIndex: 'status', title: '商品状态', fixed: 'right' },
  { dataIndex: 'immediateMode', title: '处理模式', enum: InitialMode, fixed: 'right' },
  { dataIndex: 'shelfTime', title: '上下架时间', fixed: 'right', width: 200 },
])

const rowSelection = ref({
  selectedRowKeys: [],
  onChange: (selectedRowKeys: any) => {
    rowSelection.value.selectedRowKeys = selectedRowKeys
  },
})

async function putOnShelves() {
  await api.PointsMalls.BulkPublish_PostAsync(rowSelection.value.selectedRowKeys)
  rowSelection.value.selectedRowKeys = []
  proTableRef.value?.refresh()
  message.success('上架成功')
}

async function removeFromShelves() {
  await api.PointsMalls.BulkUnpublish_PostAsync(rowSelection.value.selectedRowKeys)
  rowSelection.value.selectedRowKeys = []
  proTableRef.value?.refresh()
  message.success('下架成功')
}

const { modalState, fields, save, model, readonly, onClose, onEdit, onSetRecommend, onDel }
  = useEditModal()

function avatarUpload() {
  useFileMangerModal(
    (files) => {
      model.value.image = files[0]?.id
    },
    { multiple: false, immediateReturn: true, menu: [FileType.图片] },
  )
}

function useEditModal() {
  const modalState = ref(false)

  const proFormRef = useTemplateRef('proFormRef')

  const model = ref(new PointsCommodity())

  const readonly = ref(true)

  const fields = computed<FormField[]>(() => [
    { label: '排序', el: 'input-number', prop: 'order', attrs: {} },
    {
      label: '商品标题',
      el: 'input',
      prop: 'title',
      attrs: { placeholder: '请输入产品名称' },
      formItem: {},
      required: true,
    },
    {
      label: '描述',
      el: 'input',
      prop: 'description',
      attrs: { placeholder: '请输入描述' },
      formItem: {},
      required: true,
    },
    { label: '产品规格', el: 'input', prop: 'specs', required: true, attrs: { placeholder: '请输入产品规格' }, formItem: {} },
    {
      label: '图片(1:1)',
      el: 'input',
      prop: 'image',
      attrs: { placeholder: '请输入图片（Path' },
      formItem: {},
      required: true,
    },
    {
      label: '积分价格',
      el: 'input-number',
      prop: 'points',
      attrs: { placeholder: '请输入积分价格', style: { width: '100%' } },
      formItem: {},
      required: true,
    },
    {
      label: '库存',
      el: 'input-number',
      prop: 'stock',
      attrs: { placeholder: '请输入库存', style: { width: '100%' } },
      formItem: {},
      required: true,
    },
    {
      label: '库存警戒值',
      el: 'input-number',
      prop: 'stockAlertThreshold',
      attrs: { placeholder: '请输入库存警戒值', style: { width: '100%' } },
      formItem: {},
      required: true,
    },
    {
      label: '是否推荐',
      el: 'boolean-select',
      prop: 'isRecommend',
      attrs: { placeholder: '请选择是否推荐', style: { width: '100%' } },
      formItem: {},
      required: true,
    },
    {
      label: '商品状态',
      el: 'enum-select',
      prop: 'status',
      isShow: readonly.value,
      formItem: {},
      required: true,
      attrs: { enum: PointsCommodityStatus },
    },
    {
      label: '处理模式',
      el: 'enum-select',
      prop: 'immediateMode',
      attrs: { 'placeholder': '请选择处理模式', 'enum': InitialMode, 'onUpdate:value': () => {
        if (model.value.immediateMode === InitialMode.定时上架) {
          model.value.shelfTime = dayjs()
          model.value.removeTime = dayjs()
        }
        else {
          model.value.shelfTime = undefined
          model.value.removeTime = undefined
        }
      } },
      formItem: {},
      required: true,
    },
    {
      label: '上架时间',
      el: 'date-picker',
      prop: 'shelfTime',
      attrs: { placeholder: '请选择上架时间', showTime: true },
      formItem: {},
      isShow: model.value.immediateMode === InitialMode.定时上架,
      required: true,
    },
    {
      label: '下架时间',
      el: 'date-picker',
      prop: 'removeTime',
      required: true,
      attrs: { placeholder: '请选择上架时间', showTime: true },
      isShow: model.value.immediateMode === InitialMode.定时上架,
      formItem: {},
    },

  ])

  function onClose() {
    modalState.value = false
  }

  async function onEdit(data?: PointsCommodity, readOnly = false) {
    model.value = deepCopy(data) ?? new PointsCommodity()
    modalState.value = true

    readonly.value = readOnly
  }

  async function onDel(record: PointsCommodityPageView) {
    await api.PointsMalls.Remove_PostAsync([record.id])
    proTableRef.value?.refresh()
  }

  async function onSetRecommend(record: PointsCommodityPageView) {
    api.PointsMalls.SetRecommend_GetAsync({ id: record.id }).then((res) => {
      if (res) {
        record.isRecommend = !record.isRecommend
        message.success('设置成功')
      }
    })
  }

  const { run: save, spinner } = useLoading(async () => {
    await proFormRef.value?.validate()
    return api.PointsMalls.SaveCommodity_PostAsync(model.value).then(() => {
      modalState.value = false
      proTableRef.value?.refresh()
      message.success(Guid.isNotNull(model.value.id) ? '更新成功' : '创建成功')
    })
  })

  return {
    modalState,
    save,
    spinner,
    fields,
    model,
    readonly,
    onClose,
    onEdit,
    onSetRecommend,
    onDel,
  }
}

function radioChange() {
  nextTick(() => {
    proTableRef.value?.search()
  })
}

onMounted(async () => {
  // await install()
  // router.currentRoute.value.query.id && getArticleData(router.currentRoute.value.query.id.toString())
})
</script>
