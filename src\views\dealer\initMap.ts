import { SystemBaseInfo } from '@/api'

export const initMap = {
  scriptNode: null as HTMLScriptElement | null,
  mapLoaded: false,

  async install() {
    if (this.mapLoaded) {
      return Promise.resolve((window as any).TMap)
    }

    const AK = await SystemBaseInfo.GetTencentMapKeyAsync()

    const TMap_URL = `https://map.qq.com/api/gljs?v=1.exp&libraries=tools,service&key=${AK}&callback=onMapCallback`

    this.scriptNode = document.createElement('script')
    this.scriptNode.setAttribute('type', 'text/javascript')
    this.scriptNode.setAttribute('src', TMap_URL)

    // 使用 onload 确认脚本加载完成
    return new Promise((resolve, reject) => {
      this.scriptNode!.onload = () => {
        if (typeof (window as any).TMap !== 'undefined') {
          this.mapLoaded = true
          resolve((window as any).TMap)
        }
        else {
          // 如果 TMap 还未初始化，则等待 onMapCallback
          (window as any).onMapCallback = () => {
            this.mapLoaded = true
            resolve((window as any).TMap)
          }
        }
      }

      this.scriptNode!.onerror = () => {
        reject(new Error('Failed to load TMap script'))
      }

      document.body.appendChild(this.scriptNode!)
    })
  },

  destroy() {
    if (this.scriptNode) {
      this.scriptNode.remove()
      this.scriptNode = null
    }
    delete (window as any).onMapCallback
    this.mapLoaded = false
  },
}

export function useInitMap(useId: () => string, options?: {
  immediate?: boolean
  onSuggestionChange?: (data: ISuggestion) => void
  onClick?: (data: Location) => void
}, mapOptions?: any) {
  const map = shallowRef()

  const mapId = `map-${useId()}`

  const mapSearchModel = reactive({
    provinceList: [] as District[],
    cityList: {} as Record<string, District[]>,
    results: [] as Array<any>,
    search: null as any,
    suggest: null as any,
    fold: false,
  })

  const infoWindow = shallowRef([] as any)

  let district: any

  const init = async () => {
    if (map.value) {
      map.value.destroy()
    }
    if (!TMap) {
      await initMap.install()
    }
    await nextTick()
    map.value = new TMap.Map(mapId, mapOptions || {
      zoom: 8, // 设置地图缩放级别
      viewMode: '2D',
      center: new TMap.LatLng(25.207313, 110.283999),
    })

    map.value.on('click', (evt: any) => {
      if (options?.onClick)
        options.onClick(evt.latLng)
    })

    district = new TMap.service.District({
      // 新建一个行政区划类
      polygon: 2, // 返回行政区划边界的类型
    })

    getDistrict(['广西壮族自治区', '桂林市'])
  }

  async function getDistrict([province, city]: [string, string]) {
    if (!mapSearchModel.provinceList.length) {
      await district.getChildren().then((result: { result: any[] }) => {
        mapSearchModel.provinceList = result.result[0]
      })
    }
    const provinceInfo = mapSearchModel.provinceList.find(v => v.fullname === province)
    if (!provinceInfo)
      return
    if (!(province in mapSearchModel.cityList)) {
      await district
        .getChildren({ id: provinceInfo.id })
        .then((result: any) => {
          mapSearchModel.cityList[provinceInfo.fullname] = result.result[0]
        })
    }
    const cityInfo = mapSearchModel.cityList[provinceInfo.fullname]?.find(v => v.fullname === city)
    if (cityInfo?.location) {
      map.value.setCenter(cityInfo.location)
    }
    else {
      map.value.setCenter(provinceInfo.location)
    }
    map.value.setZoom(12)
    mapSearchByRegion(city ?? province)
  }

  function mapSearchByRegion(region: string) {
    mapSearchModel.search = new TMap.service.Search({ pageSize: 10 }) // 新建一个地点搜索类
    mapSearchModel.suggest = new TMap.service.Suggestion({
      // 新建一个关键字输入提示类
      pageSize: 10, // 返回结果每页条目数
      region, // 限制城市范围
      regionFix: true, // 搜索无结果时是否固定在当前城市
    })
  }

  function mapSearchByKeyWord(e: any) {
    // 使用者在搜索框中输入文字时触发
    const keyword = e.target.value
    if (keyword && map.value) {
      mapSearchModel.suggest
        .getSuggestions({ keyword, location: map.value.getCenter() })
        .then((result: any) => {
          mapSearchModel.results = result.data as any
        })
        .catch((error: any) => {
          console.log(error)
        })
    }
  }

  const setSuggestion = useDebounceFn((item: ISuggestion) => {
    if (map.value) {
      infoWindow.value.forEach((infoWindow: { close: () => void }) => {
        infoWindow.close()
      })

      const _infoWindow = new TMap.InfoWindow({
        map: map.value,
        position: item.location,
        content: `<h3>${item?.title || ''}</h3><p>地址：${item.address || ''}</p><p>坐标：${item.location.lat},${item.location.lng}</p><button >选择</button>`,
        offset: { x: 0, y: -10 },
      })

      const dom = _infoWindow.dom as HTMLDivElement
      const button = dom.querySelector('button')

      button!.onclick = (e) => {
        e.stopPropagation()
        e.preventDefault()
        if (options?.onSuggestionChange)
          options?.onSuggestionChange(item)
        _infoWindow.close()
      }
      infoWindow.value.push(_infoWindow)
      map.value.setCenter(item.location)
    };
  }, 100)

  const maskList = shallowRef<any[]>([])

  function initMask<T extends Location>(data: T) {
    removeMarker()
    if (!data)
      return
    const { Dom } = Mask()

    const domObj = new Dom({
      map: map.value,
      position: [data.lng, data.lat],
      data,
    })
    map.value.setCenter({ lat: data.lat, lng: data.lng })
    maskList.value.push(domObj)
  }

  function removeMarker() {
    maskList.value.forEach((marker) => {
      marker.setMap(null)
      marker = null
    })
    maskList.value = []
  }

  onMounted(async () => {
    console.dir(mapId)
    await initMap.install()
    if (options?.immediate) {
      init()
    }
  })

  onUnmounted(() => {
    initMap.destroy()
  })

  return { initMask, mapId, map, init, mapSearchByRegion, mapSearchByKeyWord, mapSearchModel, setSuggestion, getDistrict }
}

export interface ISuggestion {
  id: string
  title?: string
  address?: string
  category?: string
  type?: number
  location: Location
  adcode?: number
  province?: string
  city?: string
  district?: string
  _distance?: number
}

interface Location {
  lat: number
  lng: number
  height?: number
}

export type DOMOverlay<T extends object> = {
  readonly map: any
  position: [number, number] // new TMap.LatLng(39.984104, 116.307503); [longitude,latitude]
} & T

interface District {
  id: string
  name: string
  fullname: string
  pinyin: string[]
  location: Location
  polygon: Location[][]
}

export function useDOMOverlay<T extends object>({
  createDOM,
  onInit,
  updateDOM,
  onDestroy,
}: {
  createDOM: (this: DOMOverlay<T>) => any
  onInit?: (this: DOMOverlay<T>, options: DOMOverlay<T>) => void
  updateDOM: (this: DOMOverlay<T> & { dom: any }) => any
  onDestroy?: (this: DOMOverlay<T> & { dom: any }) => any
}) {
  function Dom<T extends object>(this: DOMOverlay<T>, options: DOMOverlay<T>) {
    TMap.DOMOverlay.call(this, {
      ...options,
      position: new TMap.LatLng(Number(options.position[1]), Number(options.position[0])),
    })
  }

  Dom.prototype = new TMap.DOMOverlay()

  Dom.prototype.onDestroy = onDestroy
  Dom.prototype.updateDOM = updateDOM
  Dom.prototype.createDOM = createDOM
  if (!onInit) {
    Dom.prototype.onInit = function onIni(this: DOMOverlay<T>, options: DOMOverlay<T>) {
      Object.keys(options).forEach((k) => {
        (this as any)[k] = (options as any)[k]
      })
    }
  }
  else {
    Dom.prototype.onInit = onInit
  }
  return { Dom } as unknown as { Dom: new (options: DOMOverlay<T>) => any }
}

export function Mask() {
  return useDOMOverlay<{ data: any }>({
    createDOM() {
      const box = document.createElementNS('http://www.w3.org/2000/svg', 'svg')
      box.setAttribute('style', '--color:red')
      box.setAttribute('viewBox', '0 0 1024 1024')
      box.setAttribute('class', 'map-icon')
      box.setAttribute('width', '24')
      box.setAttribute('height', '24')
      const path = document.createElementNS('http://www.w3.org/2000/svg', 'path')
      path.setAttribute(
        'd',
        'M895.616384 347.812188q0 10.22977-0.511489 19.436563t-1.534466 19.436563q-9.206793 84.907093-37.338661 163.164835t-71.096903 150.377622-99.228771 138.613387-121.734266 127.872128q-9.206793 11.252747-23.528472 11.252747-15.344655 0-24.551449-11.252747-65.470529-61.378621-122.245754-128.895105t-100.251748-141.170829-71.608392-152.935065-36.315684-165.210789q0-8.183816-0.511489-15.344655t-0.511489-15.344655q0-71.608392 28.131868-135.032967t76.211788-110.481518 113.038961-74.677323 138.613387-27.62038 138.101898 27.62038 112.527473 74.677323 76.211788 110.481518 28.131868 135.032967zM540.643357 507.396603q33.758242 0 63.424575-12.787213t51.66034-34.26973 34.781219-50.637363 12.787213-61.89011-12.787213-61.89011-34.781219-50.637363-51.66034-34.26973-63.424575-12.787213-63.424575 12.787213-52.171828 34.26973-35.292707 50.637363-12.787213 61.89011 12.787213 61.89011 35.292707 50.637363 52.171828 34.26973 63.424575 12.787213z',
      )
      path.setAttribute('p-id', '2440')
      box.append(path)
      return box
    },
    updateDOM() {
      if (!this.map) {
        return
      }
      // 经纬度坐标转容器像素坐标
      const pixel = this.map.projectToContainer(this.position)
      // 使饼图中心点对齐经纬度坐标点
      const left = `${pixel.getX() - this.dom.clientWidth / 2}px`
      const top = `${pixel.getY() - this.dom.clientHeight / 2}px`
      this.dom.style.transform = `translate(${left}, ${top})`
    },
  })
}
