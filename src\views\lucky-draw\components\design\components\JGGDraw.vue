<template>
  <div class="w-68%">
    <div class="grid grid-cols-[5rem_5rem_5rem] grid-rows-3 gap2">
      <div v-for="item, index in prizesList" :key="index" class="size-20">
        <div v-if="index === 4" class="yw box-border size-100% center rounded-4 px-3 text-center text-5.5 c-#B5322A font-bold tracking-widest" @click="onDrawPrize()">
          {{ item.name }}
        </div>
        <div v-else :class="{ select: index === curIndex }" class="size-100% flex flex-col items-center justify-center rounded-4 bg-#FFFBE6">
          <div class="h-12.5 w-16">
            <img class="h-100% w-100% rounded-4" :src="joinImageById(item.images!)" alt="">
          </div>
          <div class="w-full overflow-hidden text-center text-3.5 c-#F33030" :class="{ 'c-#fff': index === curIndex }">
            {{ item.name }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { LotteryPrizes } from '@/api/models'
import { message } from 'ant-design-vue'
import { useInjectionLuckDrawData, useLuckDraw } from '../useDraw'

const { data, joinImageById, drawPrizeApi, luckDrawConfig, prizes: prizesInfo } = useInjectionLuckDrawData()

const { begin, curIndex, winningAPrize, stop } = useLuckDraw({
  success: () => {
    if (prizesInfo.value)
      luckDrawConfig?.onLuckDrawCallback(prizesInfo.value)
  },
})

const prizesList = computed(() => {
  const temp = deepCopy(data?.value.prizes)
  if (temp?.length !== 9)
    temp!.splice(4, 0, { ...new LotteryPrizes(), name: '立即抽奖' })
  return temp
})

async function onDrawPrize() {
  winningAPrize.value = -1
  begin()
  try {
    const res = await drawPrizeApi()
    winningAPrize.value = prizesList.value?.findIndex(v => v.id === res.id) ?? -1
    if (winningAPrize.value === -1) {
      message.error('奖品获取错误')
      stop()
    }
  }
  catch (err) {
    console.log('%c [  ]-38', 'font-size:13px; background:pink; color:#bf2c9f;', err)
    stop()
  }
}
</script>

<style scoped>
.yw {
  background: radial-gradient(
    57.14% 59.78% at 50.54943359729352% 49.99999585359027%,
    rgba(255, 221, 67, 1) 0%,
    rgba(250, 226, 151, 1) 100%
  );
}
</style>
