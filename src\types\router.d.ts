import type { RoleName } from '../permission'
import type RouterItemMeta from './interfaces'
/*
 * @Description: ^_^
 * @Author: sharebravery
 * @Date: 2022-12-20 16:57:05
 */
import 'vue-router'

declare module 'vue-router' {
  interface RouteMeta extends RouterItemMeta {

    /**
     * 授权信息，可以是字符串、字符串数组或回调函数,空数组认为有无权限要求，undefined则需要配置否则无法访问
     */
    authorize?: RoleName | RoleName[]

    /**
     * 子项是否授权
     * @default false
     */
    authorizeChild?: boolean
    /**
     * 布局
     * false 不设置布局、null则使用默认布局
     * @default null
     */
    layout?: string | false
    /**
     * 当前页面的渲染组件是否替换为指定的布局组件
     * 当layout设置为false时,且当前值为true则将当前组件作为布局组件,否则将当前页面的渲染组件替换为指定的布局组件
     * @default false
     */
    isLayout?: boolean
    /**
     * 生成一个父级路由作为layout
     * 如果不替换时将生成一个新的嵌套，这个配置是生成嵌套路由的配置
     */
    layoutRoute?: Partial<Omit<RouteRecordRaw, 'layoutRoute' | 'isLayout'>>
    /**
     * 是否是本地路由，不需要进行后端验证,如果存在authorize属性则以authorize为准
     */
    local?: boolean

    /**
     * 生成路由时赋值的全路径
     */
    fullPath?: string

    /**
     * 隐藏菜单
     */
    hidden?: boolean

    /**
     * 排序
     */
    order?: number

    /**
     * 页面标题
     */
    title?: string
    /**
     * 目标路由，可以为空或未定义
     */
    target?: string | null | undefined
    /**
     * 备注信息，用于开发时的注释
     */
    remark?: string
    /**
     * 徽章数
     */
    badge?: number
    /**
     * 未处理的表单数
     */
    unFormCount?: number

    /**
     * 是否保持页面活跃
     */
    keepAlive?: boolean
    /**
     * 是否固定在标签页
     */
    isAffix?: boolean
    /**
     * 是否为全屏页面
     */
    isFull?: boolean
    /**
     * 图标，显示在导航菜单及标签页
     */
    icon?: string | null | undefined
    /**
     * 是否隐藏页面头部
     */
    hiddenPageHeader?: boolean
    /**
     * 是否不显示页面标题内容
     */
    hiddenPageHeaderContent?: boolean

    /**
     * 是否隐藏页面头部的面包屑导航
     */
    hiddenBreadcrumb?: boolean
    /**
     * 导航中是否提升子菜单到本级，自身不显示在导航
     */
    concede?: boolean
    /**
     * 是否总是显示
     */
    alwaysShow?: boolean
    /**
     * 是否不缓存页面
     */
    noCache?: boolean
  }

}

declare module '@zougt/vite-plugin-theme-preprocessor/dist/browser-utils' {
  type toggleTheme = any
}
