<!--
 * @Description:
 * @Author: 景 彡
 * @Date: 2024-11-01 11:50:33
 * @LastEditors: 景 彡
-->
<template>
  <div class="h-full p-16px py-32px c-bg">
    <a-spin :spinning="spinner">
      <a-form
        v-if="form"
        :model="form"
        name="basic"
        autocomplete="off"
        layout="vertical"
        @finish="onFinish"
      >
        <a-form-item
          label="AppID(小程序ID)"
          name="appId"
          :rules="[{ required: true, message: '请输入小程序ID' }]"
        >
          <a-input v-model:value="form.appId!" />
        </a-form-item>

        <a-form-item
          label="AppSecret(小程序密钥)"
          name="appSecret"
          :rules="[{ required: true, message: '请输入小程序密钥' }]"
        >
          <a-input-password v-model:value="form.appSecret!" />
        </a-form-item>

        <a-form-item>
          <a-button type="primary" html-type="submit">保存</a-button>
        </a-form-item>
      </a-form>
    </a-spin>
  </div>
</template>

<script lang='ts' setup>
import * as api from '@/.generated/apis'
import { WeChatApiKeyEntity } from '@/.generated/models'
import { message } from 'ant-design-vue'

const [,spinner, form] = useLoading(api.WeChatApiKeys.GetAppApiKeyAsync, { default: new WeChatApiKeyEntity(), immediate: true })

async function onFinish() {
  spinner.value = true
  await api.WeChatApiKeys.SaveAppApiKey_PostAsync(form.value!).then(() => {
    spinner.value = false
    message.success('保存成功')
  }).catch(() => {
    spinner.value = false
    message.error('保存失败')
  })
}
</script>

<style scoped>

</style>
