<template>
  <c-modal v-model:visible="open" full-modal wrap-class-name="form-design-data-modal" width="80%" :footer="null" destroy-on-close>
    <template #title>
      <div>
        <span>{{ dataForm.name }}</span>
        <span class="ml-4 text-4 font-normal">【未处理的数据数量：<span class="c-primary font-bold">{{ unFormCount }}</span> 】</span>
      </div>
    </template>
    <c-pro-table row-key="id" :show-del-btn="false" :columns="formDataColumns" immediate :api="findFormDataByDesign">
      <template #header>
        <a-button type="primary" @click="exportDow">数据导出</a-button>
      </template>
    </c-pro-table>
  </c-modal>
</template>

<script setup lang="tsx">
import type { FormData } from '@/api/models'
import type { ColumnProps } from 'ch2-components/types/pro-table/types'
import * as api from '@/api'
import { FormDataState, FormDesign } from '@/api/models'

import { message, Tag } from 'ant-design-vue'
import { EnumSelect } from 'ch2-components'

const open = ref(false)

const dataForm = ref(new FormDesign())

const unFormCount = ref(0)

async function getUnFormCount() {
  unFormCount.value = await api.FormDesigns.GetUnFormDataStateByIdAsync({ formDesignId: dataForm.value.id })
}

const { lookData, findFormDataByDesign, formDataColumns } = useLookData()

function lookDataById(id: string) {
  api.FormDesigns.FindDesignById_GetAsync({ id }).then((res) => {
    lookData(res)
  })
}

function useLookData() {
  const formDataColumns = ref<ColumnProps[]>([])

  const current = ref<FormData>()

  const lookData = (data: FormDesign) => {
    dataForm.value = data!
    formDataColumns.value = data.formStructure?.map((item) => {
      return {
        title: item.label,
        dataIndex: ['data', item.field!],
        key: item.field!,
      }
    }) || []

    formDataColumns.value.push({
      title: '提交时间',
      dataIndex: 'createTime',
      key: 'createTime',
      dateFormat: true,
      width: 160,
      align: 'center',
    }, {
      title: '状态',
      dataIndex: 'formDataState',
      key: 'formDataState',
      enum: FormDataState,
      width: 140,
      align: 'center',
      search: {
        prop: 'state',
        method: 'GET',
        el: 'enum-select',
        attrs: { enum: FormDataState },
      },
      bodyCell({ record }) {
        if (current.value && current.value?.id === record.id) {
          return (
            <EnumSelect
              enum={FormDataState}
              value={current.value?.formDataState}
              onUpdate:value={v => current.value!.formDataState = v as any}
              onBlur={() => current.value = undefined}
              onChange={() => {
                api.FormDesigns.EditFormDataState_PostAsync({ id: record.id, state: current.value!.formDataState }).then(() => {
                  record.formDataState = current.value!.formDataState
                  current.value = undefined
                  message.success('修改成功')
                })
              }}
            />
          )
        }

        return <Tag onClick={() => current.value = deepCopy(record)} color={record.formDataState === 0 ? 'red' : 'blue'}>{FormDataState[record.formDataState]}</Tag>
      },
    })

    open.value = true
  }

  const findFormDataByDesign = async (prams: any) => {
    const res = await api.FormDesigns.FindFormDataByDesign_GetAsync({ ...prams, designId: dataForm.value!.id })
    getUnFormCount()
    return {
      ...res,
      items: res.items?.map((item) => {
        const data = Object.fromEntries(item.formItem?.map(item => [item.key, item.value]) || [])

        return { ...item, data }
      }),
    }
  }

  return {
    dataVisible: open,
    lookData,
    findFormDataByDesign,
    formDataColumns,
    unFormCount,
  }
}

async function exportDow() {
  useDownload(() => api.FormDesigns.ExportFormData_GetAsync({ designId: dataForm.value.id }))
}

defineExpose({
  lookDataById,
  lookData,
  open,
})
</script>

<style scoped>

</style>
