/*
 * @Description:
 * @Author: luckymiaow
 * @Date: 2023-06-05 16:47:11
 * @LastEditors: luckymiaow
 */

import type { ThemeColor } from '@/types/interfaces'
import { theme as aTheme } from 'ant-design-vue'
import { computed } from 'vue'

export * from './utils'

export function useToken() {
  const { useToken } = aTheme
  const { token, theme, hashId } = useToken()

  return {
    token,
    theme,
    hashId,
    textStyle: computed(() => ({ color: token.value.colorText, fontSize: token.value.fontSize })),
  }
}

// #region  主题文件

// path 对应assets/themes下的主题文件
export const themes: Record<string, ThemeColor> = {
  '#00A73B': {
    colorPrimary: '#00A73B',
    colorInfo: '#00A73B',
    colorSuccess: '#52c41a',
    colorError: '#f5222d',
    colorWarning: '#FFD700',
    colorBgBase: '#ffffff',
    colorTextBase: '#000000',
  },
  '#1890ff': {
    colorPrimary: '#1890ff',
    colorInfo: '#1890ff',
    colorSuccess: '#52c41a',
    colorError: '#f5222d',
    colorWarning: '#faad14',
    colorBgBase: '#ffffff',
    colorTextBase: '#000000',
  },
  '#722ed1': {
    colorPrimary: '#722ed1',
    colorInfo: '#722ed1',
    colorSuccess: '#34c3ff',
    colorError: '#f5222d',
    colorWarning: '#faad14',
    colorBgBase: '#ffffff',
    colorTextBase: '#000000',
  },
  '#eb2f96': {
    colorPrimary: '#eb2f96',
    colorInfo: '#eb2f96',
    colorSuccess: '#52c41a',
    colorError: '#f5222d',
    colorWarning: '#faad14',
    colorBgBase: '#ffffff',
    colorTextBase: '#000000',
  },
  '#13c2c2': {
    colorPrimary: '#13c2c2',
    colorInfo: '#13c2c2',
    colorSuccess: '#52c41a',
    colorError: '#f5222d',
    colorWarning: '#faad14',
    colorBgBase: '#ffffff',
    colorTextBase: '#000000',
  },
  '#fa8c16': {
    colorPrimary: '#fa8c16',
    colorInfo: '#fa8c16',
    colorSuccess: '#52c41a',
    colorError: '#f5222d',
    colorWarning: '#faad14',
    colorBgBase: '#ffffff',
    colorTextBase: '#000000',
  },
}

export const darkThemes: Record<string, ThemeColor> = {
  '#00A73B': {
    colorPrimary: '#00A73B',
    colorInfo: '#00A73B',
    colorSuccess: '#52c41a',
    colorError: '#f5222d',
    colorWarning: '#FFD700',
    colorBgBase: '#000000',
    colorTextBase: '#ffffff',
  },
  '#1890ff': {
    colorPrimary: '#1890ff',
    colorInfo: '#1890ff',
    colorSuccess: '#52c41a',
    colorError: '#f5222d',
    colorWarning: '#faad14',
    colorBgBase: '#000000',
    colorTextBase: '#ffffff',
  },
  '#820014': {
    colorPrimary: '#820014',
    colorInfo: '#820014',
    colorSuccess: '#234f1e',
    colorError: '#610b0b',
    colorWarning: '#78350f',
    colorBgBase: '#000000',
    colorTextBase: '#ffffff',
  },
  '#13c2c2': {
    colorPrimary: '#13c2c2',
    colorInfo: '#13c2c2',
    colorSuccess: '#52c41a',
    colorError: '#f5222d',
    colorWarning: '#faad14',
    colorBgBase: '#000000',
    colorTextBase: '#ffffff',
  },
}

// #endregion
