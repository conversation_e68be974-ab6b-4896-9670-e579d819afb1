import { ClientUser } from "./ClientUser";
import { RecordSource } from "./RecordSource";
/**积分交易记录*/
export class PointsTransaction {
  /**积分的增减量*/
  number: number = 0;
  /**增加t / 减少 f*/
  opType: boolean = false;
  /**操作时间*/
  time: Dayjs = dayjs();
  /**简短描述*/
  shortDescription?: string | null | undefined = null;
  /**描述*/
  description?: string | null | undefined = null;
  clientUserId: GUID = "00000000-0000-0000-0000-000000000000";
  /**关联用户*/
  clientUser?: ClientUser | null | undefined = null;
  /**来源*/
  source: RecordSource = 0;
  /**来源ID
产品扫码 => 产品ID
推广返佣 => 推广活动ID
抽奖 => 奖品ID*/
  sourceId?: GUID = null;
  /**防伪ID*/
  productSecurityCodeId?: GUID = null;
  /**产品活动ID*/
  productActivitiesId?: GUID = null;
  /**软删除标记*/
  softDeleted: boolean = false;
  /**软删除者*/
  deletedBy?: string | null | undefined = null;
  /**软删除时间*/
  deleted?: Dayjs | null | undefined = null;
  /**创建时间*/
  created: Dayjs = dayjs();
  /**修改时间*/
  modified?: Dayjs | null | undefined = null;
  /**创建者*/
  createdBy?: string | null | undefined = null;
  /**修改者*/
  modifiedBy?: string | null | undefined = null;
  /**主键*/
  id: GUID = "00000000-0000-0000-0000-000000000000";
}
