<template>
  <a-drawer v-model:open="modalState" destroy-on-close :mask-closable="false" width="100%" placement="right">
    <a-spin :spinning="spinner">
      <div class="flex">
        <div class="w-600px">
          <c-pro-form
            ref="proFormRef"
            v-model:value="model"
            :read-only="readOnly"
            :fields="fields"
            :descriptions="{ column: 1, bordered: true, labelStyle: { width: '140px' } }"
          >
            <template #image>
              <div v-if="model.image" class="coverBox size-140px overflow-hidden">
                <ImageView :src="(model.image)" alt="avatar" :preview="true" :del-ico="true" style="height: 140px; width:140px ; object-fit:cover" @del-image="() => model.image = ''" />
              </div>
              <a-button v-else type="dashed" block class="size-25" @click="avatarUpload">
                <template #icon>
                  <c-icon-plus-outlined />
                </template>
                上传
              </a-button>
            </template>>
          </c-pro-form>
        </div>
        <div :calss="{ py16px: readOnly }" class="ml8px w0 flex-1 rounded-4px shadow">
          <Editor v-model:value="model.details!" :read-only="readOnly" />
        </div>
      </div>
    </a-spin>
    <template v-if="!readOnly" #extra>
      <a-button style="margin-right: 8px" @click="onClose">取消</a-button>
      <a-button type="primary" @click="save()">保存</a-button>
    </template>
  </a-drawer>
</template>

<script setup lang="ts">
import type { FormField } from 'ch2-components/types/pro-form/types'
import * as api from '@/api/'
import { FileType, ProductEditModel, ProductStatus } from '@/api/models'
import ProductTypeSelect from '@/components/ProductTypeSelect.vue'
import { Guid } from '@/utils/GUID'
import { message } from 'ant-design-vue'
import { UnitType } from 'ch2-components/lib/input-money/types'

const props = defineProps<{ readOnly?: boolean, id?: GUID }>()

const emit = defineEmits(['change'])

const modalState = defineModel('open', { default: false })

const model = defineModel('value', { default: new ProductEditModel() })

const proFormRef = useTemplateRef('proFormRef')

watch(props, () => {
  if (Guid.isNotNull(props.id) && modalState.value === true) {
    api.Products.GetAsync({ id: props.id }).then((res) => {
      model.value = res
    })
  }
}, { immediate: true })

const fields = reactive<FormField[]>([
  { label: '产品编号', el: 'input', prop: 'productionNumber', required: true, attrs: { placeholder: '请输入产品编号' }, formItem: {} },
  { label: '产品名称', el: 'input', prop: 'name', required: true, attrs: { placeholder: '请输入产品名称' }, formItem: {} },
  { label: '产品规格', el: 'input', prop: 'specs', required: true, attrs: { placeholder: '请输入产品规格' }, formItem: {} },
  { label: '产品特点', el: 'textarea', prop: 'feature', required: true, attrs: { placeholder: '请输入产品特点' }, formItem: {} },
  { label: '描述', el: 'textarea', prop: 'description', required: true, attrs: { placeholder: '请输入描述' }, formItem: {} },
  { label: '图片(1:1)', el: 'input', prop: 'image', required: true, attrs: { placeholder: '请输入图片（Path' }, formItem: {} },
  { label: '价格', el: 'input-money', prop: 'price', required: true, attrs: { placeholder: '请输入价格', unit: UnitType.元, showUnit: true, style: { width: '100%' } }, formItem: {} },
  { label: '产品状态', el: 'enum-select', prop: 'status', required: true, attrs: { placeholder: '请选择产品状态', enum: ProductStatus }, formItem: {} },
  { label: '产品类型', el: ProductTypeSelect, prop: 'typeId', required: true, attrs: {
    placeholder: '请输入产品类型',
  }, formItem: { rules: [{
    required: true,
    validator: FormValidator('guidReg', '产品类型不能为空'),
    trigger: 'blur',
  }] } },
])

function onClose() {
  modalState.value = false
}

const { run: save, spinner } = useLoading(async () => {
  await proFormRef.value?.validate()
  return (Guid.isNotNull(model.value.id) ? api.Products.Update_PostAsync : api.Products.Add_PostAsync)({ ...model.value }).then(() => {
    modalState.value = false
    emit('change')
    message.success(model.value.id ? '更新成功' : '创建成功')
  })
},

)

function avatarUpload() {
  useFileMangerModal((files) => {
    model.value.image = files[0]?.id
  }, { multiple: false, immediateReturn: true, menu: [FileType.图片] })
}
</script>

<style scoped>

</style>
