<!--
 * @Description:积分红包
 * @Author: 景 彡
 * @Date: 2024-11-01 10:39:19
 * @LastEditors: 景 彡
-->
<template>
  <div class="h-76px p-16px c-bg">
    <a-row :gutter="[16, 16]">
      <a-col :span="8">
        <div class="flex items-center">
          <span class="inline-block w-76px">时间范围</span>
          <c-range-picker v-model:start-time="params.start!" v-model:end-time="params.end!" style="width: 100%" />
        </div>
      </a-col>
      <a-col :span="8">
        <a-button type="primary" :icon="h(SearchOutlined)" @click="getData()">搜索</a-button>
      </a-col>
    </a-row>
  </div>
  <a-row :gutter="[16, 16]">
    <a-col :span="8">
      <div class="mt-24px p-16px c-bg">
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <div class="h-20px w-5px rounded-[0_4px_4px_0] bg-primary" />
            <div class="ml-8px text-16px font-bold">积分累计排行榜</div>
          </div>
          <div>单位：个</div>
        </div>
        <div ref="integralRef" class="h-420px w-100%" />
      </div>
    </a-col>
    <a-col :span="8">
      <div class="mt-24px p-16px c-bg">
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <div class="h-20px w-5px rounded-[0_4px_4px_0] bg-primary" />
            <div class="ml-8px text-16px font-bold">现金红包累计排行榜</div>
          </div>
          <div>单位：元</div>
        </div>
        <div ref="redEnvelopeRef" class="h-420px w-100%" />
      </div>
    </a-col>
    <a-col :span="8">
      <div class="mt-24px p-16px c-bg">
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <div class="h-20px w-5px rounded-[0_4px_4px_0] bg-primary" />
            <div class="ml-8px text-16px font-bold">推广人数累计排行榜</div>
          </div>
          <div>单位：个</div>
        </div>
        <div ref="extensionRef" class="h-420px w-100%" />
      </div>
    </a-col>
  </a-row>

  <div class="mt-16px px-16px py-32px c-bg">
    <a-row>
      <a-col :span="8">
        <div class="flex flex-col items-center justify-center">
          <div class="text-42px c-primary-text font-bold">{{ data.pointTransaction }}</div>
          <div class="mt-8px text-16px">累计积分</div>
        </div>
      </a-col>
      <a-col :span="8">
        <div class="flex flex-col items-center justify-center">
          <div class="text-42px c-primary-text font-bold">{{ data.cashbackRecord / 100 }}</div>
          <div class="mt-8px text-16px">累计返现</div>
        </div>
      </a-col>
      <a-col :span="8">
        <div class="flex flex-col items-center justify-center">
          <div class="text-42px c-primary-text font-bold">{{ data.referralRecord }}</div>
          <div class="mt-8px text-16px">累计推广人数</div>
        </div>
      </a-col>
    </a-row>
  </div>

  <div class="mt-16px">
    <a-row :gutter="[16, 16]">
      <a-col :span="8" class="p-16px c-bg">
        <a-card title="积分记录" style="width: 100%">
          <a-table :columns="pointColumns" :data-source="data.pointTransactionLog || []">
            <template #bodyCell="{ column, text }">
              <template v-if="column.dataIndex === 'time'">
                <span>{{ dateFormat(text) }}</span>
              </template>
            </template>
          </a-table>
        </a-card>
      </a-col>
      <a-col :span="8" class="p-16px c-bg">
        <a-card title="红包记录" style="width: 100%">
          <a-table :columns="cashbackColumns" :data-source="data.cashbackRecordLog || []">
            <template #bodyCell="{ column, text }">
              <template v-if="column.dataIndex === 'data'">
                <span>{{ text / 100 }}元</span>
              </template>
              <template v-if="column.dataIndex === 'time'">
                <span>{{ dateFormat(text) }}</span>
              </template>
            </template>
          </a-table>
        </a-card>
      </a-col>
      <a-col :span="8" class="p-16px c-bg">
        <a-card title="推广记录" style="width: 100%">
          <a-table :columns="referralColumns" :data-source="data.referralRecordLog || []">
            <template #bodyCell="{ column, text }">
              <template v-if="column.dataIndex === 'time'">
                <span>{{ dateFormat(text) }}</span>
              </template>
            </template>
          </a-table>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script lang='ts' setup>
import * as api from '@/api/'
import { PointsCashbackTimeStatistics } from '@/api/models'
import { SearchOutlined } from '@ant-design/icons-vue'
import { getChartsBarOption } from './components/getChartsBarOption'

definePage({
  meta: {
    title: '积分红包',
    layoutRoute: {
      meta: { layout: 'admin', title: '积分红包', local: true, icon: 'RedEnvelopeOutlined', order: 13 },
    },
  },
})

const params = ref({
  start: dayjs().subtract(14, 'day'),
  end: dayjs(),
})

const referralColumns = ref([
  {
    title: '姓名',
    dataIndex: 'name',
  },
  {
    title: '手机号',
    dataIndex: 'phone',
  },
  {
    title: '推广人数',
    dataIndex: 'data',
  },
  {
    title: '获取时间',
    dataIndex: 'time',
  },

])

const pointColumns = ref([
  {
    title: '姓名',
    dataIndex: 'name',
  },
  {
    title: '手机号',
    dataIndex: 'phone',
  },
  {
    title: '积分',
    dataIndex: 'data',
  },
  {
    title: '获取时间',
    dataIndex: 'time',
  },

])

const cashbackColumns = ref([
  {
    title: '姓名',
    dataIndex: 'name',
  },
  {
    title: '手机号',
    dataIndex: 'phone',
  },
  {
    title: '红包',
    dataIndex: 'data',
  },
  {
    title: '获取时间',
    dataIndex: 'time',
  },

])

const data = ref(new PointsCashbackTimeStatistics())

const [extensionRef,,init] = useECharts('extensionRef')

const [redEnvelopeRef,,redEnvelopeInit] = useECharts('redEnvelopeRef')

const [integralRef,,integralInit] = useECharts('integralRef')

async function getData() {
  data.value = await api.DataAnalysis.PointsAndCashbackRecordTotal_GetAsync({ ...params.value, start: params.value.start?.toDate(), end: params.value.end?.toDate() })

  data.value.cashbackRecordTotal = data.value.cashbackRecordTotal?.map(item => ({ phone: item.phone, name: item.name, data: item.data / 100 }))

  nextTick(() => {
    init(getChartsBarOption(data.value.referralRecordsTotal || [], ['#09B388', '#009944']))
    redEnvelopeInit(getChartsBarOption(data.value.cashbackRecordTotal || [], ['#F5BFCC', '#E33C64']))
    integralInit(getChartsBarOption(data.value.pointTransactionTotal || [], ['#9BF2DC', '#43CF7C']))
  })
}

onMounted(() => {
  getData()
})
</script>

<style scoped>

</style>
