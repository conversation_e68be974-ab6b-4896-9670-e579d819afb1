/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-router. ‼️ DO NOT MODIFY THIS FILE ‼️
// It's recommended to commit this file.
// Make sure to add this file to your tsconfig.json file as an "includes" or "files" entry.

declare module 'vue-router/auto-routes' {
  import type {
    RouteRecordInfo,
    ParamValue,
    ParamValueOneOrMore,
    ParamValueZeroOrMore,
    ParamValueZeroOrOne,
  } from 'vue-router'

  /**
   * Route name map generated by unplugin-vue-router
   */
  export interface RouteNamedMap {
    '/[...404]': RouteRecordInfo<'/[...404]', '/:404(.*)', { 404: ParamValue<true> }, { 404: ParamValue<false> }>,
    '/article-management/': RouteRecordInfo<'/article-management/', '/article-management', Record<never, never>, Record<never, never>>,
    '/article-management/category': RouteRecordInfo<'/article-management/category', '/article-management/category', Record<never, never>, Record<never, never>>,
    '/article-management/editor-new': RouteRecordInfo<'/article-management/editor-new', '/article-management/editor-new', Record<never, never>, Record<never, never>>,
    '/article-management/form-design': RouteRecordInfo<'/article-management/form-design', '/article-management/form-design', Record<never, never>, Record<never, never>>,
    '/customer-service/': RouteRecordInfo<'/customer-service/', '/customer-service', Record<never, never>, Record<never, never>>,
    '/dealer/': RouteRecordInfo<'/dealer/', '/dealer', Record<never, never>, Record<never, never>>,
    '/event-management/': RouteRecordInfo<'/event-management/', '/event-management', Record<never, never>, Record<never, never>>,
    '/event-management/editor-new': RouteRecordInfo<'/event-management/editor-new', '/event-management/editor-new', Record<never, never>, Record<never, never>>,
    '/file-managers': RouteRecordInfo<'/file-managers', '/file-managers', Record<never, never>, Record<never, never>>,
    '/home': RouteRecordInfo<'/home', '/home', Record<never, never>, Record<never, never>>,
    '/lacquer-worker-management/': RouteRecordInfo<'/lacquer-worker-management/', '/lacquer-worker-management', Record<never, never>, Record<never, never>>,
    '/lacquer-worker-management/log/': RouteRecordInfo<'/lacquer-worker-management/log/', '/lacquer-worker-management/log', Record<never, never>, Record<never, never>>,
    '/login/': RouteRecordInfo<'/login/', '/login', Record<never, never>, Record<never, never>>,
    '/login/login': RouteRecordInfo<'/login/login', '/login/login', Record<never, never>, Record<never, never>>,
    '/logoutReload': RouteRecordInfo<'/logoutReload', '/logoutReload', Record<never, never>, Record<never, never>>,
    '/lucky-draw/': RouteRecordInfo<'/lucky-draw/', '/lucky-draw', Record<never, never>, Record<never, never>>,
    '/lucky-draw/start_[[id]]': RouteRecordInfo<'/lucky-draw/start_[[id]]', '/lucky-draw/start_:id?', { id?: ParamValueZeroOrOne<true> }, { id?: ParamValueZeroOrOne<false> }>,
    '/notification-management/': RouteRecordInfo<'/notification-management/', '/notification-management', Record<never, never>, Record<never, never>>,
    '/order-management/': RouteRecordInfo<'/order-management/', '/order-management', Record<never, never>, Record<never, never>>,
    '/points-mall/': RouteRecordInfo<'/points-mall/', '/points-mall', Record<never, never>, Record<never, never>>,
    '/points-red-envelope/': RouteRecordInfo<'/points-red-envelope/', '/points-red-envelope', Record<never, never>, Record<never, never>>,
    '/product-manager/': RouteRecordInfo<'/product-manager/', '/product-manager', Record<never, never>, Record<never, never>>,
    '/product-manager/activity-management': RouteRecordInfo<'/product-manager/activity-management', '/product-manager/activity-management', Record<never, never>, Record<never, never>>,
    '/product-manager/type': RouteRecordInfo<'/product-manager/type', '/product-manager/type', Record<never, never>, Record<never, never>>,
    '/qr-code-configuration/': RouteRecordInfo<'/qr-code-configuration/', '/qr-code-configuration', Record<never, never>, Record<never, never>>,
    '/qr-code-configuration/anti-theft-brush': RouteRecordInfo<'/qr-code-configuration/anti-theft-brush', '/qr-code-configuration/anti-theft-brush', Record<never, never>, Record<never, never>>,
    '/referrals-management/': RouteRecordInfo<'/referrals-management/', '/referrals-management', Record<never, never>, Record<never, never>>,
    '/referrals-management/configure': RouteRecordInfo<'/referrals-management/configure', '/referrals-management/configure', Record<never, never>, Record<never, never>>,
    '/statistics/': RouteRecordInfo<'/statistics/', '/statistics', Record<never, never>, Record<never, never>>,
    '/system-manage/amount-management/': RouteRecordInfo<'/system-manage/amount-management/', '/system-manage/amount-management', Record<never, never>, Record<never, never>>,
    '/system-manage/basic-information-management/': RouteRecordInfo<'/system-manage/basic-information-management/', '/system-manage/basic-information-management', Record<never, never>, Record<never, never>>,
    '/system-manage/key-configuration/': RouteRecordInfo<'/system-manage/key-configuration/', '/system-manage/key-configuration', Record<never, never>, Record<never, never>>,
    '/system-manage/log/': RouteRecordInfo<'/system-manage/log/', '/system-manage/log', Record<never, never>, Record<never, never>>,
    '/system-manage/manage': RouteRecordInfo<'/system-manage/manage', '/system-manage/manage', Record<never, never>, Record<never, never>>,
    '/system-manage/role/': RouteRecordInfo<'/system-manage/role/', '/system-manage/role', Record<never, never>, Record<never, never>>,
    '/system-manage/user/': RouteRecordInfo<'/system-manage/user/', '/system-manage/user', Record<never, never>, Record<never, never>>,
    '/user-center/': RouteRecordInfo<'/user-center/', '/user-center', Record<never, never>, Record<never, never>>,
    '/user-center/user-center': RouteRecordInfo<'/user-center/user-center', '/user-center/user-center', Record<never, never>, Record<never, never>>,
  }
}
