<!--
 * @Description:
 * @Author: 景 彡
 * @Date: 2024-11-01 11:50:33
 * @LastEditors: 景 彡
-->
<template>
  <div class="h-full p-16px py-32px c-bg">
    <a-spin :spinning="spinner">
      <a-form
        v-if="form"
        :model="form"
        name="basic"
        autocomplete="off"
        layout="vertical"
        @finish="onFinish"
      >
        <a-form-item
          label="申请商户号的appid或商户号绑定的appid"
          name="appId"
          :rules="[{ required: true, message: '请输入申请商户号的appid或商户号绑定的appid' }]"
        >
          <a-input v-model:value="form.appId!" />
        </a-form-item>

        <a-form-item
          label="微信商户号"
          name="merchantId"
          :rules="[{ required: true, message: '请输入微信商户号' }]"
        >
          <a-input v-model:value="form.merchantId!" />
        </a-form-item>
        <a-form-item
          label="微信商户 v3 API 密钥"
          name="merchantV3Secret"
          :rules="[{ required: true, message: '请输入微信商户 v3 API 密钥' }]"
        >
          <a-input v-model:value="form.merchantV3Secret!" />
        </a-form-item>

        <a-form-item
          label="微信商户证书序列号"
          name="merchantCertificateSerialNumber"
          :rules="[{ required: true, message: '请输入微信商户证书序列号' }]"
        >
          <a-input v-model:value="form.merchantCertificateSerialNumber!" />
        </a-form-item>
        <a-form-item>
          <a-radio-group v-model:value="keyType" button-style="solid">
            <a-radio-button value="秘钥内容">秘钥内容</a-radio-button>
            <a-radio-button value="秘钥路径">秘钥路径</a-radio-button>》
          </a-radio-group>
        </a-form-item>
        <a-form-item
          v-if="keyType === '秘钥内容'"
          label="微信商户证书私钥内容"
          name="merchantCertificatePrivateKey"
          :rules="[{ required: true, message: '请输入微信商户证书私钥内容' }]"
        >
          <a-textarea v-model:value="form.merchantCertificatePrivateKey!" />
        </a-form-item>
        <a-form-item
          v-if="keyType === '秘钥路径'"
          label="微信商户证书私钥文件路径"
          name="merchantCertificatePrivateKeyPath"
          :rules="[{ required: true, message: '请输入微信商户证书私钥文件路径' }]"
        >
          <a-input v-model:value="form.merchantCertificatePrivateKeyPath!" />
        </a-form-item>

        <a-form-item>
          <a-button type="primary" html-type="submit">保存</a-button>
        </a-form-item>
      </a-form>
    </a-spin>
  </div>
</template>

<script lang='ts' setup>
import * as api from '@/.generated/apis'
import { TenPayApiKeyEntity } from '@/.generated/models'
import { message } from 'ant-design-vue'

const keyType = ref('秘钥内容')

const [,spinner, form] = useLoading(api.SystemBaseInfo.GetTenPayAsync, { default: new TenPayApiKeyEntity(), immediate: true, callback(res) {
  if (res.merchantCertificatePrivateKeyPath) {
    keyType.value = '秘钥路径'
  }
} })

async function onFinish() {
  spinner.value = true
  await api.SystemBaseInfo.SaveTenPay_PostAsync(form.value!).then(() => {
    spinner.value = false
    message.success('保存成功')
  }).catch(() => {
    spinner.value = false
    message.error('保存失败')
  })
}
</script>

<style scoped>

</style>
