/**产品类型*/
export class ProductType {
  /**名称*/
  name?: string | null | undefined = null;
  /**描述*/
  description?: string | null | undefined = null;
  mainType?: string | null | undefined = null;
  brand?: string | null | undefined = null;
  /**排序序号*/
  sortOrder: number = 0;
  /**软删除标记*/
  softDeleted: boolean = false;
  /**软删除者*/
  deletedBy?: string | null | undefined = null;
  /**软删除时间*/
  deleted?: Dayjs | null | undefined = null;
  /**创建时间*/
  created: Dayjs = dayjs();
  /**修改时间*/
  modified?: Dayjs | null | undefined = null;
  /**创建者*/
  createdBy?: string | null | undefined = null;
  /**修改者*/
  modifiedBy?: string | null | undefined = null;
  /**主键*/
  id: GUID = "00000000-0000-0000-0000-000000000000";
}
