<template>
  <DefineTemplate>
    <a-card>
      <template #title>
        <div class="flex gap8px">
          <a-input-search v-model:value="code" addon-before="二维码编号" placeholder="请输入二维码编号或点击右侧二维码图标识别" enter-button @search="getData()" />
          <a-upload
            accept=".png,.PNG,.jpg,.JPG,.jpge,.JPGE,.jpeg,.JPEG"
            :show-upload-list="false"
            :before-upload="onScanCode"
          >
            <a-button type="primary" class="aspect-square center p0" title="点击识别二维码" ghost>
              <c-icon-qrcode-outlined class="text-24px" />
            </a-button>
          </a-upload>
        </div>
      </template>
      <a-spin :spinning="spinning" message="获取数据中">
        <a-row v-if="info" :gutter="16">
          <a-col :span="12">
            <a-card size="small" title="产品信息" class="h-full">
              <a-descriptions :column="1">
                <a-descriptions-item label="产品名称">
                  <a @click="() => openProductDetail = true">{{ info.productName }}</a>
                </a-descriptions-item>
                <a-descriptions-item label="产品类型">
                  {{ info.typeName }}
                </a-descriptions-item>
                <a-descriptions-item label="生产批次日期">
                  {{ dateTime(info.productBatchTime) }}
                </a-descriptions-item>
              </a-descriptions>
            </a-card>
          </a-col>
          <a-col :span="12">
            <a-card size="small" title="激活情况" class="h-full">
              <template #extra>
                <a-button size="small" type="primary" @click="() => openLog = true">
                  激活日志
                </a-button>
              </template>
              <span v-if="info.batchIsError" class="text-16px c-success">批次已过期</span>
              <span v-else-if="!info.batchIsError && !info.isActivation" class="text-16px c-success">{{ info.message ? info.message : "该二维码还未被激活" }}</span>
              <a-descriptions v-else :column="1">
                <a-descriptions-item label="激活时间">
                  {{ dateTime(info.activationTime) }}
                </a-descriptions-item>
                <a-descriptions-item label="激活人昵称">
                  {{ info.userName }}
                </a-descriptions-item>
                <a-descriptions-item label="激活人手机号码">
                  {{ info.userPhoneNumber }}
                </a-descriptions-item>
              </a-descriptions>
            </a-card>
          </a-col>
        </a-row>
      </a-spin>
    </a-card>
  </DefineTemplate>
  <c-modal v-if="isModal" v-model:open="open" :footer="null" title="核验二维码使用情况" width="800px"><ReuseTemplate /></c-modal>
  <ReuseTemplate v-else />
  <QRActivationLog v-model:open="openLog" :code="code" />
  <ProductInfo v-if="info" :id="info.productId" v-model:open="openProductDetail" :read-only="true" title="产品信息" />
</template>

<script setup lang="ts">
import * as api from '@/api'
import { ProductActivationLogViewModel } from '@/api/models'
import ProductInfo from '@/views/product-manager/components/ProductInfo.vue'
import { message } from 'ant-design-vue'
import jsQR from 'jsqr'

const { isModal = true, imgUrl, qrCode } = defineProps<{ isModal?: boolean, imgUrl?: string, qrCode?: string }>()

const open = defineModel<boolean>('open', { default: false })

const code = ref<string>('')

watch(open, () => {
  if (open.value) {
    if (imgUrl)
      distinguishQrByUrl(imgUrl)
    if (qrCode) {
      code.value = qrCode
      getData()
    }
  }
}, { immediate: true })

const [DefineTemplate, ReuseTemplate] = createReusableTemplate()

const info = ref<ProductActivationLogViewModel | null>(new ProductActivationLogViewModel())

const openProductDetail = ref(false)

const openLog = ref(false)

const spinning = ref(false)

function getData() {
  info.value = null
  if (!code.value)
    return
  spinning.value = true
  api.Products.FindProductActivationLogByQr_GetAsync({ qrnumber: code.value }).then(async (res) => {
    if (res) {
      info.value = res
    }
    else {
      info.value = null
      message.error('未找到该二维码的激活记录')
    }
    spinning.value = false
  }).catch(() => {
    spinning.value = false
  })
}

function getImageData(url: string) {
  return new Promise<ImageData>((resolve, reject) => {
    loadImage(url).then((src) => {
      if (!src)
        throw new Error('无法加载图像')
      const canvas = document.createElement('canvas')
      const context = canvas.getContext('2d')
      const img = new Image()
      img.onload = () => {
      // 设置画布大小
        canvas.width = img.width
        canvas.height = img.height
        // 将图像绘制到画布上
        context?.drawImage(img, 0, 0)
        // 获取图像数据
        const imageData = context?.getImageData(0, 0, canvas.width, canvas.height)
        canvas.width = 0
        canvas.height = 0
        if (imageData) {
          resolve(imageData)
        }
        else {
          reject(new Error('无法获取图像数据'))
        }
      }
      img.src = src
    }).catch(reject)
  })
}

async function getImageDataFromFile(file: File): Promise<ImageData> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = (e) => {
      getImageData(e.target?.result as string).then(resolve).catch(reject)
    }
    reader.onerror = error => reject(error)
    reader.readAsDataURL(file)
  })
}

function onScanCode(file: File) {
  spinning.value = true
  info.value = null
  getImageDataFromFile(file)
    .then(distinguishQr)
    .catch((error) => {
      spinning.value = false
      message.error(error.message)
    })
  return Promise.resolve(false)
}

function distinguishQr(imageData: ImageData) {
  const { width, height } = imageData
  const res = jsQR(imageData.data, width, height)
  if (res) {
    code.value = parseUrlParams(res.data)?.code
    if (!code.value)
      throw new Error('不是有效的二维码')
    getData()
  }
  else {
    throw new Error('无法识别二维码')
  }
}

function distinguishQrByUrl(url: string) {
  spinning.value = true
  info.value = null
  getImageData(url)
    .then(distinguishQr)
    .catch((error) => {
      message.error(error.message)
      spinning.value = false
    })
}
</script>

<style scoped>

</style>
