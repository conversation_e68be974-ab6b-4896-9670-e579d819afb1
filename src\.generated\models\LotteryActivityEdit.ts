import { LotteryActivityType } from "./LotteryActivityType";
export class LotteryActivityEdit {
  /**活动名称*/
  name?: string | null | undefined = null;
  /**图片（Path*/
  images?: string | null | undefined = null;
  /**开始时间（每日抽奖不需要设置）*/
  start?: Dayjs | null | undefined = null;
  /**结束时间（每日抽奖不需要设置）*/
  end?: Dayjs | null | undefined = null;
  /**描述*/
  description?: string | null | undefined = null;
  /**模板*/
  template?: string | null | undefined = null;
  /**保底次数：在此次数内用户必须中保底奖品*/
  guaranteedDraws: number = 0;
  /**抽奖类型*/
  type: LotteryActivityType = 0;
  /**该活动初始赠送的抽奖次数
（后台：需要用户点击查看抽奖活动触发）*/
  initialDrawAttempts: number = 0;
  /**主键*/
  id: GUID = "00000000-0000-0000-0000-000000000000";
}
