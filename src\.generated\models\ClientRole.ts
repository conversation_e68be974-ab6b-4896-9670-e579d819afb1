import { IdentityRole } from "./IdentityRole";
import { ClientUserRole } from "./ClientUserRole";
import { ClientUser } from "./ClientUser";
/**角色表*/
export class ClientRole extends IdentityRole<string> {
  userRoles?: ClientUserRole[] | null | undefined = [];
  users?: ClientUser[] | null | undefined = [];
  /**软删除标记*/
  softDeleted: boolean = false;
  /**软删除者*/
  deletedBy?: string | null | undefined = null;
  /**软删除时间*/
  deleted?: Dayjs | null | undefined = null;
  /**创建时间*/
  created: Dayjs = dayjs();
  /**修改时间*/
  modified?: Dayjs | null | undefined = null;
  /**创建者*/
  createdBy?: string | null | undefined = null;
  /**修改者*/
  modifiedBy?: string | null | undefined = null;
}
