// config/vite.config.dev.ts
import { mergeConfig } from "file:///E:/Company/project/yuqijun/YuQiJun.admin/node_modules/.pnpm/vite@5.0.10_@types+node@18.19.60_less@4.2.0/node_modules/vite/dist/node/index.js";
import VueRouter from "file:///E:/Company/project/yuqijun/YuQiJun.admin/node_modules/.pnpm/unplugin-vue-router@0.10.8_vue-router@4.4.5_vue@3.5.10/node_modules/unplugin-vue-router/dist/vite.js";
import vueDevTools from "file:///E:/Company/project/yuqijun/YuQiJun.admin/node_modules/.pnpm/vite-plugin-vue-devtools@7.5.4_vite@5.0.10_vue@3.5.10/node_modules/vite-plugin-vue-devtools/dist/vite.mjs";

// config/vite.config.base.ts
import { defineConfig } from "file:///E:/Company/project/yuqijun/YuQiJun.admin/node_modules/.pnpm/vite@5.0.10_@types+node@18.19.60_less@4.2.0/node_modules/vite/dist/node/index.js";
import vue from "file:///E:/Company/project/yuqijun/YuQiJun.admin/node_modules/.pnpm/@vitejs+plugin-vue@5.1.4_vite@5.0.10_vue@3.5.10/node_modules/@vitejs/plugin-vue/dist/index.mjs";
import vueJsx from "file:///E:/Company/project/yuqijun/YuQiJun.admin/node_modules/.pnpm/@vitejs+plugin-vue-jsx@3.1.0_vite@5.0.10_vue@3.5.10/node_modules/@vitejs/plugin-vue-jsx/dist/index.mjs";
import { createHtmlPlugin } from "file:///E:/Company/project/yuqijun/YuQiJun.admin/node_modules/.pnpm/vite-plugin-html@3.2.1_vite@5.0.10/node_modules/vite-plugin-html/dist/index.mjs";
import ViteComponents from "file:///E:/Company/project/yuqijun/YuQiJun.admin/node_modules/.pnpm/unplugin-vue-components@0.27.3_vue@3.5.10/node_modules/unplugin-vue-components/dist/vite.js";
import { AntDesignVueResolver } from "file:///E:/Company/project/yuqijun/YuQiJun.admin/node_modules/.pnpm/unplugin-vue-components@0.27.3_vue@3.5.10/node_modules/unplugin-vue-components/dist/resolvers.js";
import { Ch2ComponentResolver } from "file:///E:/Company/project/yuqijun/YuQiJun.admin/node_modules/.pnpm/ch2-components@3.4.46/node_modules/ch2-components/Ch2ComponentResolver.js";
import Unocss from "file:///E:/Company/project/yuqijun/YuQiJun.admin/node_modules/.pnpm/unocss@0.58.9_postcss@8.4.47_vite@5.0.10/node_modules/unocss/dist/vite.mjs";
import AutoImport from "file:///E:/Company/project/yuqijun/YuQiJun.admin/node_modules/.pnpm/unplugin-auto-import@0.18.3_@vueuse+core@10.11.1/node_modules/unplugin-auto-import/dist/vite.js";

// src/theme/utils.ts
import { theme } from "file:///E:/Company/project/yuqijun/YuQiJun.admin/node_modules/.pnpm/ant-design-vue@4.1.2_vue@3.5.10/node_modules/ant-design-vue/lib/index.js";
import dayjs from "file:///E:/Company/project/yuqijun/YuQiJun.admin/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js";
import { kebabCase } from "file:///E:/Company/project/yuqijun/YuQiJun.admin/node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/lodash.js";
var { defaultAlgorithm, defaultSeed } = theme;
var mapToken = defaultAlgorithm(defaultSeed);
function getThemeAttrKey(key) {
  return `--ch2-${kebabCase(key)}`;
}
var THEME_KEY = `${dayjs().valueOf()}-dynamic-theme`;
function getLessModifyVars() {
  const data = Object.keys(mapToken).reduce((a, key) => {
    a[key] = `var(${getThemeAttrKey(key)})`;
    return a;
  }, {});
  return data;
}

// config/vite.config.base.ts
var vite_config_base_default = defineConfig({
  server: {
    hmr: {
      overlay: false
    },
    host: "0.0.0.0"
  },
  plugins: [
    vue(),
    vueJsx(),
    createHtmlPlugin({
      viteNext: true
    }),
    Unocss(),
    ViteComponents({
      dts: "src/components.d.ts",
      resolvers: [
        (componentName) => {
          if (componentName.startsWith("CIcon"))
            return { name: componentName.slice(5), from: "@ant-design/icons-vue" };
        },
        AntDesignVueResolver({ importStyle: false }),
        Ch2ComponentResolver()
      ]
    }),
    AutoImport({
      imports: ["vue", "@vueuse/core"],
      dts: "src/auto-imports.d.ts",
      dirs: ["src/composables", "src/stores", "src/utils", "src/hooks"],
      vueTemplate: true
    })
  ],
  resolve: {
    alias: [
      {
        find: "@/",
        replacement: "/src/"
      },
      {
        find: "vue-i18n",
        replacement: "vue-i18n/dist/vue-i18n.cjs.js"
      }
    ],
    extensions: [".mjs", ".js", ".ts", ".jsx", ".tsx", ".json", ".vue"]
  },
  css: {
    preprocessorOptions: {
      less: {
        modifyVars: getLessModifyVars(),
        javascriptEnabled: true
      }
    }
  }
});

// config/utils/proxy.ts
function proxy(env2) {
  const proxyTarget2 = env2.VITE_APP_PROXY_TARGET;
  return {
    "/api/": {
      target: proxyTarget2,
      changeOrigin: true,
      secure: false
    },
    "/connect/": {
      target: proxyTarget2,
      changeOrigin: true,
      secure: false
    },
    "/files/": {
      target: proxyTarget2,
      changeOrigin: true,
      secure: false
    },
    "/fileExplorer/": {
      target: proxyTarget2,
      changeOrigin: true,
      secure: false
    },
    "/uploaded-images/": {
      target: proxyTarget2,
      changeOrigin: true,
      secure: false
    },
    "/msgHub/": {
      target: proxyTarget2,
      changeOrigin: true,
      secure: false
    },
    "/api/msgHub/": {
      target: proxyTarget2,
      changeOrigin: true,
      secure: false
    }
  };
}
var proxy_default = proxy;

// config/utils/getEnv.ts
import process2 from "node:process";
import { loadEnv } from "file:///E:/Company/project/yuqijun/YuQiJun.admin/node_modules/.pnpm/vite@5.0.10_@types+node@18.19.60_less@4.2.0/node_modules/vite/dist/node/index.js";
function getEnv(mode2) {
  const root = process2.cwd();
  const env2 = loadEnv(mode2, root);
  return env2;
}

// config/utils/useFileLayoutRouter.ts
var code = `
import { routes as _allRoutes } from 'vue-router/auto-routes';

function getLocalRoutes(routes) {
  function findLocalBranches(node) {
    return node?.flatMap(item => {
      const res = {
        ...item,
        children: item.meta?.local ? item.children || [] : findLocalBranches(item.children || [])
      };
      if ((res.meta?.local || res.children?.length) && res.meta?.authorize?.length === 0) return [res]
      return [];
    });
  }
  return findLocalBranches(routes);
}

const layouts = Object.entries(import.meta.glob('/src/layouts/*.vue')).map(([key, modules]) => {
  const p = key.split('/');
  return [p[p.length - 1]?.split('.')[0] || '', modules];
}).reduce((a, b) => {
  a[b[0]] = b[1];
  return a;
}, {});

function setupLayouts2(routes) {
  const defaultLayout = 'default' in layouts ? 'default' : false;
  const rootRoutes = [];

  function push(res, route) {
    res.push(route);
  }

  function filterChildNoLayout(child) {
    const root = child?.filter(v => v.meta?.layout === false);
    if (root) {
      root.forEach(item => {
        if (item.meta?.fullPath) item.path = item.meta?.fullPath;
        rootRoutes.push(item);
      });
    }
    return child?.filter(v => v.meta?.layout !== false);
  }

  function setLayout(routes, isLayout = true, parentPath = '') {
    const res = []
    for (const route of routes) {
      const fullPaths = []
      
      if (parentPath)
        fullPaths.push(parentPath)

      fullPaths.push(route.path)

      const fullPath = fullPaths.join('/')

      let tempRoute = { ...route, meta: { ...route.meta, fullPath } }

      const layoutIndex = tempRoute.children?.findIndex(v => v.meta?.isLayout || v.meta?.layoutRoute) ?? -1

      if (layoutIndex > -1) {
        const t = tempRoute.children[layoutIndex]

        const isFather = t.meta?.layoutRoute?.meta?.layout

        // \u751F\u6210\u4E00\u4E2A\u65B0\u7684\u7236\u7EA7\u5D4C\u5957
        const layout = t.meta?.layoutRoute?.meta?.layout ?? defaultLayout

        // isLayout\u4E0D\u66FF\u6362\u65F6\u624D\u4EA7\u751F\u65B0\u7684\u8DEF\u7531\uFF0C\u4EA7\u751F\u7684\u8DEF\u7531\u5FC5\u987B\u8981\u6709\u5E03\u5C40\u6587\u4EF6\uFF0C\u5426\u5219\u65E0\u6CD5\u6E32\u67D3
        if (isFather && layout && !t.meta?.layoutRoute?.meta?.isLayout) {
          tempRoute = {
            ...t.meta?.layoutRoute,
            isLayout: true,
            component: layouts[layout],
            meta: { ...t.meta?.layoutRoute?.meta, fullPath, layoutRoute: undefined, layout, isLayout: true },
            path: tempRoute.path,
            children: tempRoute.children || [],
          } 
        }
        else {
          tempRoute.children?.splice(layoutIndex, 1)
          tempRoute = {
            ...t,
            meta: { ...t.meta, fullPath },
            path: tempRoute.path,
            name: tempRoute.name,
            children: tempRoute.children || [],
          }
        }
      }

      const meta = tempRoute.meta || {}

      const layout = meta?.layout ?? defaultLayout

      if (meta?.layout === false) {
        push(res, tempRoute)
      }
      else if (layout && isLayout) {
        if (meta.isLayout) {
          tempRoute.component = layouts[layout]
          push(res, tempRoute)
        }

        else {
          const layoutRoute = {
            ...meta.layoutRoute,
            meta: { ...meta.layoutRoute?.meta, isLayout: true, fullPath },
            component: layouts[layout],
            children: [tempRoute],
          } 

          if (!layoutRoute.path) {
            layoutRoute.path = tempRoute.path
            tempRoute.path = ''
            tempRoute.meta.fullPath = layoutRoute.path + '/'

          }

          push(res, layoutRoute)
        }
      }
      else {
        push(res, tempRoute)
      }

      if (tempRoute.children)
        tempRoute.children = filterChildNoLayout(setLayout(tempRoute.children, false, fullPath))
    }
    return res
  }

  const result = setLayout(routes);
  return [...result, ...rootRoutes];
}

function flattenTree(tree) {
  let flatArray = [];
  tree.forEach(node => {
    flatArray.push(node);
    if (node.children) {
      flatArray = flatArray.concat(flattenTree(node.children));
    }
  });
  return flatArray;
}

export function useFileRouter() {
  const allRoutes = setupLayouts2(_allRoutes);
  const localRoutes = getLocalRoutes(allRoutes);
  const allRoutesNamedMap = flattenTree(allRoutes).filter(p => p.name).reduce((acc, curr) => {
    acc[curr.name] = curr;
    return acc;
  }, {});
  const localRoutesNamedMap = flattenTree(localRoutes).filter(p => p.name).reduce((acc, curr) => {
    acc[curr.name] = curr;
    return acc;
  }, {});
  return {
    localRoutes,
    allRoutesNamedMap,
    localRoutesNamedMap,
    allRoutes
  };
}
`;
function virtualModulePlugin() {
  const virtualModuleId = "virtual:file-layout-router-module";
  return {
    name: "file-layout-router-module",
    resolveId(id) {
      if (id === virtualModuleId)
        return id;
    },
    load(id) {
      if (id === virtualModuleId)
        return code;
    }
  };
}

// config/utils/preventFullReloadPlugin.ts
function preventFullReloadPlugin() {
  return {
    name: "vite-plugin-prevent-full-reload",
    configureServer(server) {
      const originalSend = server.ws.send;
      server.ws.send = function(payload, ...args) {
        if (payload.type === "full-reload") {
          return;
        }
        return originalSend.call(this, payload, ...args);
      };
    }
  };
}

// config/utils/global-directives-types.ts
import fs from "node:fs";
import { exec } from "node:child_process";
import path from "node:path";
function readDirectory({ dir, out }) {
  fs.readdir(dir, (err, files) => {
    if (err)
      return;
    const target = files.filter((v) => v !== "index.ts");
    let str = `export { }
 declare module 'vue' {
    export interface ComponentCustomProperties {
 `;
    target.forEach((name) => {
      const nat = name.split(".")[0];
      str += `  v${nat.charAt(0).toUpperCase() + nat.slice(1)}: typeof import('./directives/${name}')['default'] 
`;
    });
    str += `    }
}`;
    fs.writeFileSync(out, str);
    exec(`eslint ${out} --fix`);
  });
}
function watchDirectory(options) {
  fs.watch(options.dir, (eventType, filename) => {
    if (filename)
      readDirectory(options);
  });
}
function globalDirectivesTypes(options) {
  return {
    name: "global-directives-types",
    buildStart() {
      options.dir = path.resolve(process.cwd(), options.dir);
      options.out = path.resolve(process.cwd(), options.out, "global-directives-types.d.ts");
      readDirectory(options);
      watchDirectory(options);
    }
  };
}

// config/vite.config.dev.ts
var mode = "dev";
var env = getEnv(mode);
var proxyTarget = env.VITE_APP_PROXY_TARGET;
var vite_config_dev_default = mergeConfig(
  {
    mode,
    server: {
      open: true,
      fs: {
        strict: true
      },
      proxy: proxyTarget ? proxy_default(env) : void 0
    },
    plugins: [
      preventFullReloadPlugin(),
      globalDirectivesTypes({ dir: "src/directives", out: "src" }),
      // https://uvr.esm.is/guide/file-based-routing.html
      VueRouter({
        /* options */
        dts: "src/typed-router.d.ts",
        routesFolder: {
          src: "src/views"
        },
        exclude: ["**/components/*.vue", "components/**/*.vue", "**/*/components/**/*.vue"],
        watch: false
      }),
      virtualModulePlugin(),
      vueDevTools({
        launchEditor: "code"
      })
    ],
    optimizeDeps: {
      exclude: ["ch2-components"]
    }
  },
  vite_config_base_default
);
export {
  vite_config_dev_default as default
};
//# sourceMappingURL=data:application/json;base64,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
