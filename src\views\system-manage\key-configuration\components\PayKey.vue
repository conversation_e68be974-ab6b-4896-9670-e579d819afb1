<template>
  <div class="h-full p-16px c-bg">
    <a-spin :spinning="spinner">
      <a-alert
        type="warning"
        :message="`${state ? '已设置，请妥善保管密码保证您的账户安全' : '未设置支付密码请及时设置，否则无法通过后台发红包！'}`"
        class="mb-16px"
      />
      <a-form
        :model="form"
        name="basic"
        autocomplete="off"
        layout="vertical"
        @finish="onFinish"
      >
        <a-form-item
          label="红包密码"
          name="key"
          :rules="[{ required: true, message: '请输入红包支付密码' }]"
        >
          <a-input-password v-model:value="form.key" />
        </a-form-item>
        <a-form-item>
          <a-button type="primary" html-type="submit">保存</a-button>
        </a-form-item>
      </a-form>
    </a-spin>
  </div>
</template>

<script setup lang="ts">
import * as api from '@/.generated/apis'
import { message } from 'ant-design-vue'

const form = reactive({
  key: '',
})

const state = ref(false)

onMounted(async () => {
  const res = await api.SystemBaseInfo.VerifyHasPassword_GetAsync()
  state.value = res
})

const spinner = ref(false)

async function onFinish() {
  spinner.value = true
  api.SystemBaseInfo.UpdateTenPayKey_GetAsync({ newKey: form.key }).then(() => {
    spinner.value = false

    message.success('保存成功, 请妥善保管密码保证您的账户安全')
  }).catch(() => {
    spinner.value = false
    message.error('保存失败')
  })
}
</script>

<style scoped>

</style>
