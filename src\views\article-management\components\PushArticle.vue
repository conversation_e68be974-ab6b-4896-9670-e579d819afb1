<!-- eslint-disable promise/param-names -->
<!-- eslint-disable unused-imports/no-unused-vars -->
<template>
  <a-spin :spinning="loading">
    <a-card :active-tab-key="tabActiveKey" :tab-list="[{ key: 'base', tab: '基本信息' }, { key: 'images', tab: '横幅图' }]" @tab-change="(key:string) => tabActiveKey = key">
      <template #tabBarExtraContent>
        <a-button title="将当前文章发布，并且清空当前界面" size="small" type="primary" @click="onPublishArticle()">
          保存发布
        </a-button>
        <a-divider type="vertical" />
        <a-button size="small" title="将当前文章保存为草稿，并且清空当前界面" type="primary" @click="onSave(true)">
          保存并关闭
        </a-button>
        <a-divider type="vertical" />
        <a-button size="small" type="primary" @click="onSave()">
          {{ Guid.isNull(form.informationId) ? "创建" : "保存" }}
        </a-button>
      </template>
      <c-form v-show="tabActiveKey === 'base'" ref="formRef" :label-col="{ xs: { span: 3 }, md: { span: 4 } }" :model="form" :rules="rules">
        <c-form-item has-feedback label="作者" name="authorNames" v-bind="validateInfos.authorNames">
          <c-input v-model:value="form.authorNames!" placeholder="请输入作者" allow-clear />
        </c-form-item>
        <c-form-item has-feedback label="简述" name="description" v-bind="validateInfos.description">
          <c-textarea v-model:value="form.description!" :maxlength="50" placeholder="请输入简述" allow-clear show-count />
        </c-form-item>
        <c-form-item has-feedback label="分类" name="categoryId" v-bind="validateInfos.categoryId">
          <a-tree-select
            v-model:value="form.categoryId"
            style="width: 100%"
            :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
            placeholder="请选择"
            allow-clear
            :tree-data="treeData"
            :field-names="{ children: 'children', label: 'name', value: 'id' }"
            show-checked-strategy="SHOW_ALL"
            show-search
            multiple
            tree-node-filter-prop="name"
          />
        </c-form-item>
        <c-form-item has-feedback label="标签" name="tags">
          <c-input v-model:value="form.tags!" placeholder="标签" allow-clear />
        </c-form-item>
        <c-form-item has-feedback label="url" name="url">
          <a-input v-model:value="form.url!" placeholder="请输入url">
            <template #suffix>
              <a-tooltip title="填写该url则详情页为该url，填写的正文将不会展示">
                <c-icon-info-circle-outlined style="color: rgba(0, 0, 0, 0.45)" />
              </a-tooltip>
            </template>
          </a-input>
        </c-form-item>
        <c-form-item has-feedback label="附件" name="enclosure">
          <a-button type="primary" ghost @click="addAttachments">
            添加附件
          </a-button>
          <div v-for="(item, index) in form.enclosureObj" :key="index" class="mt-10px">
            <a> {{ item.des }} </a><c-icon-delete-outlined class="ml-6px cursor-pointer c-#ff0000" style="color: #ff0000" @click="attachmentDeletion(item.id!)" />
          </div>
        </c-form-item>
        <c-form-item has-feedback label="封面" name="cover" v-bind="validateInfos.cover">
          <div v-if="form.cover" class="size-100px overflow-hidden">
            <ImageView class="size-100px object-cover" :src="(form.cover)" alt="avatar" :preview="true" :del-ico="true" @del-image="() => form.cover = null" />
          </div>
          <a-button v-else type="dashed" block style="width: 100px; height: 100px" @click="avatarUpload">
            <template #icon>
              <c-icon-plus-outlined />
            </template>
            上传
          </a-button>
        </c-form-item>
      </c-form>
      <div v-show="tabActiveKey === 'images'" class="flex flex-wrap">
        <div v-for="(item, index) in form.infoImages" :key="index" class="coverBox mb-10px mr-10px size-100px overflow-hidden">
          <ImageView class="size-100px object-cover" :src="(item.id!)" alt="avatar" :preview="true" :del-ico="true" @del-image="bannerDel(item.path!)" />
        </div>
        <a-button type="dashed" block style="width: 100px; height: 100px" @click="bannerUpload">
          <template #icon>
            <c-icon-plus-outlined />
          </template>
          上传
        </a-button>
      </div>
    </a-card>
  </a-spin>
</template>

<script lang="ts" setup>
import * as api from '@/api'
import * as models from '@/api/models'
import { FileType } from '@/api/models'
import { useFileMangerModal } from '@/hooks/useFileMangerModal'
import { deepCopy } from '@/utils/util'
import { DatePicker, Form, message, Modal } from 'ant-design-vue'
import { onMounted, reactive, ref, watch } from 'vue'

const props = defineProps<{ formData: models.Information }>()

const emits = defineEmits<{
  'update:formData': [models.Information]
  'clar': []
}>()

const formRef = useTemplateRef('formRef')// form表单ref

const loading = ref<boolean>(false)

const form = ref<models.InformationEditModel>(new models.InformationEditModel()) // 表单

watch(() => props.formData, (v) => {
  if (!v)
    return
  form.value = deepCopy({ ...v, categoryId: v.informationCategory?.map(v => v.id), enclosure: [] })
}, { immediate: true })

function avatarUpload() {
  useFileMangerModal((files) => {
    form.value.cover = files[0]?.id
  }, { multiple: false, immediateReturn: true, menu: [FileType.图片] })
}

function bannerUpload() {
  useFileMangerModal((files) => {
    files.forEach((item) => {
      if (!form.value.infoImages?.some(p => item.path === p.path)) {
        form.value.infoImages?.push({
          des: item.originalName,
          length: item.length,
          id: item.id,
          path: item.path,
        })
      }
    })
  }, { immediateReturn: true, menu: [FileType.图片] })
}

function bannerDel(path: string) {
  form.value.infoImages = form.value.infoImages?.filter(item => item.path !== path)
}

/** 表单验证规则 */
const rules = reactive({
  authorNames: [{ required: true, message: '请输入!', trigger: 'change' }],
  categoryId: [{ required: true, message: '请输入!', trigger: 'change' }],
  description: [{ required: true, message: '请输入!', trigger: 'change' }],
})

const { validate, validateInfos, resetFields } = Form.useForm(form, rules)

async function onSave(reset = false) {
  await validate()
    .then(async () => {
      try {
        form.value.title = props.formData.title
        if (!form.value.title)
          return message.error('请填写标题')
        loading.value = true

        form.value.content = props.formData.content
        if (form.value.cover === null)
          form.value.cover = ''
        if (Guid.isNull(form.value.informationId)) {
          const res = await api.Informations.CreateInformation_PostAsync(form.value)
          form.value.informationId = res.informationId
          emits('update:formData', res)
          message.success('文章添加成功！')
        }
        else {
          const res = await api.Informations.ModifyInformation_PostAsync(form.value)
          emits('update:formData', res)
          message.success('文章修改成功！')
        }
        loading.value = false
        if (reset) {
          form.value = new models.InformationEditModel()
          resetFields()
          emits('update:formData', new models.Information())
          emits('clar')
        }
      }
      catch (error: any) {
        message.error(`资讯信息更新失败：${error.message}`)
        loading.value = false
      }
    })
    .catch((val) => {
      console.log('%c [ val ]-92', 'font-size:13px; background:pink; color:#bf2c9f;', val)
    })
}

function onPublishArticle() {
  let time = new Date()
  Modal.confirm({
    title: '请选择发布时间',
    content: h(DatePicker, {
      showTime: true,
      placeholder: '请选择发布时间',
      onChange(date: any) {
        time = date.toDate()
      },
    }),
    onOk: async () => {
      try {
        await onSave()
        await api.Informations.SetStatusByInformationId_PostAsync({ informationId: form.value.informationId!, status: models.InformationStatus.已发布, updateTime: time })
        form.value = new models.InformationEditModel()
        resetFields()
        emits('update:formData', new models.Information())
        emits('clar')
        message.success('发布成功，并且刷新当前窗口')
      }
      catch (error: any) {
        message.error(error.message)
      }
    },
  })
}

const treeData = ref<models.InformationCategoryView[]>([])

async function getOptions() {
  treeData.value = await api.InformationCategories.GeCategoryPaged_GetAsync({})
}

const tabActiveKey = ref('base')

function addAttachments() {
  !form.value.enclosureObj && (form.value.enclosureObj = [])
  useFileMangerModal((files) => {
    files.forEach((item) => {
      if (!form.value.enclosure?.some(p => item.id === p)) {
        form.value.enclosure?.push(item.id)
        form.value.enclosureObj?.push({
          des: item.originalName,
          length: item.length,
          id: item.id,
          path: item.path,
        })
      }
    })
  }, { fileType: FileType.文档 })
}

function attachmentDeletion(id: string) {
  form.value.enclosure = form.value.enclosure?.filter(item => item !== id)
  form.value.enclosureObj = form.value.enclosureObj?.filter(item => item.id !== id)
}

onMounted(() => getOptions())

defineExpose({ onSave })
</script>

<style scoped lang="less">
.coverBox {
  position: relative;
  width: 200px;
  height: 200px;

  .icon {
    position: absolute;
    right: 0;
    top: 0;
    background-color: #ccc;
    width: 20px;
    height: 20px;
    line-height: 20px;
    cursor: pointer;
  }
}
.push-article {
  background-color: #fff;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.12);
  padding: 10px;
  border-radius: 4px;
  margin-right: 20px;
}
.avatar-uploade {
  :deep(.ant-upload) {
    width: 200px;
    height: 192px;
  }
}

:deep(.ant-image) {
  width: 100%;
  height: 100%;
}
</style>
