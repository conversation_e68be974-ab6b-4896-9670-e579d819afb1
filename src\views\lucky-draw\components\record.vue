<!--
 * @Description:
 * @Author: 景 彡
 * @Date: 2024-10-30 10:28:55
 * @LastEditors: 景 彡
-->
<template>
  <c-modal v-model:open="recordRef.visible" width="90%" :title="recordRef.title">
    <c-pro-table :columns="columns" :data-source="record" serial-number bordered row-key="id">
      <template #bodyCell="{ column, text, record }">
        <template v-if="column.dataIndex === 'name'">
          <a>{{ text }}</a>
        </template>
        <template v-if="column.dataIndex === 'images'">
          <ImageView
            :src="(record.images)" alt="avatar" :preview="true" :del-ico="true"
            style="height: 60px; width:60px ; object-fit:cover"
          />
        </template>
        <template v-if="column.dataIndex === 'operation'">
          <a @click="removePrizes(record.id)">删除</a>
        </template>
      </template>
    </c-pro-table>
  </c-modal>
</template>

<script lang='ts' setup>
import type { LotteryResultPageView } from '@/api/models'
import type { ColumnProps } from 'ch2-components/types/pro-table/types'
import * as api from '@/.generated/apis'
import { LotteryResultStatus } from '@/api/models'
import { message } from 'ant-design-vue'

const recordRef = ref({
  visible: false,
  title: '',
  currentId: '0',
  type: '',
  callback: () => { },
})

const record = ref<LotteryResultPageView[]>([])

const columns = reactive<ColumnProps<LotteryResultPageView>[]>(
  [
    {
      title: '姓名',
      dataIndex: 'name',
      align: 'center',
    },
    {
      title: '手机号',
      dataIndex: 'phoneNumber',
      align: 'center',
      width: '100px',
    },
    {
      title: '奖品名称',
      dataIndex: 'prizesName',
      align: 'center',
    },
    {
      title: '图片',
      dataIndex: 'images',
      align: 'center',
      width: '80px',
    },
    {
      title: '中奖时间',
      dataIndex: 'time',
      align: 'center',
      dateFormat: true,
    },

    {
      title: '状态',
      dataIndex: 'status',
      align: 'center',
      enum: LotteryResultStatus,
    },
  ],
)

async function removePrizes(id: string) {
  await api.Lotterys.RemovePrizeByActiyityId_GetAsync({ actiyityId: recordRef.value.currentId, prizesId: id }).then((res) => {
    if (res) {
      message.success('移除成功')
      getData(recordRef.value.currentId)
    }
  })
}

/**
 *  父组件传值
 * @param params 参数
 */
async function acceptParams(params: typeof recordRef.value): Promise<void> {
  recordRef.value = params
  recordRef.value.visible = params.visible
  if (params.currentId) {
    getData(params.currentId)
  }
}

async function getData(id: string) {
  await api.Lotterys.LotteryResultPage_GetAsync({ actiyityId: id }).then((res) => {
    if (res) {
      record.value = res.items as LotteryResultPageView[]
    }
  })
}

defineExpose({
  acceptParams,
})
</script>

  <style scoped></style>
