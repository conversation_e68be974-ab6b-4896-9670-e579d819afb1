import { InfoImage } from "./InfoImage";
/**文章编辑模型*/
export class InformationEditModel {
  /**文章访问Id*/
  informationId?: GUID = null;
  /**标题*/
  title?: string | null | undefined = null;
  /**内容*/
  content?: string | null | undefined = null;
  /**简述*/
  description?: string | null | undefined = null;
  /**填写作者名*/
  authorNames?: string | null | undefined = null;
  /**分类Id*/
  categoryId?: string[] | null | undefined = [];
  /**封面（相对路径的url）*/
  cover?: string | null | undefined = null;
  /**版本*/
  version: number = 0;
  /**是否为自动生成的简述*/
  autoDescription: boolean = false;
  /**url,填写改url则详情页为该url*/
  url?: string | null | undefined = null;
  /**标签，以,分割*/
  tags?: string | null | undefined = null;
  /**附件*/
  enclosure?: string[] | null | undefined = [];
  /**文章内的头部图片*/
  infoImages?: InfoImage[] | null | undefined = [];
  /**附件*/
  enclosureObj?: InfoImage[] | null | undefined = [];
}
